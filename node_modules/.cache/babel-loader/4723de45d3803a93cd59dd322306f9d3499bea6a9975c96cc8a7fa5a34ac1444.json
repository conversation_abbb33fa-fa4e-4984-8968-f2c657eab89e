{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/ADFD_Tracking_System/src/components/Layout/Header.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Menu, X, Shield, Zap } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = () => {\n  _s();\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n  const navItems = [{\n    name: 'Features',\n    href: '#features',\n    icon: Zap\n  }, {\n    name: 'Security',\n    href: '#security',\n    icon: Shield\n  }];\n  return /*#__PURE__*/_jsxDEV(motion.header, {\n    initial: {\n      y: -100\n    },\n    animate: {\n      y: 0\n    },\n    transition: {\n      duration: 0.8,\n      ease: 'easeOut'\n    },\n    className: `fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${isScrolled ? 'glass-effect shadow-lg' : 'bg-transparent'}`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-custom\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between h-16 lg:h-20\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          whileHover: {\n            scale: 1.05\n          },\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-10 h-10 bg-gradient-to-r from-primary-600 to-gold-500 rounded-lg flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(Shield, {\n              className: \"w-6 h-6 text-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-xl font-bold text-gray-900\",\n              children: \"FinTrack\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Request System\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 43,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n          className: \"hidden lg:flex items-center space-x-8\",\n          children: navItems.map((item, index) => /*#__PURE__*/_jsxDEV(motion.a, {\n            href: item.href,\n            initial: {\n              opacity: 0,\n              y: -20\n            },\n            animate: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: index * 0.1 + 0.3\n            },\n            whileHover: {\n              scale: 1.05\n            },\n            className: \"flex items-center space-x-2 text-navy-700 hover:text-primary-600 transition-colors duration-300 font-medium\",\n            children: [/*#__PURE__*/_jsxDEV(item.icon, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 61,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: item.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 17\n            }, this)]\n          }, item.name, true, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden lg:flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(motion.button, {\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            className: \"text-navy-700 hover:text-primary-600 font-medium transition-colors duration-300\",\n            children: \"Sign In\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            className: \"btn-primary\",\n            children: \"Get Started\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 68,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n          whileTap: {\n            scale: 0.95\n          },\n          onClick: () => setIsMenuOpen(!isMenuOpen),\n          className: \"lg:hidden p-2 text-navy-700 hover:text-primary-600 transition-colors duration-300\",\n          children: isMenuOpen ? /*#__PURE__*/_jsxDEV(X, {\n            className: \"w-6 h-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 27\n          }, this) : /*#__PURE__*/_jsxDEV(Menu, {\n            className: \"w-6 h-6\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 55\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: false,\n        animate: {\n          height: isMenuOpen ? 'auto' : 0\n        },\n        className: \"lg:hidden overflow-hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"py-4 space-y-4\",\n          children: [navItems.map(item => /*#__PURE__*/_jsxDEV(\"a\", {\n            href: item.href,\n            onClick: () => setIsMenuOpen(false),\n            className: \"flex items-center space-x-3 text-navy-700 hover:text-primary-600 transition-colors duration-300 font-medium\",\n            children: [/*#__PURE__*/_jsxDEV(item.icon, {\n              className: \"w-4 h-4\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 109,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              children: item.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 17\n            }, this)]\n          }, item.name, true, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 15\n          }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"pt-4 space-y-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"w-full text-left text-navy-700 hover:text-primary-600 font-medium transition-colors duration-300\",\n              children: \"Sign In\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"w-full btn-primary text-center\",\n              children: \"Get Started\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 96,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 33,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 23,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"wcf3U8/NDcncqNPQTEEGYFGEme8=\");\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "motion", "<PERSON><PERSON>", "X", "Shield", "Zap", "jsxDEV", "_jsxDEV", "Header", "_s", "isMenuOpen", "setIsMenuOpen", "isScrolled", "setIsScrolled", "handleScroll", "window", "scrollY", "addEventListener", "removeEventListener", "navItems", "name", "href", "icon", "header", "initial", "y", "animate", "transition", "duration", "ease", "className", "children", "div", "whileHover", "scale", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "index", "a", "opacity", "delay", "button", "whileTap", "onClick", "height", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/ADFD_Tracking_System/src/components/Layout/Header.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { Menu, X, Shield, Zap } from 'lucide-react';\n\nconst Header: React.FC = () => {\n  const [isMenuOpen, setIsMenuOpen] = useState(false);\n  const [isScrolled, setIsScrolled] = useState(false);\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n    window.addEventListener('scroll', handleScroll);\n    return () => window.removeEventListener('scroll', handleScroll);\n  }, []);\n\n  const navItems = [\n    { name: 'Features', href: '#features', icon: Zap },\n    { name: 'Security', href: '#security', icon: Shield },\n  ];\n\n  return (\n    <motion.header\n      initial={{ y: -100 }}\n      animate={{ y: 0 }}\n      transition={{ duration: 0.8, ease: 'easeOut' }}\n      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${\n        isScrolled \n          ? 'glass-effect shadow-lg' \n          : 'bg-transparent'\n      }`}\n    >\n      <div className=\"container-custom\">\n        <div className=\"flex items-center justify-between h-16 lg:h-20\">\n          {/* Logo */}\n          <motion.div\n            whileHover={{ scale: 1.05 }}\n            className=\"flex items-center space-x-3\"\n          >\n            <div className=\"w-10 h-10 bg-gradient-to-r from-primary-600 to-gold-500 rounded-lg flex items-center justify-center\">\n              <Shield className=\"w-6 h-6 text-white\" />\n            </div>\n            <div>\n              <h1 className=\"text-xl font-bold text-gray-900\">FinTrack</h1>\n              <p className=\"text-sm text-gray-600\">Request System</p>\n            </div>\n          </motion.div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden lg:flex items-center space-x-8\">\n            {navItems.map((item, index) => (\n              <motion.a\n                key={item.name}\n                href={item.href}\n                initial={{ opacity: 0, y: -20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: index * 0.1 + 0.3 }}\n                whileHover={{ scale: 1.05 }}\n                className=\"flex items-center space-x-2 text-navy-700 hover:text-primary-600 transition-colors duration-300 font-medium\"\n              >\n                <item.icon className=\"w-4 h-4\" />\n                <span>{item.name}</span>\n              </motion.a>\n            ))}\n          </nav>\n\n          {/* CTA Buttons */}\n          <div className=\"hidden lg:flex items-center space-x-4\">\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"text-navy-700 hover:text-primary-600 font-medium transition-colors duration-300\"\n            >\n              Sign In\n            </motion.button>\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"btn-primary\"\n            >\n              Get Started\n            </motion.button>\n          </div>\n\n          {/* Mobile Menu Button */}\n          <motion.button\n            whileTap={{ scale: 0.95 }}\n            onClick={() => setIsMenuOpen(!isMenuOpen)}\n            className=\"lg:hidden p-2 text-navy-700 hover:text-primary-600 transition-colors duration-300\"\n          >\n            {isMenuOpen ? <X className=\"w-6 h-6\" /> : <Menu className=\"w-6 h-6\" />}\n          </motion.button>\n        </div>\n\n        {/* Mobile Menu */}\n        <motion.div\n          initial={false}\n          animate={{ height: isMenuOpen ? 'auto' : 0 }}\n          className=\"lg:hidden overflow-hidden\"\n        >\n          <div className=\"py-4 space-y-4\">\n            {navItems.map((item) => (\n              <a\n                key={item.name}\n                href={item.href}\n                onClick={() => setIsMenuOpen(false)}\n                className=\"flex items-center space-x-3 text-navy-700 hover:text-primary-600 transition-colors duration-300 font-medium\"\n              >\n                <item.icon className=\"w-4 h-4\" />\n                <span>{item.name}</span>\n              </a>\n            ))}\n            <div className=\"pt-4 space-y-3\">\n              <button className=\"w-full text-left text-navy-700 hover:text-primary-600 font-medium transition-colors duration-300\">\n                Sign In\n              </button>\n              <button className=\"w-full btn-primary text-center\">\n                Get Started\n              </button>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </motion.header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,IAAI,EAAEC,CAAC,EAAEC,MAAM,EAAEC,GAAG,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEpD,MAAMC,MAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM,CAACa,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,KAAK,CAAC;EAEnDC,SAAS,CAAC,MAAM;IACd,MAAMc,YAAY,GAAGA,CAAA,KAAM;MACzBD,aAAa,CAACE,MAAM,CAACC,OAAO,GAAG,EAAE,CAAC;IACpC,CAAC;IACDD,MAAM,CAACE,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,CAAC;IAC/C,OAAO,MAAMC,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMK,QAAQ,GAAG,CACf;IAAEC,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAEjB;EAAI,CAAC,EAClD;IAAEe,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAElB;EAAO,CAAC,CACtD;EAED,oBACEG,OAAA,CAACN,MAAM,CAACsB,MAAM;IACZC,OAAO,EAAE;MAAEC,CAAC,EAAE,CAAC;IAAI,CAAE;IACrBC,OAAO,EAAE;MAAED,CAAC,EAAE;IAAE,CAAE;IAClBE,UAAU,EAAE;MAAEC,QAAQ,EAAE,GAAG;MAAEC,IAAI,EAAE;IAAU,CAAE;IAC/CC,SAAS,EAAE,+DACTlB,UAAU,GACN,wBAAwB,GACxB,gBAAgB,EACnB;IAAAmB,QAAA,eAEHxB,OAAA;MAAKuB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAC/BxB,OAAA;QAAKuB,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBAE7DxB,OAAA,CAACN,MAAM,CAAC+B,GAAG;UACTC,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BJ,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAEvCxB,OAAA;YAAKuB,SAAS,EAAC,qGAAqG;YAAAC,QAAA,eAClHxB,OAAA,CAACH,MAAM;cAAC0B,SAAS,EAAC;YAAoB;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtC,CAAC,eACN/B,OAAA;YAAAwB,QAAA,gBACExB,OAAA;cAAIuB,SAAS,EAAC,iCAAiC;cAAAC,QAAA,EAAC;YAAQ;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eAC7D/B,OAAA;cAAGuB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAc;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACpD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,eAGb/B,OAAA;UAAKuB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnDZ,QAAQ,CAACoB,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACxBlC,OAAA,CAACN,MAAM,CAACyC,CAAC;YAEPrB,IAAI,EAAEmB,IAAI,CAACnB,IAAK;YAChBG,OAAO,EAAE;cAAEmB,OAAO,EAAE,CAAC;cAAElB,CAAC,EAAE,CAAC;YAAG,CAAE;YAChCC,OAAO,EAAE;cAAEiB,OAAO,EAAE,CAAC;cAAElB,CAAC,EAAE;YAAE,CAAE;YAC9BE,UAAU,EAAE;cAAEiB,KAAK,EAAEH,KAAK,GAAG,GAAG,GAAG;YAAI,CAAE;YACzCR,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BJ,SAAS,EAAC,6GAA6G;YAAAC,QAAA,gBAEvHxB,OAAA,CAACiC,IAAI,CAAClB,IAAI;cAACQ,SAAS,EAAC;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjC/B,OAAA;cAAAwB,QAAA,EAAOS,IAAI,CAACpB;YAAI;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GATnBE,IAAI,CAACpB,IAAI;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAUN,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eAGN/B,OAAA;UAAKuB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDxB,OAAA,CAACN,MAAM,CAAC4C,MAAM;YACZZ,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BY,QAAQ,EAAE;cAAEZ,KAAK,EAAE;YAAK,CAAE;YAC1BJ,SAAS,EAAC,iFAAiF;YAAAC,QAAA,EAC5F;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC,eAChB/B,OAAA,CAACN,MAAM,CAAC4C,MAAM;YACZZ,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BY,QAAQ,EAAE;cAAEZ,KAAK,EAAE;YAAK,CAAE;YAC1BJ,SAAS,EAAC,aAAa;YAAAC,QAAA,EACxB;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eAGN/B,OAAA,CAACN,MAAM,CAAC4C,MAAM;UACZC,QAAQ,EAAE;YAAEZ,KAAK,EAAE;UAAK,CAAE;UAC1Ba,OAAO,EAAEA,CAAA,KAAMpC,aAAa,CAAC,CAACD,UAAU,CAAE;UAC1CoB,SAAS,EAAC,mFAAmF;UAAAC,QAAA,EAE5FrB,UAAU,gBAAGH,OAAA,CAACJ,CAAC;YAAC2B,SAAS,EAAC;UAAS;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,gBAAG/B,OAAA,CAACL,IAAI;YAAC4B,SAAS,EAAC;UAAS;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,eAGN/B,OAAA,CAACN,MAAM,CAAC+B,GAAG;QACTR,OAAO,EAAE,KAAM;QACfE,OAAO,EAAE;UAAEsB,MAAM,EAAEtC,UAAU,GAAG,MAAM,GAAG;QAAE,CAAE;QAC7CoB,SAAS,EAAC,2BAA2B;QAAAC,QAAA,eAErCxB,OAAA;UAAKuB,SAAS,EAAC,gBAAgB;UAAAC,QAAA,GAC5BZ,QAAQ,CAACoB,GAAG,CAAEC,IAAI,iBACjBjC,OAAA;YAEEc,IAAI,EAAEmB,IAAI,CAACnB,IAAK;YAChB0B,OAAO,EAAEA,CAAA,KAAMpC,aAAa,CAAC,KAAK,CAAE;YACpCmB,SAAS,EAAC,6GAA6G;YAAAC,QAAA,gBAEvHxB,OAAA,CAACiC,IAAI,CAAClB,IAAI;cAACQ,SAAS,EAAC;YAAS;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACjC/B,OAAA;cAAAwB,QAAA,EAAOS,IAAI,CAACpB;YAAI;cAAAe,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA,GANnBE,IAAI,CAACpB,IAAI;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAOb,CACJ,CAAC,eACF/B,OAAA;YAAKuB,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BxB,OAAA;cAAQuB,SAAS,EAAC,kGAAkG;cAAAC,QAAA,EAAC;YAErH;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT/B,OAAA;cAAQuB,SAAS,EAAC,gCAAgC;cAAAC,QAAA,EAAC;YAEnD;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB,CAAC;AAAC7B,EAAA,CAzHID,MAAgB;AAAAyC,EAAA,GAAhBzC,MAAgB;AA2HtB,eAAeA,MAAM;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}