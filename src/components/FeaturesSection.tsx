import React from 'react';

const FeaturesSection: React.FC = () => {
  const features = [
    {
      icon: '📄',
      title: 'AI-Powered OCR Intelligence',
      description: 'Advanced neural networks trained on Arabic and English financial documents achieve 99.9% extraction accuracy. Automatically reads, validates, and processes withdrawal requests in milliseconds.',
      gradient: 'var(--accent-gradient)'
    },
    {
      icon: '📍',
      title: 'Intelligent Regional Routing',
      description: 'Smart geographic assignment automatically routes requests to specialized regional teams across North Africa, Central Africa, Southeast Asia, and Central Asia for optimal processing efficiency.',
      gradient: 'var(--success-gradient)'
    },
    {
      icon: '🛡️',
      title: 'Zero-Exception Security Model',
      description: 'Military-grade role-based access controls with immutable permissions. Archive creates, Operations approves, Core Banking disburses - zero privilege escalation allowed.',
      gradient: 'var(--warning-gradient)'
    },
    {
      icon: '📊',
      title: 'Real-Time Process Intelligence',
      description: 'Live tracking with predictive analytics, automated bottleneck detection, and intelligent priority routing. Complete audit trails with regulatory compliance built-in.',
      gradient: 'linear-gradient(135deg, #8b5cf6, #7c3aed)'
    },
    {
      icon: '⚡',
      title: 'Intelligent Alert System',
      description: 'ML-powered priority detection with automated escalation. Urgent disbursements get immediate attention with smart notifications and visual indicators.',
      gradient: 'linear-gradient(135deg, #ef4444, #dc2626)'
    },
    {
      icon: '🔍',
      title: 'Advanced Analytics Engine',
      description: 'Multi-dimensional search, predictive insights, and performance analytics. Custom dashboards and automated reporting for data-driven decision making.',
      gradient: 'linear-gradient(135deg, #06b6d4, #0891b2)'
    }
  ];

  return (
    <section id="features" className="features">
      <div className="container">
        <div className="section-header animate-on-scroll">
          <div className="section-badge">
            ⚡ Powerful Features
          </div>
          <h2 className="section-title">Built for Enterprise Financial Operations</h2>
          <p className="section-description">
            Advanced AI capabilities, intelligent automation, and enterprise-grade security designed for the most demanding financial institutions.
          </p>
        </div>

        <div className="features-grid">
          {features.map((feature, index) => (
            <div key={index} className={`feature-card animate-on-scroll delay-${(index % 4) + 1}`}>
              <div
                className="feature-icon"
                style={{ background: feature.gradient }}
              >
                {feature.icon}
              </div>
              <h3 className="feature-title">{feature.title}</h3>
              <p className="feature-description">{feature.description}</p>
              <a href="#" className="feature-link">
                Learn more →
              </a>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default FeaturesSection;
