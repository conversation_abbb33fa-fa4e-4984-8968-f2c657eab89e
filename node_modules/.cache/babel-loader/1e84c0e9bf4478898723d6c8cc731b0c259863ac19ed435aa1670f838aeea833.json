{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/ADFD_Tracking_System/src/components/CTASection.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CTASection = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"cta-section\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"animate-on-scroll\",\n        children: \"Ready to Transform Your Operations?\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"animate-on-scroll\",\n        children: \"Join leading financial institutions already using our intelligent withdrawal request tracking system.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"cta-buttons animate-on-scroll\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"#\",\n          className: \"btn-white\",\n          children: \"\\uD83D\\uDCC5 Schedule Demo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"#\",\n          className: \"btn-primary\",\n          children: \"\\uD83D\\uDE80 Start Free Trial\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = CTASection;\nexport default CTASection;\nvar _c;\n$RefreshReg$(_c, \"CTASection\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "CTASection", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/ADFD_Tracking_System/src/components/CTASection.tsx"], "sourcesContent": ["import React from 'react';\n\nconst CTASection: React.FC = () => {\n  return (\n    <section className=\"cta-section\">\n      <div className=\"container\">\n        <h2 className=\"animate-on-scroll\">Ready to Transform Your Operations?</h2>\n        <p className=\"animate-on-scroll\">\n          Join leading financial institutions already using our intelligent withdrawal request tracking system.\n        </p>\n        <div className=\"cta-buttons animate-on-scroll\">\n          <a href=\"#\" className=\"btn-white\">\n            📅 Schedule Demo\n          </a>\n          <a href=\"#\" className=\"btn-primary\">\n            🚀 Start Free Trial\n          </a>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default CTASection;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EACjC,oBACED,OAAA;IAASE,SAAS,EAAC,aAAa;IAAAC,QAAA,eAC9BH,OAAA;MAAKE,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBH,OAAA;QAAIE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAAC;MAAmC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC1EP,OAAA;QAAGE,SAAS,EAAC,mBAAmB;QAAAC,QAAA,EAAC;MAEjC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJP,OAAA;QAAKE,SAAS,EAAC,+BAA+B;QAAAC,QAAA,gBAC5CH,OAAA;UAAGQ,IAAI,EAAC,GAAG;UAACN,SAAS,EAAC,WAAW;UAAAC,QAAA,EAAC;QAElC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJP,OAAA;UAAGQ,IAAI,EAAC,GAAG;UAACN,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAEpC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACE,EAAA,GAnBIR,UAAoB;AAqB1B,eAAeA,UAAU;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}