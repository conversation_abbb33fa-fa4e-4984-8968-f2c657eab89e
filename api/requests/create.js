// Vercel serverless function for creating withdrawal requests
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY // Use service role for admin operations
)

// Regional assignment mapping
const REGIONAL_MAPPING = {
  // Europe & Latin America
  'Spain': 'europe_latin_america',
  'Portugal': 'europe_latin_america', 
  'Italy': 'europe_latin_america',
  'France': 'europe_latin_america',
  'Germany': 'europe_latin_america',
  'UK': 'europe_latin_america',
  'Brazil': 'europe_latin_america',
  'Argentina': 'europe_latin_america',
  'Chile': 'europe_latin_america',
  'Colombia': 'europe_latin_america',
  'Mexico': 'europe_latin_america',
  'Peru': 'europe_latin_america',
  
  // Africa
  'Egypt': 'africa',
  'Libya': 'africa',
  'Tunisia': 'africa',
  'Algeria': 'africa',
  'Morocco': 'africa',
  'Sudan': 'africa',
  'Chad': 'africa',
  'CAR': 'africa',
  'DRC': 'africa',
  'Cameroon': 'africa',
  'Nigeria': 'africa',
  'Kenya': 'africa',
  'Ethiopia': 'africa',
  'Ghana': 'africa',
  'Senegal': 'africa',
  
  // Asia
  'Malaysia': 'asia',
  'Indonesia': 'asia',
  'Thailand': 'asia',
  'Vietnam': 'asia',
  'Philippines': 'asia',
  'Singapore': 'asia',
  'Kazakhstan': 'asia',
  'Uzbekistan': 'asia',
  'Kyrgyzstan': 'asia',
  'Tajikistan': 'asia',
  'Afghanistan': 'asia',
  'Pakistan': 'asia',
  'India': 'asia',
  'Bangladesh': 'asia'
}

// Regional team leads for auto-assignment
const REGIONAL_LEADS = {
  'europe_latin_america': '<EMAIL>',
  'africa': '<EMAIL>',
  'asia': '<EMAIL>'
}

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', process.env.FRONTEND_URL || 'http://localhost:3000')
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization')

  if (req.method === 'OPTIONS') {
    return res.status(200).end()
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const authHeader = req.headers.authorization
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'No valid token provided' })
    }

    const token = authHeader.substring(7)

    // Verify user and get profile
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    if (authError || !user) {
      return res.status(401).json({ error: 'Invalid token' })
    }

    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', user.id)
      .single()

    if (profileError || !profile) {
      return res.status(403).json({ error: 'User profile not found' })
    }

    // Check if user has permission to create requests (Archive Team only)
    if (profile.role !== 'archive_team' || !profile.can_create_requests) {
      return res.status(403).json({ 
        error: 'Insufficient permissions. Only Archive Team can create requests.' 
      })
    }

    const {
      project_number,
      country,
      ref_number,
      beneficiary_name,
      amount,
      currency = 'USD',
      value_date,
      priority = 'medium',
      beneficiary_bank,
      bank_account,
      iban,
      swift_code,
      agreement_date
    } = req.body

    // Validate required fields
    if (!project_number || !country || !ref_number || !beneficiary_name || !amount || !value_date) {
      return res.status(400).json({ 
        error: 'Missing required fields: project_number, country, ref_number, beneficiary_name, amount, value_date' 
      })
    }

    // Determine region and auto-assign
    const region = REGIONAL_MAPPING[country]
    if (!region) {
      return res.status(400).json({ 
        error: `Country "${country}" is not supported. Please contact administrator.` 
      })
    }

    const assignedToEmail = REGIONAL_LEADS[region]
    
    // Get assigned user ID
    const { data: assignedUser, error: assignedError } = await supabase
      .from('user_profiles')
      .select('id')
      .eq('email', assignedToEmail)
      .single()

    if (assignedError || !assignedUser) {
      return res.status(500).json({ 
        error: 'Failed to assign regional team lead' 
      })
    }

    // Calculate SLA deadline based on priority
    const slaHours = {
      'urgent': 24,
      'high': 72,
      'medium': 168, // 7 days
      'low': 336     // 14 days
    }
    
    const slaDeadline = new Date()
    slaDeadline.setHours(slaDeadline.getHours() + slaHours[priority])

    // Create withdrawal request
    const { data: request, error: createError } = await supabase
      .from('withdrawal_requests')
      .insert({
        project_number,
        country,
        region,
        ref_number,
        beneficiary_name,
        amount: parseFloat(amount),
        currency,
        value_date,
        status: 'pending',
        current_stage: 'initial_review',
        priority,
        assigned_to: assignedUser.id,
        created_by: user.id,
        beneficiary_bank,
        bank_account,
        iban,
        swift_code,
        agreement_date,
        sla_deadline: slaDeadline.toISOString(),
        processing_days: 0,
        is_overdue: false
      })
      .select()
      .single()

    if (createError) {
      return res.status(500).json({ 
        error: 'Failed to create withdrawal request',
        details: createError.message 
      })
    }

    // Log audit trail
    await supabase
      .from('audit_logs')
      .insert({
        request_id: request.id,
        user_id: user.id,
        action_type: 'create',
        action_details: `Withdrawal request created for ${beneficiary_name} - ${amount} ${currency}`,
        new_stage: 'initial_review',
        new_status: 'pending',
        amount_involved: parseFloat(amount),
        regional_context: region,
        metadata: {
          auto_assigned_to: assignedToEmail,
          country: country,
          priority: priority
        }
      })

    return res.status(201).json({
      success: true,
      message: 'Withdrawal request created successfully',
      request: {
        id: request.id,
        project_number: request.project_number,
        ref_number: request.ref_number,
        beneficiary_name: request.beneficiary_name,
        amount: request.amount,
        currency: request.currency,
        status: request.status,
        current_stage: request.current_stage,
        region: request.region,
        assigned_to_email: assignedToEmail,
        sla_deadline: request.sla_deadline,
        created_at: request.created_at
      }
    })

  } catch (error) {
    console.error('Create request error:', error)
    return res.status(500).json({ 
      error: 'Internal server error',
      details: error.message 
    })
  }
}
