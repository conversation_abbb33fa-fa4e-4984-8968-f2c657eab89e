# ADFD Tracking System - API Documentation

## Overview
This API provides backend functionality for the Abu Dhabi Fund Withdrawal Request Tracker system. All endpoints are designed as Vercel serverless functions with Supabase integration.

## Architecture
- **Framework**: Vercel Serverless Functions
- **Database**: Supabase PostgreSQL
- **Authentication**: Supabase Auth with JWT tokens
- **Authorization**: Role-based access control (RLS)

## Authentication
All API endpoints (except health checks) require authentication via Bearer token:
```
Authorization: Bearer <jwt_token>
```

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout  
- `GET /api/auth/verify` - Verify token validity

### Withdrawal Requests
- `POST /api/requests/create` - Create new withdrawal request (Archive Team only)
- `GET /api/requests/list` - List requests (role-based filtering)
- `POST /api/requests/approve` - Approve request (Operations/Core Banking)
- `POST /api/requests/reject` - Reject request (Operations Team only)

### User Management
- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update user profile

## Role-Based Access Control

### Archive Team
- Can create withdrawal requests
- Can view own created requests
- Cannot approve, reject, or disburse

### Operations Team (Regional)
- Can view requests in assigned region only
- Can approve/reject requests in Technical Review stage
- Cannot create requests or disburse

### Core Banking Team
- Can view requests in Core Banking stage
- Can process disbursements (final approval)
- Cannot create, approve, or reject requests

### Observers (Loan Administrator, Head of Operations)
- View-only access to all requests across regions
- Can add comments for coordination
- Cannot perform any workflow actions

## Regional Mapping
Requests are automatically assigned to regional teams based on beneficiary country:

### Europe & Latin America
- Countries: Spain, Portugal, Italy, France, Germany, UK, Brazil, Argentina, Chile, Colombia, Mexico, Peru
- Lead: Ali Al Derie (<EMAIL>)

### Africa  
- Countries: Egypt, Libya, Tunisia, Algeria, Morocco, Sudan, Chad, CAR, DRC, Cameroon, Nigeria, Kenya, Ethiopia, Ghana, Senegal
- Lead: Ahmed Al Kalbani (<EMAIL>)

### Asia
- Countries: Malaysia, Indonesia, Thailand, Vietnam, Philippines, Singapore, Kazakhstan, Uzbekistan, Kyrgyzstan, Tajikistan, Afghanistan, Pakistan, India, Bangladesh
- Lead: Abdulla Al Mansoori (<EMAIL>)

## Workflow Stages
1. **Initial Review** - Request created by Archive Team
2. **Technical Review** - Operations Team approval/rejection
3. **Core Banking** - Core Banking Team disbursement processing
4. **Disbursed** - Request completed

## Error Handling
All endpoints return consistent error responses:
```json
{
  "error": "Error message",
  "details": "Additional error details"
}
```

## Environment Variables
Required environment variables for deployment:
- `SUPABASE_URL` - Your Supabase project URL
- `SUPABASE_ANON_KEY` - Supabase anonymous key
- `SUPABASE_SERVICE_ROLE_KEY` - Supabase service role key (admin access)
- `FRONTEND_URL` - Frontend application URL for CORS

## Deployment
This API is optimized for Vercel deployment:
1. Push code to GitHub repository
2. Connect repository to Vercel
3. Set environment variables in Vercel dashboard
4. Deploy automatically on push to main branch

## Security Features
- JWT token authentication
- Role-based access control
- Row Level Security (RLS) policies
- CORS protection
- Input validation and sanitization
- Audit logging for all actions
