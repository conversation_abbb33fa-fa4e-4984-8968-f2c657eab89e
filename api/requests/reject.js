// Vercel serverless function for rejecting withdrawal requests
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', process.env.FRONTEND_URL || 'http://localhost:3000')
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization')

  if (req.method === 'OPTIONS') {
    return res.status(200).end()
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const authHeader = req.headers.authorization
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'No valid token provided' })
    }

    const token = authHeader.substring(7)

    // Verify user and get profile
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    if (authError || !user) {
      return res.status(401).json({ error: 'Invalid token' })
    }

    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', user.id)
      .single()

    if (profileError || !profile) {
      return res.status(403).json({ error: 'User profile not found' })
    }

    const { request_id, reason, comments } = req.body

    if (!request_id || !reason) {
      return res.status(400).json({ error: 'Request ID and rejection reason are required' })
    }

    // Get the withdrawal request
    const { data: request, error: requestError } = await supabase
      .from('withdrawal_requests')
      .select('*')
      .eq('id', request_id)
      .single()

    if (requestError || !request) {
      return res.status(404).json({ error: 'Withdrawal request not found' })
    }

    // Validate user permissions - only Operations Team can reject
    if (profile.role !== 'operations_team' || !profile.can_approve_reject) {
      return res.status(403).json({ 
        error: 'Only Operations Team members can reject requests' 
      })
    }

    // Check if request is in correct stage and region
    if (request.current_stage !== 'technical_review') {
      return res.status(400).json({ 
        error: 'Request can only be rejected during Technical Review stage' 
      })
    }

    if (request.region !== profile.region) {
      return res.status(403).json({ 
        error: 'You can only reject requests in your assigned region' 
      })
    }

    if (request.assigned_to !== user.id) {
      return res.status(403).json({ 
        error: 'You can only reject requests assigned to you' 
      })
    }

    // Update the withdrawal request
    const { data: updatedRequest, error: updateError } = await supabase
      .from('withdrawal_requests')
      .update({
        status: 'rejected',
        current_stage: 'rejected',
        updated_at: new Date().toISOString(),
        rejection_reason: reason,
        rejection_comments: comments
      })
      .eq('id', request_id)
      .select()
      .single()

    if (updateError) {
      return res.status(500).json({ 
        error: 'Failed to update withdrawal request',
        details: updateError.message 
      })
    }

    // Log audit trail
    await supabase
      .from('audit_logs')
      .insert({
        request_id: request_id,
        user_id: user.id,
        action_type: 'reject',
        action_details: `Request rejected by ${profile.full_name} - Reason: ${reason}${comments ? ` - ${comments}` : ''}`,
        previous_stage: request.current_stage,
        new_stage: 'rejected',
        previous_status: request.status,
        new_status: 'rejected',
        amount_involved: request.amount,
        regional_context: request.region,
        metadata: {
          rejection_reason: reason,
          rejection_comments: comments,
          processing_time_days: Math.floor((new Date() - new Date(request.created_at)) / (1000 * 60 * 60 * 24))
        }
      })

    // Add rejection comment
    await supabase
      .from('request_comments')
      .insert({
        request_id: request_id,
        user_id: user.id,
        comment: `REJECTION: ${reason}${comments ? ` - ${comments}` : ''}`,
        comment_type: 'rejection'
      })

    return res.status(200).json({
      success: true,
      message: 'Request successfully rejected',
      request: {
        id: updatedRequest.id,
        status: updatedRequest.status,
        current_stage: updatedRequest.current_stage,
        rejection_reason: updatedRequest.rejection_reason,
        rejection_comments: updatedRequest.rejection_comments,
        updated_at: updatedRequest.updated_at
      }
    })

  } catch (error) {
    console.error('Reject request error:', error)
    return res.status(500).json({ 
      error: 'Internal server error',
      details: error.message 
    })
  }
}
