{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/ADFD_Tracking_System/src/components/DemoSection.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst DemoSection = () => {\n  const openDemo = role => {\n    let message = '';\n    switch (role) {\n      case 'archive':\n        message = 'Opening Archive Team demo - Create new requests with OCR document upload!';\n        break;\n      case 'operations':\n        message = 'Opening Operations Team demo - Approve/reject requests in your region!';\n        break;\n      case 'banking':\n        message = 'Opening Core Banking demo - Process urgent disbursements!';\n        break;\n    }\n    alert(message);\n  };\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"demo\",\n    className: \"demo\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"section-header animate-on-scroll\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"See It In Action\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Experience the power of intelligent withdrawal request tracking\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 26,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 24,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"demo-container animate-on-scroll\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"demo-preview\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"demo-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"demo-status\",\n              children: \"\\u2705 Live System Status\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                marginLeft: 'auto',\n                color: '#10b981'\n              },\n              children: \"\\uD83D\\uDCCA Real-Time Tracking Active\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 30,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'grid',\n              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))',\n              gap: '1rem',\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '2rem',\n                  fontWeight: 'bold',\n                  color: '#3b82f6'\n                },\n                children: \"8\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '0.9rem',\n                  opacity: 0.8\n                },\n                children: \"Total Requests\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '2rem',\n                  fontWeight: 'bold',\n                  color: '#f59e0b'\n                },\n                children: \"3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 49,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '0.9rem',\n                  opacity: 0.8\n                },\n                children: \"Pending Review\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 48,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '2rem',\n                  fontWeight: 'bold',\n                  color: '#10b981'\n                },\n                children: \"2\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 53,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '0.9rem',\n                  opacity: 0.8\n                },\n                children: \"Ready to Disburse\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 54,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '2rem',\n                  fontWeight: 'bold',\n                  color: '#8b5cf6'\n                },\n                children: \"3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: '0.9rem',\n                  opacity: 0.8\n                },\n                children: \"Disbursed\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            margin: '2rem 0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            style: {\n              marginBottom: '1rem',\n              color: '#1f2937'\n            },\n            children: \"Try Different User Roles:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              gap: '1rem',\n              justifyContent: 'center',\n              flexWrap: 'wrap'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn-primary\",\n              onClick: () => openDemo('archive'),\n              children: \"\\u2B06\\uFE0F Archive Team\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn-primary\",\n              onClick: () => openDemo('operations'),\n              children: \"\\u2611\\uFE0F Operations\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              className: \"btn-primary\",\n              onClick: () => openDemo('banking'),\n              children: \"\\uD83D\\uDCB0 Core Banking\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#6b7280',\n            fontSize: '0.9rem'\n          },\n          children: \"Click any role above to experience the system from different perspectives. Each user sees only what they need to see, with actions restricted to their permissions.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 23,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n_c = DemoSection;\nexport default DemoSection;\nvar _c;\n$RefreshReg$(_c, \"DemoSection\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "DemoSection", "openDemo", "role", "message", "alert", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "marginLeft", "color", "display", "gridTemplateColumns", "gap", "textAlign", "fontSize", "fontWeight", "opacity", "margin", "marginBottom", "justifyContent", "flexWrap", "onClick", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/ADFD_Tracking_System/src/components/DemoSection.tsx"], "sourcesContent": ["import React from 'react';\n\nconst DemoSection: React.FC = () => {\n  const openDemo = (role: string) => {\n    let message = '';\n    switch(role) {\n      case 'archive':\n        message = 'Opening Archive Team demo - Create new requests with OCR document upload!';\n        break;\n      case 'operations':\n        message = 'Opening Operations Team demo - Approve/reject requests in your region!';\n        break;\n      case 'banking':\n        message = 'Opening Core Banking demo - Process urgent disbursements!';\n        break;\n    }\n    \n    alert(message);\n  };\n\n  return (\n    <section id=\"demo\" className=\"demo\">\n      <div className=\"container\">\n        <div className=\"section-header animate-on-scroll\">\n          <h2>See It In Action</h2>\n          <p>Experience the power of intelligent withdrawal request tracking</p>\n        </div>\n        <div className=\"demo-container animate-on-scroll\">\n          <div className=\"demo-preview\">\n            <div className=\"demo-header\">\n              <div className=\"demo-status\">\n                ✅ Live System Status\n              </div>\n              <div style={{ marginLeft: 'auto', color: '#10b981' }}>\n                📊 Real-Time Tracking Active\n              </div>\n            </div>\n            <div style={{ \n              display: 'grid', \n              gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', \n              gap: '1rem', \n              textAlign: 'center' \n            }}>\n              <div>\n                <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#3b82f6' }}>8</div>\n                <div style={{ fontSize: '0.9rem', opacity: 0.8 }}>Total Requests</div>\n              </div>\n              <div>\n                <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#f59e0b' }}>3</div>\n                <div style={{ fontSize: '0.9rem', opacity: 0.8 }}>Pending Review</div>\n              </div>\n              <div>\n                <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#10b981' }}>2</div>\n                <div style={{ fontSize: '0.9rem', opacity: 0.8 }}>Ready to Disburse</div>\n              </div>\n              <div>\n                <div style={{ fontSize: '2rem', fontWeight: 'bold', color: '#8b5cf6' }}>3</div>\n                <div style={{ fontSize: '0.9rem', opacity: 0.8 }}>Disbursed</div>\n              </div>\n            </div>\n          </div>\n          <div style={{ margin: '2rem 0' }}>\n            <h3 style={{ marginBottom: '1rem', color: '#1f2937' }}>Try Different User Roles:</h3>\n            <div style={{ display: 'flex', gap: '1rem', justifyContent: 'center', flexWrap: 'wrap' }}>\n              <button className=\"btn-primary\" onClick={() => openDemo('archive')}>\n                ⬆️ Archive Team\n              </button>\n              <button className=\"btn-primary\" onClick={() => openDemo('operations')}>\n                ☑️ Operations\n              </button>\n              <button className=\"btn-primary\" onClick={() => openDemo('banking')}>\n                💰 Core Banking\n              </button>\n            </div>\n          </div>\n          <p style={{ color: '#6b7280', fontSize: '0.9rem' }}>\n            Click any role above to experience the system from different perspectives. \n            Each user sees only what they need to see, with actions restricted to their permissions.\n          </p>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default DemoSection;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAClC,MAAMC,QAAQ,GAAIC,IAAY,IAAK;IACjC,IAAIC,OAAO,GAAG,EAAE;IAChB,QAAOD,IAAI;MACT,KAAK,SAAS;QACZC,OAAO,GAAG,2EAA2E;QACrF;MACF,KAAK,YAAY;QACfA,OAAO,GAAG,wEAAwE;QAClF;MACF,KAAK,SAAS;QACZA,OAAO,GAAG,2DAA2D;QACrE;IACJ;IAEAC,KAAK,CAACD,OAAO,CAAC;EAChB,CAAC;EAED,oBACEJ,OAAA;IAASM,EAAE,EAAC,MAAM;IAACC,SAAS,EAAC,MAAM;IAAAC,QAAA,eACjCR,OAAA;MAAKO,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBR,OAAA;QAAKO,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/CR,OAAA;UAAAQ,QAAA,EAAI;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzBZ,OAAA;UAAAQ,QAAA,EAAG;QAA+D;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACnE,CAAC,eACNZ,OAAA;QAAKO,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/CR,OAAA;UAAKO,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BR,OAAA;YAAKO,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BR,OAAA;cAAKO,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAC;YAE7B;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNZ,OAAA;cAAKa,KAAK,EAAE;gBAAEC,UAAU,EAAE,MAAM;gBAAEC,KAAK,EAAE;cAAU,CAAE;cAAAP,QAAA,EAAC;YAEtD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNZ,OAAA;YAAKa,KAAK,EAAE;cACVG,OAAO,EAAE,MAAM;cACfC,mBAAmB,EAAE,sCAAsC;cAC3DC,GAAG,EAAE,MAAM;cACXC,SAAS,EAAE;YACb,CAAE;YAAAX,QAAA,gBACAR,OAAA;cAAAQ,QAAA,gBACER,OAAA;gBAAKa,KAAK,EAAE;kBAAEO,QAAQ,EAAE,MAAM;kBAAEC,UAAU,EAAE,MAAM;kBAAEN,KAAK,EAAE;gBAAU,CAAE;gBAAAP,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/EZ,OAAA;gBAAKa,KAAK,EAAE;kBAAEO,QAAQ,EAAE,QAAQ;kBAAEE,OAAO,EAAE;gBAAI,CAAE;gBAAAd,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,eACNZ,OAAA;cAAAQ,QAAA,gBACER,OAAA;gBAAKa,KAAK,EAAE;kBAAEO,QAAQ,EAAE,MAAM;kBAAEC,UAAU,EAAE,MAAM;kBAAEN,KAAK,EAAE;gBAAU,CAAE;gBAAAP,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/EZ,OAAA;gBAAKa,KAAK,EAAE;kBAAEO,QAAQ,EAAE,QAAQ;kBAAEE,OAAO,EAAE;gBAAI,CAAE;gBAAAd,QAAA,EAAC;cAAc;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,eACNZ,OAAA;cAAAQ,QAAA,gBACER,OAAA;gBAAKa,KAAK,EAAE;kBAAEO,QAAQ,EAAE,MAAM;kBAAEC,UAAU,EAAE,MAAM;kBAAEN,KAAK,EAAE;gBAAU,CAAE;gBAAAP,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/EZ,OAAA;gBAAKa,KAAK,EAAE;kBAAEO,QAAQ,EAAE,QAAQ;kBAAEE,OAAO,EAAE;gBAAI,CAAE;gBAAAd,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtE,CAAC,eACNZ,OAAA;cAAAQ,QAAA,gBACER,OAAA;gBAAKa,KAAK,EAAE;kBAAEO,QAAQ,EAAE,MAAM;kBAAEC,UAAU,EAAE,MAAM;kBAAEN,KAAK,EAAE;gBAAU,CAAE;gBAAAP,QAAA,EAAC;cAAC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAC/EZ,OAAA;gBAAKa,KAAK,EAAE;kBAAEO,QAAQ,EAAE,QAAQ;kBAAEE,OAAO,EAAE;gBAAI,CAAE;gBAAAd,QAAA,EAAC;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNZ,OAAA;UAAKa,KAAK,EAAE;YAAEU,MAAM,EAAE;UAAS,CAAE;UAAAf,QAAA,gBAC/BR,OAAA;YAAIa,KAAK,EAAE;cAAEW,YAAY,EAAE,MAAM;cAAET,KAAK,EAAE;YAAU,CAAE;YAAAP,QAAA,EAAC;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACrFZ,OAAA;YAAKa,KAAK,EAAE;cAAEG,OAAO,EAAE,MAAM;cAAEE,GAAG,EAAE,MAAM;cAAEO,cAAc,EAAE,QAAQ;cAAEC,QAAQ,EAAE;YAAO,CAAE;YAAAlB,QAAA,gBACvFR,OAAA;cAAQO,SAAS,EAAC,aAAa;cAACoB,OAAO,EAAEA,CAAA,KAAMzB,QAAQ,CAAC,SAAS,CAAE;cAAAM,QAAA,EAAC;YAEpE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTZ,OAAA;cAAQO,SAAS,EAAC,aAAa;cAACoB,OAAO,EAAEA,CAAA,KAAMzB,QAAQ,CAAC,YAAY,CAAE;cAAAM,QAAA,EAAC;YAEvE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTZ,OAAA;cAAQO,SAAS,EAAC,aAAa;cAACoB,OAAO,EAAEA,CAAA,KAAMzB,QAAQ,CAAC,SAAS,CAAE;cAAAM,QAAA,EAAC;YAEpE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNZ,OAAA;UAAGa,KAAK,EAAE;YAAEE,KAAK,EAAE,SAAS;YAAEK,QAAQ,EAAE;UAAS,CAAE;UAAAZ,QAAA,EAAC;QAGpD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACgB,EAAA,GAjFI3B,WAAqB;AAmF3B,eAAeA,WAAW;AAAC,IAAA2B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}