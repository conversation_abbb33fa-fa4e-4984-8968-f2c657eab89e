@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

:root {
    --primary-gradient: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%);
    --accent-gradient: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 25%, #1e40af 50%, #1e3a8a 75%, #172554 100%);
    --success-gradient: linear-gradient(135deg, #059669 0%, #047857 25%, #065f46 50%, #064e3b 75%, #022c22 100%);
    --warning-gradient: linear-gradient(135deg, #d97706 0%, #b45309 25%, #92400e 50%, #78350f 75%, #451a03 100%);
    --surface-glass: rgba(255, 255, 255, 0.08);
    --surface-glass-hover: rgba(255, 255, 255, 0.12);
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-muted: #64748b;
    --border-subtle: rgba(148, 163, 184, 0.2);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    overflow-x: hidden;
    background: #fafafa;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Enhanced Background */


/* Enhanced Navigation */
.navigation {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(15, 23, 42, 0.8);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 1000;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.navigation.scrolled {
    background: #ffffff;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.navigation.scrolled .brand {
    color: #1f2937;
}

.navigation.scrolled .nav-menu a {
    color: #6b7280;
}

.navigation.scrolled .nav-menu a:hover {
    color: #1f2937;
}

.navigation.scrolled .btn-nav.secondary {
    color: #374151;
    border-color: #d1d5db;
    background: rgba(0, 0, 0, 0.05);
}

.navigation.scrolled .btn-nav.secondary:hover {
    background: rgba(0, 0, 0, 0.1);
    border-color: #9ca3af;
}


.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 0;
}

.brand {
    display: flex;
    align-items: center;
    gap: 1rem;
    text-decoration: none;
    color: white;
}

.brand-icon {
    width: 48px;
    height: 48px;
    background: var(--accent-gradient);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
}

.brand-text {
    display: flex;
    flex-direction: column;
}

.brand-name {
    font-size: 1.5rem;
    font-weight: 800;
    letter-spacing: -0.025em;
}

.brand-tagline {
    font-size: 0.75rem;
    opacity: 0.7;
    font-weight: 500;
    letter-spacing: 0.05em;
    text-transform: uppercase;
}

.nav-menu {
    display: flex;
    gap: 3rem;
    list-style: none;
}

.nav-menu a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem 0;
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--accent-gradient);
    transition: width 0.3s ease;
}

.nav-menu a:hover {
    color: white;
}

.nav-menu a:hover::after {
    width: 100%;
}

.nav-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.btn-nav {
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    cursor: pointer;
}

.btn-nav.secondary {
    color: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
}

.btn-nav.secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-1px);
}

.btn-nav.primary {
    background: linear-gradient(135deg, #1e3a8a, #34d399);
    color: white;
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.btn-nav.primary:hover {
    box-shadow: 0 6px 24px rgba(59, 130, 246, 0.4);
    transform: translateY(-2px);
}

/* Hero Section */
.hero {
    position: relative;
    padding: 12rem 0 8rem;
    background: linear-gradient(135deg, #1e3a8a, #34d399);
    color: white;
    text-align: center;
}

.hero-content {
    max-width: 900px;
    margin: 0 auto;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.15);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 3rem;
}

.hero-badge::before {
    content: '';
    width: 8px;
    height: 8px;
    background: #10b981;
    border-radius: 50%;
}

.hero-title {
    font-size: clamp(3rem, 6vw, 5.5rem);
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 2rem;
    color: white;
}

.hero-subtitle {
    font-size: 1.5rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 3rem;
    font-weight: 400;
}

.hero-actions {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 4rem;
}

.btn-hero {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1.25rem 2.5rem;
    border-radius: 16px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.btn-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-hero:hover::before {
    left: 100%;
}

.btn-hero.primary {
    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
    color: #0f172a;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.btn-hero.primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}

.btn-hero.secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-color: rgba(255, 255, 255, 0.3);
}

.btn-hero.secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-3px);
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    max-width: 900px;
    margin: 0 auto;
    padding: 0 1rem;
}

.hero-stat {
    text-align: center;
    padding: 1.5rem 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.hero-stat:hover {
    transform: translateY(-4px);
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.2);
}

.hero-stat-number {
    display: block;
    font-size: 3rem;
    font-weight: 900;
    color: #ffffff;
    text-shadow: 2px 2px 5px rgba(0, 0, 0, 0.3);
    margin-bottom: 0.5rem;
}

.hero-stat-label {
    color: #ffffff;
    font-weight: 600;
    font-size: 0.95rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
}

/* Trust Section */
.trust-section {
    background: white;
    padding: 6rem 0;
    border-top: 1px solid var(--border-subtle);
}

.trust-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.trust-label {
    color: var(--text-muted);
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin-bottom: 3rem;
}

.trust-logos {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 3rem;
    align-items: center;
}

.trust-logo {
    height: 60px;
    background: var(--surface-glass);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    color: var(--text-secondary);
    border: 1px solid var(--border-subtle);
    transition: all 0.3s ease;
}

.trust-logo:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.2);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Scroll Animations */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-on-scroll.visible {
    opacity: 1;
    transform: translateY(0);
}

.delay-1 { transition-delay: 0.1s; }
.delay-2 { transition-delay: 0.2s; }
.delay-3 { transition-delay: 0.3s; }
.delay-4 { transition-delay: 0.4s; }

/* Section Styles */
.section-header {
    text-align: center;
    margin-bottom: 5rem;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--surface-glass);
    border: 1px solid var(--border-subtle);
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.85rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
}

.section-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 900;
    line-height: 1.2;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
}

.section-description {
    font-size: 1.25rem;
    line-height: 1.6;
    color: var(--text-secondary);
    max-width: 700px;
    margin: 0 auto;
}

/* Features Section */
.features {
    padding: 8rem 0;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

/* Security Section */
.security {
    padding: 8rem 0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: #1e293b;
    position: relative;
}

.security-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 3rem;
    margin-bottom: 6rem;
}

.security-card {
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 24px;
    padding: 3rem;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
    position: relative;
    z-index: 1;
}

.security-card:hover {
    transform: translateY(-8px);
    background: #ffffff;
    border-color: #cbd5e1;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.security-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin: 0 auto 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.security-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #1e293b;
}

.security-description {
    color: #64748b;
    line-height: 1.6;
}



.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 3rem;
}

.feature-card {
    background: white;
    border-radius: 24px;
    padding: 3rem;
    border: 1px solid var(--border-subtle);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--accent-gradient);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 24px 64px rgba(0, 0, 0, 0.15);
}

.feature-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.feature-title {
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--text-primary);
    margin-bottom: 1rem;
    line-height: 1.3;
}

.feature-description {
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: 1.5rem;
}

.feature-link {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.feature-link:hover {
    color: #1d4ed8;
    transform: translateX(4px);
}

/* Platform Section Styling */
.nav-menu li:first-child a {
    background: linear-gradient(135deg, #1e3a8a, #34d399);
    color: white !important;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.nav-menu li:first-child a:hover {
    background: linear-gradient(135deg, #1e40af, #10b981);
    transform: translateY(-2px);
}

.footer-section:first-child {
    background: linear-gradient(135deg, #1e3a8a, #34d399);
    padding: 1.5rem;
    border-radius: 12px;
    border: 1px solid #34d399;
    color: white;
}

.footer-section:first-child h4 {
    color: white !important;
    font-weight: 700;
}

.footer-section:first-child a {
    color: rgba(255, 255, 255, 0.9) !important;
}

.footer-section:first-child a:hover {
    color: white !important;
}

/* Logo Styling */
.brand-logo,
.footer-logo {
    width: 40px;
    height: 40px;
    filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.1));
}

/* Footer */
.footer {
    background: #0f172a;
    color: rgba(255, 255, 255, 0.8);
    padding: 6rem 0 3rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 6rem;
    margin-bottom: 4rem;
}

.footer-brand {
    max-width: 400px;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.footer-logo-icon {
    width: 48px;
    height: 48px;
    background: var(--accent-gradient);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.footer-description {
    line-height: 1.7;
    margin-bottom: 2rem;
}

.footer-social {
    display: flex;
    gap: 1rem;
}

.social-link {
    width: 44px;
    height: 44px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    transform: translateY(-2px);
}

.footer-sections {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 3rem;
}

.footer-section h4 {
    color: white;
    font-weight: 700;
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section li {
    margin-bottom: 0.75rem;
}

.footer-section a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: white;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-copyright {
    color: rgba(255, 255, 255, 0.6);
}

.footer-links {
    display: flex;
    gap: 2rem;
    list-style: none;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 0.9rem;
}

.footer-links a:hover {
    color: white;
}

