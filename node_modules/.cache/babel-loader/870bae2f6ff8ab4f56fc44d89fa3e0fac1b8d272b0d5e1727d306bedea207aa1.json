{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/ADFD_Tracking_System/src/components/FeaturesSection.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FeaturesSection = () => {\n  const features = [{\n    icon: '📄',\n    title: 'AI-Powered OCR',\n    description: 'Automatically extract all form data from uploaded withdrawal request PDFs. Our intelligent system reads documents, validates information, and pre-fills forms with 99.9% accuracy.',\n    gradient: 'linear-gradient(135deg, #3b82f6, #6366f1)'\n  }, {\n    icon: '📍',\n    title: 'Regional Auto-Assignment',\n    description: 'Smart geographic routing automatically assigns requests to the appropriate regional operations team based on beneficiary country, ensuring proper jurisdiction and expertise.',\n    gradient: 'linear-gradient(135deg, #10b981, #059669)'\n  }, {\n    icon: '🛡️',\n    title: 'Strict Role Controls',\n    description: 'Banking-grade security with zero-exception role enforcement. Archive creates, Operations approves, Core Banking disburses - no privilege escalation allowed.',\n    gradient: 'linear-gradient(135deg, #f59e0b, #d97706)'\n  }, {\n    icon: '📊',\n    title: 'Real-Time Tracking',\n    description: 'Live status updates, instant notifications, and comprehensive audit trails. Track every request from creation to final disbursement with complete transparency.',\n    gradient: 'linear-gradient(135deg, #8b5cf6, #7c3aed)'\n  }, {\n    icon: '⚡',\n    title: 'Urgent Action Alerts',\n    description: 'Never miss critical disbursements. Priority-based visual indicators and urgent alerts ensure high-value requests get immediate attention from the right team.',\n    gradient: 'linear-gradient(135deg, #ef4444, #dc2626)'\n  }, {\n    icon: '🔍',\n    title: 'Advanced Filtering',\n    description: 'Multi-parameter search across reference numbers, beneficiaries, countries, stages, and amounts. Find any request instantly with intelligent filtering and sorting.',\n    gradient: 'linear-gradient(135deg, #06b6d4, #0891b2)'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"features\",\n    className: \"features\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"section-header animate-on-scroll\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Powerful Features\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Everything you need to manage withdrawal requests efficiently and securely\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"features-grid\",\n        children: features.map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"feature-card animate-on-scroll\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-icon\",\n            style: {\n              background: feature.gradient\n            },\n            children: feature.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 53,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            children: feature.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: feature.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this);\n};\n_c = FeaturesSection;\nexport default FeaturesSection;\nvar _c;\n$RefreshReg$(_c, \"FeaturesSection\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "FeaturesSection", "features", "icon", "title", "description", "gradient", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "feature", "index", "style", "background", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/ADFD_Tracking_System/src/components/FeaturesSection.tsx"], "sourcesContent": ["import React from 'react';\n\nconst FeaturesSection: React.FC = () => {\n  const features = [\n    {\n      icon: '📄',\n      title: 'AI-Powered OCR',\n      description: 'Automatically extract all form data from uploaded withdrawal request PDFs. Our intelligent system reads documents, validates information, and pre-fills forms with 99.9% accuracy.',\n      gradient: 'linear-gradient(135deg, #3b82f6, #6366f1)'\n    },\n    {\n      icon: '📍',\n      title: 'Regional Auto-Assignment',\n      description: 'Smart geographic routing automatically assigns requests to the appropriate regional operations team based on beneficiary country, ensuring proper jurisdiction and expertise.',\n      gradient: 'linear-gradient(135deg, #10b981, #059669)'\n    },\n    {\n      icon: '🛡️',\n      title: 'Strict Role Controls',\n      description: 'Banking-grade security with zero-exception role enforcement. Archive creates, Operations approves, Core Banking disburses - no privilege escalation allowed.',\n      gradient: 'linear-gradient(135deg, #f59e0b, #d97706)'\n    },\n    {\n      icon: '📊',\n      title: 'Real-Time Tracking',\n      description: 'Live status updates, instant notifications, and comprehensive audit trails. Track every request from creation to final disbursement with complete transparency.',\n      gradient: 'linear-gradient(135deg, #8b5cf6, #7c3aed)'\n    },\n    {\n      icon: '⚡',\n      title: 'Urgent Action Alerts',\n      description: 'Never miss critical disbursements. Priority-based visual indicators and urgent alerts ensure high-value requests get immediate attention from the right team.',\n      gradient: 'linear-gradient(135deg, #ef4444, #dc2626)'\n    },\n    {\n      icon: '🔍',\n      title: 'Advanced Filtering',\n      description: 'Multi-parameter search across reference numbers, beneficiaries, countries, stages, and amounts. Find any request instantly with intelligent filtering and sorting.',\n      gradient: 'linear-gradient(135deg, #06b6d4, #0891b2)'\n    }\n  ];\n\n  return (\n    <section id=\"features\" className=\"features\">\n      <div className=\"container\">\n        <div className=\"section-header animate-on-scroll\">\n          <h2>Powerful Features</h2>\n          <p>Everything you need to manage withdrawal requests efficiently and securely</p>\n        </div>\n        <div className=\"features-grid\">\n          {features.map((feature, index) => (\n            <div key={index} className=\"feature-card animate-on-scroll\">\n              <div \n                className=\"feature-icon\" \n                style={{ background: feature.gradient }}\n              >\n                {feature.icon}\n              </div>\n              <h3>{feature.title}</h3>\n              <p>{feature.description}</p>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default FeaturesSection;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EACtC,MAAMC,QAAQ,GAAG,CACf;IACEC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE,oLAAoL;IACjMC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,0BAA0B;IACjCC,WAAW,EAAE,+KAA+K;IAC5LC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,KAAK;IACXC,KAAK,EAAE,sBAAsB;IAC7BC,WAAW,EAAE,8JAA8J;IAC3KC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,iKAAiK;IAC9KC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,GAAG;IACTC,KAAK,EAAE,sBAAsB;IAC7BC,WAAW,EAAE,+JAA+J;IAC5KC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,oKAAoK;IACjLC,QAAQ,EAAE;EACZ,CAAC,CACF;EAED,oBACEN,OAAA;IAASO,EAAE,EAAC,UAAU;IAACC,SAAS,EAAC,UAAU;IAAAC,QAAA,eACzCT,OAAA;MAAKQ,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBT,OAAA;QAAKQ,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/CT,OAAA;UAAAS,QAAA,EAAI;QAAiB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1Bb,OAAA;UAAAS,QAAA,EAAG;QAA0E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9E,CAAC,eACNb,OAAA;QAAKQ,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC3BP,QAAQ,CAACY,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3BhB,OAAA;UAAiBQ,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBACzDT,OAAA;YACEQ,SAAS,EAAC,cAAc;YACxBS,KAAK,EAAE;cAAEC,UAAU,EAAEH,OAAO,CAACT;YAAS,CAAE;YAAAG,QAAA,EAEvCM,OAAO,CAACZ;UAAI;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNb,OAAA;YAAAS,QAAA,EAAKM,OAAO,CAACX;UAAK;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACxBb,OAAA;YAAAS,QAAA,EAAIM,OAAO,CAACV;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA,GARpBG,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACM,EAAA,GAhEIlB,eAAyB;AAkE/B,eAAeA,eAAe;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}