// Vercel serverless function for listing withdrawal requests
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', process.env.FRONTEND_URL || 'http://localhost:3000')
  res.setHeader('Access-Control-Allow-Methods', 'GET, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization')

  if (req.method === 'OPTIONS') {
    return res.status(200).end()
  }

  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const authHeader = req.headers.authorization
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'No valid token provided' })
    }

    const token = authHeader.substring(7)

    // Verify user and get profile
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    if (authError || !user) {
      return res.status(401).json({ error: 'Invalid token' })
    }

    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', user.id)
      .single()

    if (profileError || !profile) {
      return res.status(403).json({ error: 'User profile not found' })
    }

    // Query parameters for filtering
    const { 
      page = 1, 
      limit = 20, 
      status, 
      stage, 
      priority, 
      region,
      search 
    } = req.query

    let query = supabase
      .from('withdrawal_requests')
      .select(`
        *,
        created_by_profile:user_profiles!withdrawal_requests_created_by_fkey(full_name, email),
        assigned_to_profile:user_profiles!withdrawal_requests_assigned_to_fkey(full_name, email)
      `)

    // Apply role-based filtering
    switch (profile.role) {
      case 'archive_team':
        // Archive team can see requests they created
        query = query.eq('created_by', user.id)
        break
        
      case 'operations_team':
        // Operations team can see requests in their region
        query = query.eq('region', profile.region)
        break
        
      case 'core_banking':
        // Core banking can see requests in core_banking stage
        query = query.eq('current_stage', 'core_banking')
        break
        
      case 'loan_administrator':
      case 'head_of_operations':
        // Observers can see all requests (no additional filter)
        break
        
      default:
        return res.status(403).json({ error: 'Invalid user role' })
    }

    // Apply additional filters
    if (status) {
      query = query.eq('status', status)
    }
    
    if (stage) {
      query = query.eq('current_stage', stage)
    }
    
    if (priority) {
      query = query.eq('priority', priority)
    }
    
    if (region && (profile.role === 'loan_administrator' || profile.role === 'head_of_operations')) {
      query = query.eq('region', region)
    }
    
    if (search) {
      query = query.or(`
        project_number.ilike.%${search}%,
        ref_number.ilike.%${search}%,
        beneficiary_name.ilike.%${search}%,
        country.ilike.%${search}%
      `)
    }

    // Apply pagination and ordering
    const offset = (parseInt(page) - 1) * parseInt(limit)
    query = query
      .order('created_at', { ascending: false })
      .range(offset, offset + parseInt(limit) - 1)

    const { data: requests, error: queryError } = await query

    if (queryError) {
      return res.status(500).json({ 
        error: 'Failed to fetch requests',
        details: queryError.message 
      })
    }

    // Get total count for pagination
    let countQuery = supabase
      .from('withdrawal_requests')
      .select('id', { count: 'exact', head: true })

    // Apply same role-based filtering for count
    switch (profile.role) {
      case 'archive_team':
        countQuery = countQuery.eq('created_by', user.id)
        break
      case 'operations_team':
        countQuery = countQuery.eq('region', profile.region)
        break
      case 'core_banking':
        countQuery = countQuery.eq('current_stage', 'core_banking')
        break
    }

    const { count, error: countError } = await countQuery

    if (countError) {
      console.error('Count query error:', countError)
    }

    // Calculate pagination info
    const totalPages = Math.ceil((count || 0) / parseInt(limit))
    const hasNextPage = parseInt(page) < totalPages
    const hasPrevPage = parseInt(page) > 1

    return res.status(200).json({
      success: true,
      requests: requests || [],
      pagination: {
        current_page: parseInt(page),
        total_pages: totalPages,
        total_count: count || 0,
        limit: parseInt(limit),
        has_next_page: hasNextPage,
        has_prev_page: hasPrevPage
      },
      user_context: {
        role: profile.role,
        region: profile.region,
        permissions: {
          can_create_requests: profile.can_create_requests,
          can_approve_reject: profile.can_approve_reject,
          can_disburse: profile.can_disburse,
          view_only_access: profile.view_only_access
        }
      }
    })

  } catch (error) {
    console.error('List requests error:', error)
    return res.status(500).json({ 
      error: 'Internal server error',
      details: error.message 
    })
  }
}
