{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/ADFD_Tracking_System/src/components/SecuritySection.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SecuritySection = () => {\n  const securityFeatures = [{\n    icon: '🔐',\n    title: 'Bank-Level Encryption',\n    description: 'AES-256 encryption for all data at rest and in transit, meeting international banking security standards.',\n    gradient: 'linear-gradient(135deg, #ef4444, #dc2626)'\n  }, {\n    icon: '🛡️',\n    title: 'Multi-Factor Authentication',\n    description: 'Advanced MFA with biometric verification, hardware tokens, and time-based one-time passwords.',\n    gradient: 'linear-gradient(135deg, #3b82f6, #2563eb)'\n  }, {\n    icon: '👥',\n    title: 'Role-Based Access Control',\n    description: 'Granular permissions with zero-trust architecture ensuring users access only authorized resources.',\n    gradient: 'linear-gradient(135deg, #8b5cf6, #7c3aed)'\n  }, {\n    icon: '📋',\n    title: 'Comprehensive Audit Logs',\n    description: 'Immutable audit trails with real-time monitoring and automated compliance reporting.',\n    gradient: 'linear-gradient(135deg, #10b981, #059669)'\n  }, {\n    icon: '🔍',\n    title: 'Real-Time Threat Detection',\n    description: 'AI-powered security monitoring with automated threat response and incident management.',\n    gradient: 'linear-gradient(135deg, #f59e0b, #d97706)'\n  }, {\n    icon: '📜',\n    title: 'Regulatory Compliance',\n    description: 'Full compliance with PCI DSS, SOX, GDPR, and regional financial regulations.',\n    gradient: 'linear-gradient(135deg, #06b6d4, #0891b2)'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"security\",\n    className: \"security\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"section-header animate-on-scroll\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-badge\",\n          children: \"\\uD83D\\uDD12 Enterprise Security\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"Military-Grade Security Architecture\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"section-description\",\n          children: \"Built with the highest security standards to protect sensitive financial data and ensure regulatory compliance across all operations.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"security-grid\",\n        children: securityFeatures.map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `security-card animate-on-scroll delay-${index % 3 + 1}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"security-icon\",\n            style: {\n              background: feature.gradient\n            },\n            children: feature.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"security-title\",\n            children: feature.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"security-description\",\n            children: feature.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"security-certifications animate-on-scroll\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          children: \"Security Certifications & Compliance\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"certifications-grid\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"certification-badge\",\n            children: \"ISO 27001\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"certification-badge\",\n            children: \"SOC 2 Type II\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"certification-badge\",\n            children: \"PCI DSS Level 1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"certification-badge\",\n            children: \"GDPR Compliant\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this);\n};\n_c = SecuritySection;\nexport default SecuritySection;\nvar _c;\n$RefreshReg$(_c, \"SecuritySection\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "SecuritySection", "securityFeatures", "icon", "title", "description", "gradient", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "feature", "index", "style", "background", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/ADFD_Tracking_System/src/components/SecuritySection.tsx"], "sourcesContent": ["import React from 'react';\n\nconst SecuritySection: React.FC = () => {\n  const securityFeatures = [\n    {\n      icon: '🔐',\n      title: 'Bank-Level Encryption',\n      description: 'AES-256 encryption for all data at rest and in transit, meeting international banking security standards.',\n      gradient: 'linear-gradient(135deg, #ef4444, #dc2626)'\n    },\n    {\n      icon: '🛡️',\n      title: 'Multi-Factor Authentication',\n      description: 'Advanced MFA with biometric verification, hardware tokens, and time-based one-time passwords.',\n      gradient: 'linear-gradient(135deg, #3b82f6, #2563eb)'\n    },\n    {\n      icon: '👥',\n      title: 'Role-Based Access Control',\n      description: 'Granular permissions with zero-trust architecture ensuring users access only authorized resources.',\n      gradient: 'linear-gradient(135deg, #8b5cf6, #7c3aed)'\n    },\n    {\n      icon: '📋',\n      title: 'Comprehensive Audit Logs',\n      description: 'Immutable audit trails with real-time monitoring and automated compliance reporting.',\n      gradient: 'linear-gradient(135deg, #10b981, #059669)'\n    },\n    {\n      icon: '🔍',\n      title: 'Real-Time Threat Detection',\n      description: 'AI-powered security monitoring with automated threat response and incident management.',\n      gradient: 'linear-gradient(135deg, #f59e0b, #d97706)'\n    },\n    {\n      icon: '📜',\n      title: 'Regulatory Compliance',\n      description: 'Full compliance with PCI DSS, SOX, GDPR, and regional financial regulations.',\n      gradient: 'linear-gradient(135deg, #06b6d4, #0891b2)'\n    }\n  ];\n\n  return (\n    <section id=\"security\" className=\"security\">\n      <div className=\"container\">\n        <div className=\"section-header animate-on-scroll\">\n          <div className=\"section-badge\">\n            🔒 Enterprise Security\n          </div>\n          <h2 className=\"section-title\">Military-Grade Security Architecture</h2>\n          <p className=\"section-description\">\n            Built with the highest security standards to protect sensitive financial data and ensure regulatory compliance across all operations.\n          </p>\n        </div>\n\n        <div className=\"security-grid\">\n          {securityFeatures.map((feature, index) => (\n            <div key={index} className={`security-card animate-on-scroll delay-${(index % 3) + 1}`}>\n              <div\n                className=\"security-icon\"\n                style={{ background: feature.gradient }}\n              >\n                {feature.icon}\n              </div>\n              <h3 className=\"security-title\">{feature.title}</h3>\n              <p className=\"security-description\">{feature.description}</p>\n            </div>\n          ))}\n        </div>\n\n        <div className=\"security-certifications animate-on-scroll\">\n          <h3>Security Certifications & Compliance</h3>\n          <div className=\"certifications-grid\">\n            <div className=\"certification-badge\">ISO 27001</div>\n            <div className=\"certification-badge\">SOC 2 Type II</div>\n            <div className=\"certification-badge\">PCI DSS Level 1</div>\n            <div className=\"certification-badge\">GDPR Compliant</div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default SecuritySection;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EACtC,MAAMC,gBAAgB,GAAG,CACvB;IACEC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,uBAAuB;IAC9BC,WAAW,EAAE,2GAA2G;IACxHC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,KAAK;IACXC,KAAK,EAAE,6BAA6B;IACpCC,WAAW,EAAE,+FAA+F;IAC5GC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,2BAA2B;IAClCC,WAAW,EAAE,oGAAoG;IACjHC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,0BAA0B;IACjCC,WAAW,EAAE,sFAAsF;IACnGC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,4BAA4B;IACnCC,WAAW,EAAE,wFAAwF;IACrGC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,uBAAuB;IAC9BC,WAAW,EAAE,8EAA8E;IAC3FC,QAAQ,EAAE;EACZ,CAAC,CACF;EAED,oBACEN,OAAA;IAASO,EAAE,EAAC,UAAU;IAACC,SAAS,EAAC,UAAU;IAAAC,QAAA,eACzCT,OAAA;MAAKQ,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBT,OAAA;QAAKQ,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/CT,OAAA;UAAKQ,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAE/B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNb,OAAA;UAAIQ,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAoC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACvEb,OAAA;UAAGQ,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAAC;QAEnC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENb,OAAA;QAAKQ,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC3BP,gBAAgB,CAACY,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBACnChB,OAAA;UAAiBQ,SAAS,EAAE,yCAA0CQ,KAAK,GAAG,CAAC,GAAI,CAAC,EAAG;UAAAP,QAAA,gBACrFT,OAAA;YACEQ,SAAS,EAAC,eAAe;YACzBS,KAAK,EAAE;cAAEC,UAAU,EAAEH,OAAO,CAACT;YAAS,CAAE;YAAAG,QAAA,EAEvCM,OAAO,CAACZ;UAAI;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNb,OAAA;YAAIQ,SAAS,EAAC,gBAAgB;YAAAC,QAAA,EAAEM,OAAO,CAACX;UAAK;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACnDb,OAAA;YAAGQ,SAAS,EAAC,sBAAsB;YAAAC,QAAA,EAAEM,OAAO,CAACV;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA,GARrDG,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAENb,OAAA;QAAKQ,SAAS,EAAC,2CAA2C;QAAAC,QAAA,gBACxDT,OAAA;UAAAS,QAAA,EAAI;QAAoC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7Cb,OAAA;UAAKQ,SAAS,EAAC,qBAAqB;UAAAC,QAAA,gBAClCT,OAAA;YAAKQ,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpDb,OAAA;YAAKQ,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAAa;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACxDb,OAAA;YAAKQ,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1Db,OAAA;YAAKQ,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACM,EAAA,GAhFIlB,eAAyB;AAkF/B,eAAeA,eAAe;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}