{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/ADFD_Tracking_System/src/components/StatsSection.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst StatsSection = () => {\n  const stats = [{\n    number: '85%',\n    label: 'Processing Time Reduction'\n  }, {\n    number: '99.9%',\n    label: 'OCR Accuracy Rate'\n  }, {\n    number: '4',\n    label: 'Regional Operations Centers'\n  }, {\n    number: '24/7',\n    label: 'Real-Time Monitoring'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"stats\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"stats-grid\",\n        children: stats.map((stat, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"stat-item\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-number\",\n            children: stat.number\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"stat-label\",\n            children: stat.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 12,\n    columnNumber: 5\n  }, this);\n};\n_c = StatsSection;\nexport default StatsSection;\nvar _c;\n$RefreshReg$(_c, \"StatsSection\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "StatsSection", "stats", "number", "label", "className", "children", "map", "stat", "index", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/ADFD_Tracking_System/src/components/StatsSection.tsx"], "sourcesContent": ["import React from 'react';\n\nconst StatsSection: React.FC = () => {\n  const stats = [\n    { number: '85%', label: 'Processing Time Reduction' },\n    { number: '99.9%', label: 'OCR Accuracy Rate' },\n    { number: '4', label: 'Regional Operations Centers' },\n    { number: '24/7', label: 'Real-Time Monitoring' },\n  ];\n\n  return (\n    <section className=\"stats\">\n      <div className=\"container\">\n        <div className=\"stats-grid\">\n          {stats.map((stat, index) => (\n            <div key={index} className=\"stat-item\">\n              <span className=\"stat-number\">{stat.number}</span>\n              <span className=\"stat-label\">{stat.label}</span>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default StatsSection;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EACnC,MAAMC,KAAK,GAAG,CACZ;IAAEC,MAAM,EAAE,KAAK;IAAEC,KAAK,EAAE;EAA4B,CAAC,EACrD;IAAED,MAAM,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAoB,CAAC,EAC/C;IAAED,MAAM,EAAE,GAAG;IAAEC,KAAK,EAAE;EAA8B,CAAC,EACrD;IAAED,MAAM,EAAE,MAAM;IAAEC,KAAK,EAAE;EAAuB,CAAC,CAClD;EAED,oBACEJ,OAAA;IAASK,SAAS,EAAC,OAAO;IAAAC,QAAA,eACxBN,OAAA;MAAKK,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBN,OAAA;QAAKK,SAAS,EAAC,YAAY;QAAAC,QAAA,EACxBJ,KAAK,CAACK,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrBT,OAAA;UAAiBK,SAAS,EAAC,WAAW;UAAAC,QAAA,gBACpCN,OAAA;YAAMK,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAEE,IAAI,CAACL;UAAM;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC,eAClDb,OAAA;YAAMK,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEE,IAAI,CAACJ;UAAK;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA,GAFxCJ,KAAK;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACC,EAAA,GAtBIb,YAAsB;AAwB5B,eAAeA,YAAY;AAAC,IAAAa,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}