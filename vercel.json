{"version": 2, "name": "adfd-tracking-system", "builds": [{"src": "package.json", "use": "@vercel/static-build", "config": {"distDir": "build"}}], "routes": [{"src": "/api/(.*)", "dest": "/api/$1"}, {"src": "/(.*)", "dest": "/index.html"}], "functions": {"api/auth/login.js": {"maxDuration": 10}, "api/auth/logout.js": {"maxDuration": 10}, "api/auth/verify.js": {"maxDuration": 10}, "api/requests/create.js": {"maxDuration": 15}, "api/requests/list.js": {"maxDuration": 15}, "api/requests/approve.js": {"maxDuration": 15}, "api/requests/reject.js": {"maxDuration": 15}, "api/users/profile.js": {"maxDuration": 10}}, "env": {"SUPABASE_URL": "@supabase_url", "SUPABASE_ANON_KEY": "@supabase_anon_key", "SUPABASE_SERVICE_ROLE_KEY": "@supabase_service_role_key", "FRONTEND_URL": "@frontend_url"}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}]}