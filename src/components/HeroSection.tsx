import React from 'react';

const HeroSection: React.FC = () => {
  return (
    <section className="hero">
      <div className="container">
        <div className="hero-content">
          <div className="hero-badge">
            <span>AI-Powered Financial Operations</span>
          </div>

          <h1 className="hero-title">
            Transform Fund Disbursement with Intelligent Automation
          </h1>

          <p className="hero-subtitle">
            Enterprise-grade withdrawal request platform powered by AI-driven OCR, regional intelligence, and bank-level security. Trusted by leading financial institutions worldwide.
          </p>

          <div className="hero-actions">
            <a href="#start" className="btn-hero primary">
              🚀 Start Free Enterprise Trial
            </a>
            <a href="#platform" className="btn-hero secondary">
              ▶️ See Platform in Action
            </a>
          </div>

          <div className="hero-stats">
            <div className="hero-stat">
              <span className="hero-stat-number" data-target="99.9">99.9%</span>
              <span className="hero-stat-label">Accuracy Rate</span>
            </div>
            <div className="hero-stat">
              <span className="hero-stat-number" data-target="85">85%</span>
              <span className="hero-stat-label">Faster Processing</span>
            </div>
            <div className="hero-stat">
              <span className="hero-stat-number" data-target="24">24/7</span>
              <span className="hero-stat-label">Hour Monitoring</span>
            </div>
            <div className="hero-stat">
              <span className="hero-stat-number" data-target="100">100%</span>
              <span className="hero-stat-label">Uptime SLA</span>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
