[{"/Users/<USER>/ADFD_Tracking_System/src/index.tsx": "1", "/Users/<USER>/ADFD_Tracking_System/src/reportWebVitals.ts": "2", "/Users/<USER>/ADFD_Tracking_System/src/App.tsx": "3", "/Users/<USER>/ADFD_Tracking_System/src/components/AnimatedBackground.tsx": "4", "/Users/<USER>/ADFD_Tracking_System/src/components/Navigation.tsx": "5", "/Users/<USER>/ADFD_Tracking_System/src/components/HeroSection.tsx": "6", "/Users/<USER>/ADFD_Tracking_System/src/components/StatsSection.tsx": "7", "/Users/<USER>/ADFD_Tracking_System/src/components/FeaturesSection.tsx": "8", "/Users/<USER>/ADFD_Tracking_System/src/components/WorkflowSection.tsx": "9", "/Users/<USER>/ADFD_Tracking_System/src/components/RolesSection.tsx": "10", "/Users/<USER>/ADFD_Tracking_System/src/components/TechnologySection.tsx": "11", "/Users/<USER>/ADFD_Tracking_System/src/components/DemoSection.tsx": "12", "/Users/<USER>/ADFD_Tracking_System/src/components/CTASection.tsx": "13", "/Users/<USER>/ADFD_Tracking_System/src/components/Footer.tsx": "14"}, {"size": 554, "mtime": 1753110560629, "results": "15", "hashOfConfig": "16"}, {"size": 425, "mtime": 1753110560630, "results": "17", "hashOfConfig": "16"}, {"size": 2108, "mtime": 1753164768914, "results": "18", "hashOfConfig": "16"}, {"size": 376, "mtime": 1753114336197, "results": "19", "hashOfConfig": "16"}, {"size": 879, "mtime": 1753164725282, "results": "20", "hashOfConfig": "16"}, {"size": 1654, "mtime": 1753164855056, "results": "21", "hashOfConfig": "16"}, {"size": 765, "mtime": 1753114365805, "results": "22", "hashOfConfig": "16"}, {"size": 2717, "mtime": 1753164813951, "results": "23", "hashOfConfig": "16"}, {"size": 1506, "mtime": 1753114397011, "results": "24", "hashOfConfig": "16"}, {"size": 2071, "mtime": 1753114412296, "results": "25", "hashOfConfig": "16"}, {"size": 2166, "mtime": 1753114425945, "results": "26", "hashOfConfig": "16"}, {"size": 3443, "mtime": 1753114447806, "results": "27", "hashOfConfig": "16"}, {"size": 720, "mtime": 1753114458877, "results": "28", "hashOfConfig": "16"}, {"size": 3758, "mtime": 1753115457064, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "f9paq8", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 33, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/ADFD_Tracking_System/src/index.tsx", [], [], "/Users/<USER>/ADFD_Tracking_System/src/reportWebVitals.ts", [], [], "/Users/<USER>/ADFD_Tracking_System/src/App.tsx", [], [], "/Users/<USER>/ADFD_Tracking_System/src/components/AnimatedBackground.tsx", [], [], "/Users/<USER>/ADFD_Tracking_System/src/components/Navigation.tsx", ["72"], [], "/Users/<USER>/ADFD_Tracking_System/src/components/HeroSection.tsx", [], [], "/Users/<USER>/ADFD_Tracking_System/src/components/StatsSection.tsx", [], [], "/Users/<USER>/ADFD_Tracking_System/src/components/FeaturesSection.tsx", [], [], "/Users/<USER>/ADFD_Tracking_System/src/components/WorkflowSection.tsx", [], [], "/Users/<USER>/ADFD_Tracking_System/src/components/RolesSection.tsx", [], [], "/Users/<USER>/ADFD_Tracking_System/src/components/TechnologySection.tsx", [], [], "/Users/<USER>/ADFD_Tracking_System/src/components/DemoSection.tsx", [], [], "/Users/<USER>/ADFD_Tracking_System/src/components/CTASection.tsx", ["73", "74"], [], "/Users/<USER>/ADFD_Tracking_System/src/components/Footer.tsx", ["75", "76", "77", "78", "79", "80", "81", "82", "83", "84", "85", "86", "87", "88", "89", "90", "91", "92", "93", "94", "95", "96", "97", "98", "99", "100", "101", "102", "103", "104", "105", "106", "107"], [], {"ruleId": "108", "severity": 1, "message": "109", "line": 8, "column": 11, "nodeType": "110", "endLine": 8, "endColumn": 41}, {"ruleId": "108", "severity": 1, "message": "109", "line": 12, "column": 11, "nodeType": "110", "endLine": 12, "endColumn": 45}, {"ruleId": "108", "severity": 1, "message": "109", "line": 15, "column": 11, "nodeType": "110", "endLine": 15, "endColumn": 47}, {"ruleId": "108", "severity": 1, "message": "109", "line": 22, "column": 15, "nodeType": "110", "endLine": 22, "endColumn": 51}, {"ruleId": "108", "severity": 1, "message": "109", "line": 23, "column": 15, "nodeType": "110", "endLine": 23, "endColumn": 51}, {"ruleId": "108", "severity": 1, "message": "109", "line": 24, "column": 15, "nodeType": "110", "endLine": 24, "endColumn": 51}, {"ruleId": "108", "severity": 1, "message": "109", "line": 25, "column": 15, "nodeType": "110", "endLine": 25, "endColumn": 51}, {"ruleId": "108", "severity": 1, "message": "109", "line": 33, "column": 21, "nodeType": "110", "endLine": 33, "endColumn": 33}, {"ruleId": "108", "severity": 1, "message": "109", "line": 34, "column": 21, "nodeType": "110", "endLine": 34, "endColumn": 33}, {"ruleId": "108", "severity": 1, "message": "109", "line": 35, "column": 21, "nodeType": "110", "endLine": 35, "endColumn": 33}, {"ruleId": "108", "severity": 1, "message": "109", "line": 36, "column": 21, "nodeType": "110", "endLine": 36, "endColumn": 33}, {"ruleId": "108", "severity": 1, "message": "109", "line": 37, "column": 21, "nodeType": "110", "endLine": 37, "endColumn": 33}, {"ruleId": "108", "severity": 1, "message": "109", "line": 44, "column": 21, "nodeType": "110", "endLine": 44, "endColumn": 33}, {"ruleId": "108", "severity": 1, "message": "109", "line": 45, "column": 21, "nodeType": "110", "endLine": 45, "endColumn": 33}, {"ruleId": "108", "severity": 1, "message": "109", "line": 46, "column": 21, "nodeType": "110", "endLine": 46, "endColumn": 33}, {"ruleId": "108", "severity": 1, "message": "109", "line": 47, "column": 21, "nodeType": "110", "endLine": 47, "endColumn": 33}, {"ruleId": "108", "severity": 1, "message": "109", "line": 48, "column": 21, "nodeType": "110", "endLine": 48, "endColumn": 33}, {"ruleId": "108", "severity": 1, "message": "109", "line": 55, "column": 21, "nodeType": "110", "endLine": 55, "endColumn": 33}, {"ruleId": "108", "severity": 1, "message": "109", "line": 56, "column": 21, "nodeType": "110", "endLine": 56, "endColumn": 33}, {"ruleId": "108", "severity": 1, "message": "109", "line": 57, "column": 21, "nodeType": "110", "endLine": 57, "endColumn": 33}, {"ruleId": "108", "severity": 1, "message": "109", "line": 58, "column": 21, "nodeType": "110", "endLine": 58, "endColumn": 33}, {"ruleId": "108", "severity": 1, "message": "109", "line": 59, "column": 21, "nodeType": "110", "endLine": 59, "endColumn": 33}, {"ruleId": "108", "severity": 1, "message": "109", "line": 66, "column": 21, "nodeType": "110", "endLine": 66, "endColumn": 33}, {"ruleId": "108", "severity": 1, "message": "109", "line": 67, "column": 21, "nodeType": "110", "endLine": 67, "endColumn": 33}, {"ruleId": "108", "severity": 1, "message": "109", "line": 68, "column": 21, "nodeType": "110", "endLine": 68, "endColumn": 33}, {"ruleId": "108", "severity": 1, "message": "109", "line": 69, "column": 21, "nodeType": "110", "endLine": 69, "endColumn": 33}, {"ruleId": "108", "severity": 1, "message": "109", "line": 70, "column": 21, "nodeType": "110", "endLine": 70, "endColumn": 33}, {"ruleId": "108", "severity": 1, "message": "109", "line": 77, "column": 21, "nodeType": "110", "endLine": 77, "endColumn": 33}, {"ruleId": "108", "severity": 1, "message": "109", "line": 78, "column": 21, "nodeType": "110", "endLine": 78, "endColumn": 33}, {"ruleId": "108", "severity": 1, "message": "109", "line": 79, "column": 21, "nodeType": "110", "endLine": 79, "endColumn": 33}, {"ruleId": "108", "severity": 1, "message": "109", "line": 80, "column": 21, "nodeType": "110", "endLine": 80, "endColumn": 33}, {"ruleId": "108", "severity": 1, "message": "109", "line": 81, "column": 21, "nodeType": "110", "endLine": 81, "endColumn": 33}, {"ruleId": "108", "severity": 1, "message": "109", "line": 92, "column": 17, "nodeType": "110", "endLine": 92, "endColumn": 29}, {"ruleId": "108", "severity": 1, "message": "109", "line": 93, "column": 17, "nodeType": "110", "endLine": 93, "endColumn": 29}, {"ruleId": "108", "severity": 1, "message": "109", "line": 94, "column": 17, "nodeType": "110", "endLine": 94, "endColumn": 29}, {"ruleId": "108", "severity": 1, "message": "109", "line": 95, "column": 17, "nodeType": "110", "endLine": 95, "endColumn": 29}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement"]