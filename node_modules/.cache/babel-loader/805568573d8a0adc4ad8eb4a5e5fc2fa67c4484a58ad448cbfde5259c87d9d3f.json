{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/ADFD_Tracking_System/src/components/Sections/TeamSection.tsx\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { Users, Eye, Building, CreditCard, ArrowRight, Mail } from 'lucide-react';\nimport Card from '../UI/Card';\nimport Button from '../UI/Button';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TeamSection = () => {\n  const teamRoles = [{\n    role: 'Archive Team',\n    icon: Building,\n    color: 'from-blue-500 to-blue-600',\n    description: 'Create and submit withdrawal requests with AI-powered OCR assistance',\n    permissions: ['Create Requests', 'Upload Documents', 'OCR Processing', 'View Own Requests'],\n    members: [{\n      name: '<PERSON>',\n      email: '<EMAIL>',\n      region: undefined,\n      title: undefined\n    }, {\n      name: '<PERSON><PERSON>',\n      email: 'mkalm<PERSON><EMAIL>',\n      region: undefined,\n      title: undefined\n    }],\n    ctaText: 'Access Archive Portal',\n    ctaAction: 'archive-login'\n  }, {\n    role: 'Operations Teams',\n    icon: Users,\n    color: 'from-green-500 to-green-600',\n    description: 'Review and approve/reject requests within assigned regional scope',\n    permissions: ['Regional Access', 'Approve/Reject', 'Technical Review', 'Comment & Track'],\n    members: [{\n      name: 'Ali Al Derie',\n      email: '<EMAIL>',\n      region: 'Europe & Latin America',\n      title: undefined\n    }, {\n      name: 'Ahmed Al Kalbani',\n      email: '<EMAIL>',\n      region: 'Africa',\n      title: undefined\n    }, {\n      name: 'Abdulla Al Mansoori',\n      email: '<EMAIL>',\n      region: 'Asia',\n      title: undefined\n    }],\n    ctaText: 'Operations Dashboard',\n    ctaAction: 'operations-login'\n  }, {\n    role: 'Core Banking Team',\n    icon: CreditCard,\n    color: 'from-purple-500 to-purple-600',\n    description: 'Process final disbursements and complete withdrawal requests',\n    permissions: ['Disburse Funds', 'Final Approval', 'Banking Integration', 'Completion Tracking'],\n    members: [{\n      name: 'Ahmed Siddique',\n      email: '<EMAIL>',\n      region: undefined,\n      title: undefined\n    }, {\n      name: 'Yazan Jamous',\n      email: '<EMAIL>',\n      region: undefined,\n      title: undefined\n    }, {\n      name: 'Ameer Hamza',\n      email: '<EMAIL>',\n      region: undefined,\n      title: undefined\n    }],\n    ctaText: 'Banking Portal',\n    ctaAction: 'banking-login'\n  }, {\n    role: 'Observers',\n    icon: Eye,\n    color: 'from-orange-500 to-orange-600',\n    description: 'Monitor system-wide operations with comprehensive oversight access',\n    permissions: ['View All Requests', 'Global Analytics', 'Audit Reports', 'Comment & Coordinate'],\n    members: [{\n      name: 'Fatima Al Hammadi',\n      email: '<EMAIL>',\n      region: undefined,\n      title: 'Loan Administrator'\n    }, {\n      name: 'Adel Al Hosani',\n      email: '<EMAIL>',\n      region: undefined,\n      title: 'Head of Operations'\n    }],\n    ctaText: 'Observer Dashboard',\n    ctaAction: 'observer-login'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"team\",\n    className: \"section-padding bg-white\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-custom\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 30\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8\n        },\n        className: \"text-center mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-4xl md:text-5xl font-bold text-navy-900 mb-6\",\n          children: [\"ADFD Team\", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"gradient-text block\",\n            children: \"Access Portals\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-navy-600 max-w-3xl mx-auto leading-relaxed\",\n          children: \"Role-based access portals designed for each team member's specific responsibilities and regional assignments within the Abu Dhabi Fund for Development.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16\",\n        children: teamRoles.map((team, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: index * 0.1\n          },\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"p-8 h-full group hover:shadow-2xl transition-all duration-300\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start space-x-4 mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-14 h-14 bg-gradient-to-r ${team.color} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`,\n                children: /*#__PURE__*/_jsxDEV(team.icon, {\n                  className: \"w-7 h-7 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 97,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-2xl font-bold text-navy-900 mb-2\",\n                  children: team.role\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 100,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-navy-600 leading-relaxed\",\n                  children: team.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 103,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 99,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 95,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-sm font-semibold text-navy-700 mb-3 uppercase tracking-wide\",\n                children: \"Key Permissions\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 111,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"grid grid-cols-2 gap-2\",\n                children: team.permissions.map(permission => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center text-sm text-navy-600\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-1.5 h-1.5 bg-primary-500 rounded-full mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 117,\n                    columnNumber: 25\n                  }, this), permission]\n                }, permission, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 116,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 114,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 110,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"mb-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-sm font-semibold text-navy-700 mb-3 uppercase tracking-wide\",\n                children: \"Team Members\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: team.members.map(member => /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between bg-navy-50 rounded-lg p-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"font-medium text-navy-900\",\n                      children: member.name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 133,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-navy-600\",\n                      children: member.email\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 134,\n                      columnNumber: 27\n                    }, this), member.region && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-primary-600 font-medium\",\n                      children: member.region\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 136,\n                      columnNumber: 29\n                    }, this), member.title && /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-xs text-gold-600 font-medium\",\n                      children: member.title\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 139,\n                      columnNumber: 29\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 132,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Mail, {\n                    className: \"w-4 h-4 text-navy-400\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 142,\n                    columnNumber: 25\n                  }, this)]\n                }, member.email, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 131,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              variant: \"primary\",\n              className: `w-full bg-gradient-to-r ${team.color} hover:opacity-90`,\n              icon: ArrowRight,\n              iconPosition: \"right\",\n              children: team.ctaText\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 15\n          }, this)\n        }, team.role, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 30\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"p-8 bg-gradient-to-r from-navy-50 to-primary-50 border-navy-200\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-2xl font-bold text-navy-900 mb-4\",\n              children: \"Secure System Access\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-navy-600 mb-6 max-w-3xl mx-auto\",\n              children: \"All team members access the system through secure, role-based portals with multi-factor authentication and regional access controls. Contact your system administrator for account setup and access credentials.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 173,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center mx-auto mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(Users, {\n                    className: \"w-6 h-6 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 182,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-semibold text-navy-900 mb-1\",\n                  children: \"10 Team Members\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-navy-600\",\n                  children: \"Across 4 specialized roles\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 185,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 180,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mx-auto mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(Eye, {\n                    className: \"w-6 h-6 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 190,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-semibold text-navy-900 mb-1\",\n                  children: \"Role-Based Access\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-navy-600\",\n                  children: \"Strict permission controls\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mx-auto mb-3\",\n                  children: /*#__PURE__*/_jsxDEV(Building, {\n                    className: \"w-6 h-6 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 198,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 197,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"font-semibold text-navy-900 mb-1\",\n                  children: \"3 Global Regions\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 200,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-navy-600\",\n                  children: \"Regional team assignments\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 201,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 179,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 168,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 163,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 67,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 5\n  }, this);\n};\n_c = TeamSection;\nexport default TeamSection;\nvar _c;\n$RefreshReg$(_c, \"TeamSection\");", "map": {"version": 3, "names": ["React", "motion", "Users", "Eye", "Building", "CreditCard", "ArrowRight", "Mail", "Card", "<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "TeamSection", "teamRoles", "role", "icon", "color", "description", "permissions", "members", "name", "email", "region", "undefined", "title", "ctaText", "ctaAction", "id", "className", "children", "div", "initial", "opacity", "y", "whileInView", "transition", "duration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "team", "index", "delay", "permission", "member", "variant", "iconPosition", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/ADFD_Tracking_System/src/components/Sections/TeamSection.tsx"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { Users, Eye, Building, CreditCard, ArrowRight, Mail } from 'lucide-react';\nimport Card from '../UI/Card';\nimport Button from '../UI/Button';\n\nconst TeamSection: React.FC = () => {\n  const teamRoles = [\n    {\n      role: 'Archive Team',\n      icon: Building,\n      color: 'from-blue-500 to-blue-600',\n      description: 'Create and submit withdrawal requests with AI-powered OCR assistance',\n      permissions: ['Create Requests', 'Upload Documents', 'OCR Processing', 'View Own Requests'],\n      members: [\n        { name: '<PERSON>', email: '<EMAIL>', region: undefined, title: undefined },\n        { name: '<PERSON><PERSON>', email: 'mkalm<PERSON><PERSON>@adfd.ae', region: undefined, title: undefined }\n      ],\n      ctaText: 'Access Archive Portal',\n      ctaAction: 'archive-login'\n    },\n    {\n      role: 'Operations Teams',\n      icon: Users,\n      color: 'from-green-500 to-green-600',\n      description: 'Review and approve/reject requests within assigned regional scope',\n      permissions: ['Regional Access', 'Approve/Reject', 'Technical Review', 'Comment & Track'],\n      members: [\n        { name: '<PERSON>', email: '<EMAIL>', region: 'Europe & Latin America', title: undefined },\n        { name: 'Ahmed Al Kalbani', email: '<EMAIL>', region: 'Africa', title: undefined },\n        { name: 'Abdulla Al Mansoori', email: '<EMAIL>', region: 'Asia', title: undefined }\n      ],\n      ctaText: 'Operations Dashboard',\n      ctaAction: 'operations-login'\n    },\n    {\n      role: 'Core Banking Team',\n      icon: CreditCard,\n      color: 'from-purple-500 to-purple-600',\n      description: 'Process final disbursements and complete withdrawal requests',\n      permissions: ['Disburse Funds', 'Final Approval', 'Banking Integration', 'Completion Tracking'],\n      members: [\n        { name: 'Ahmed Siddique', email: '<EMAIL>', region: undefined, title: undefined },\n        { name: 'Yazan Jamous', email: '<EMAIL>', region: undefined, title: undefined },\n        { name: 'Ameer Hamza', email: '<EMAIL>', region: undefined, title: undefined }\n      ],\n      ctaText: 'Banking Portal',\n      ctaAction: 'banking-login'\n    },\n    {\n      role: 'Observers',\n      icon: Eye,\n      color: 'from-orange-500 to-orange-600',\n      description: 'Monitor system-wide operations with comprehensive oversight access',\n      permissions: ['View All Requests', 'Global Analytics', 'Audit Reports', 'Comment & Coordinate'],\n      members: [\n        { name: 'Fatima Al Hammadi', email: '<EMAIL>', region: undefined, title: 'Loan Administrator' },\n        { name: 'Adel Al Hosani', email: '<EMAIL>', region: undefined, title: 'Head of Operations' }\n      ],\n      ctaText: 'Observer Dashboard',\n      ctaAction: 'observer-login'\n    }\n  ];\n\n  return (\n    <section id=\"team\" className=\"section-padding bg-white\">\n      <div className=\"container-custom\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl md:text-5xl font-bold text-navy-900 mb-6\">\n            ADFD Team\n            <span className=\"gradient-text block\">Access Portals</span>\n          </h2>\n          <p className=\"text-xl text-navy-600 max-w-3xl mx-auto leading-relaxed\">\n            Role-based access portals designed for each team member's specific responsibilities \n            and regional assignments within the Abu Dhabi Fund for Development.\n          </p>\n        </motion.div>\n\n        {/* Team Roles Grid */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16\">\n          {teamRoles.map((team, index) => (\n            <motion.div\n              key={team.role}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n            >\n              <Card className=\"p-8 h-full group hover:shadow-2xl transition-all duration-300\">\n                <div className=\"flex items-start space-x-4 mb-6\">\n                  <div className={`w-14 h-14 bg-gradient-to-r ${team.color} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>\n                    <team.icon className=\"w-7 h-7 text-white\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <h3 className=\"text-2xl font-bold text-navy-900 mb-2\">\n                      {team.role}\n                    </h3>\n                    <p className=\"text-navy-600 leading-relaxed\">\n                      {team.description}\n                    </p>\n                  </div>\n                </div>\n\n                {/* Permissions */}\n                <div className=\"mb-6\">\n                  <h4 className=\"text-sm font-semibold text-navy-700 mb-3 uppercase tracking-wide\">\n                    Key Permissions\n                  </h4>\n                  <div className=\"grid grid-cols-2 gap-2\">\n                    {team.permissions.map((permission) => (\n                      <div key={permission} className=\"flex items-center text-sm text-navy-600\">\n                        <div className=\"w-1.5 h-1.5 bg-primary-500 rounded-full mr-2\"></div>\n                        {permission}\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Team Members */}\n                <div className=\"mb-6\">\n                  <h4 className=\"text-sm font-semibold text-navy-700 mb-3 uppercase tracking-wide\">\n                    Team Members\n                  </h4>\n                  <div className=\"space-y-2\">\n                    {team.members.map((member) => (\n                      <div key={member.email} className=\"flex items-center justify-between bg-navy-50 rounded-lg p-3\">\n                        <div>\n                          <div className=\"font-medium text-navy-900\">{member.name}</div>\n                          <div className=\"text-sm text-navy-600\">{member.email}</div>\n                          {member.region && (\n                            <div className=\"text-xs text-primary-600 font-medium\">{member.region}</div>\n                          )}\n                          {member.title && (\n                            <div className=\"text-xs text-gold-600 font-medium\">{member.title}</div>\n                          )}\n                        </div>\n                        <Mail className=\"w-4 h-4 text-navy-400\" />\n                      </div>\n                    ))}\n                  </div>\n                </div>\n\n                {/* CTA Button */}\n                <Button\n                  variant=\"primary\"\n                  className={`w-full bg-gradient-to-r ${team.color} hover:opacity-90`}\n                  icon={ArrowRight}\n                  iconPosition=\"right\"\n                >\n                  {team.ctaText}\n                </Button>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* System Access Information */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n        >\n          <Card className=\"p-8 bg-gradient-to-r from-navy-50 to-primary-50 border-navy-200\">\n            <div className=\"text-center\">\n              <h3 className=\"text-2xl font-bold text-navy-900 mb-4\">\n                Secure System Access\n              </h3>\n              <p className=\"text-navy-600 mb-6 max-w-3xl mx-auto\">\n                All team members access the system through secure, role-based portals with \n                multi-factor authentication and regional access controls. Contact your system \n                administrator for account setup and access credentials.\n              </p>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                <div className=\"text-center\">\n                  <div className=\"w-12 h-12 bg-gradient-to-r from-green-500 to-green-600 rounded-lg flex items-center justify-center mx-auto mb-3\">\n                    <Users className=\"w-6 h-6 text-white\" />\n                  </div>\n                  <h4 className=\"font-semibold text-navy-900 mb-1\">10 Team Members</h4>\n                  <p className=\"text-sm text-navy-600\">Across 4 specialized roles</p>\n                </div>\n                \n                <div className=\"text-center\">\n                  <div className=\"w-12 h-12 bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mx-auto mb-3\">\n                    <Eye className=\"w-6 h-6 text-white\" />\n                  </div>\n                  <h4 className=\"font-semibold text-navy-900 mb-1\">Role-Based Access</h4>\n                  <p className=\"text-sm text-navy-600\">Strict permission controls</p>\n                </div>\n                \n                <div className=\"text-center\">\n                  <div className=\"w-12 h-12 bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mx-auto mb-3\">\n                    <Building className=\"w-6 h-6 text-white\" />\n                  </div>\n                  <h4 className=\"font-semibold text-navy-900 mb-1\">3 Global Regions</h4>\n                  <p className=\"text-sm text-navy-600\">Regional team assignments</p>\n                </div>\n              </div>\n            </div>\n          </Card>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default TeamSection;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,KAAK,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,UAAU,EAAEC,UAAU,EAAEC,IAAI,QAAQ,cAAc;AACjF,OAAOC,IAAI,MAAM,YAAY;AAC7B,OAAOC,MAAM,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElC,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAClC,MAAMC,SAAS,GAAG,CAChB;IACEC,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAEX,QAAQ;IACdY,KAAK,EAAE,2BAA2B;IAClCC,WAAW,EAAE,sEAAsE;IACnFC,WAAW,EAAE,CAAC,iBAAiB,EAAE,kBAAkB,EAAE,gBAAgB,EAAE,mBAAmB,CAAC;IAC3FC,OAAO,EAAE,CACP;MAAEC,IAAI,EAAE,gBAAgB;MAAEC,KAAK,EAAE,kBAAkB;MAAEC,MAAM,EAAEC,SAAS;MAAEC,KAAK,EAAED;IAAU,CAAC,EAC1F;MAAEH,IAAI,EAAE,kBAAkB;MAAEC,KAAK,EAAE,oBAAoB;MAAEC,MAAM,EAAEC,SAAS;MAAEC,KAAK,EAAED;IAAU,CAAC,CAC/F;IACDE,OAAO,EAAE,uBAAuB;IAChCC,SAAS,EAAE;EACb,CAAC,EACD;IACEZ,IAAI,EAAE,kBAAkB;IACxBC,IAAI,EAAEb,KAAK;IACXc,KAAK,EAAE,6BAA6B;IACpCC,WAAW,EAAE,mEAAmE;IAChFC,WAAW,EAAE,CAAC,iBAAiB,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,iBAAiB,CAAC;IACzFC,OAAO,EAAE,CACP;MAAEC,IAAI,EAAE,cAAc;MAAEC,KAAK,EAAE,kBAAkB;MAAEC,MAAM,EAAE,wBAAwB;MAAEE,KAAK,EAAED;IAAU,CAAC,EACvG;MAAEH,IAAI,EAAE,kBAAkB;MAAEC,KAAK,EAAE,oBAAoB;MAAEC,MAAM,EAAE,QAAQ;MAAEE,KAAK,EAAED;IAAU,CAAC,EAC7F;MAAEH,IAAI,EAAE,qBAAqB;MAAEC,KAAK,EAAE,sBAAsB;MAAEC,MAAM,EAAE,MAAM;MAAEE,KAAK,EAAED;IAAU,CAAC,CACjG;IACDE,OAAO,EAAE,sBAAsB;IAC/BC,SAAS,EAAE;EACb,CAAC,EACD;IACEZ,IAAI,EAAE,mBAAmB;IACzBC,IAAI,EAAEV,UAAU;IAChBW,KAAK,EAAE,+BAA+B;IACtCC,WAAW,EAAE,8DAA8D;IAC3EC,WAAW,EAAE,CAAC,gBAAgB,EAAE,gBAAgB,EAAE,qBAAqB,EAAE,qBAAqB,CAAC;IAC/FC,OAAO,EAAE,CACP;MAAEC,IAAI,EAAE,gBAAgB;MAAEC,KAAK,EAAE,mBAAmB;MAAEC,MAAM,EAAEC,SAAS;MAAEC,KAAK,EAAED;IAAU,CAAC,EAC3F;MAAEH,IAAI,EAAE,cAAc;MAAEC,KAAK,EAAE,iBAAiB;MAAEC,MAAM,EAAEC,SAAS;MAAEC,KAAK,EAAED;IAAU,CAAC,EACvF;MAAEH,IAAI,EAAE,aAAa;MAAEC,KAAK,EAAE,oBAAoB;MAAEC,MAAM,EAAEC,SAAS;MAAEC,KAAK,EAAED;IAAU,CAAC,CAC1F;IACDE,OAAO,EAAE,gBAAgB;IACzBC,SAAS,EAAE;EACb,CAAC,EACD;IACEZ,IAAI,EAAE,WAAW;IACjBC,IAAI,EAAEZ,GAAG;IACTa,KAAK,EAAE,+BAA+B;IACtCC,WAAW,EAAE,oEAAoE;IACjFC,WAAW,EAAE,CAAC,mBAAmB,EAAE,kBAAkB,EAAE,eAAe,EAAE,sBAAsB,CAAC;IAC/FC,OAAO,EAAE,CACP;MAAEC,IAAI,EAAE,mBAAmB;MAAEC,KAAK,EAAE,mBAAmB;MAAEC,MAAM,EAAEC,SAAS;MAAEC,KAAK,EAAE;IAAqB,CAAC,EACzG;MAAEJ,IAAI,EAAE,gBAAgB;MAAEC,KAAK,EAAE,oBAAoB;MAAEC,MAAM,EAAEC,SAAS;MAAEC,KAAK,EAAE;IAAqB,CAAC,CACxG;IACDC,OAAO,EAAE,oBAAoB;IAC7BC,SAAS,EAAE;EACb,CAAC,CACF;EAED,oBACEf,OAAA;IAASgB,EAAE,EAAC,MAAM;IAACC,SAAS,EAAC,0BAA0B;IAAAC,QAAA,eACrDlB,OAAA;MAAKiB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAE/BlB,OAAA,CAACV,MAAM,CAAC6B,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BR,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAE7BlB,OAAA;UAAIiB,SAAS,EAAC,mDAAmD;UAAAC,QAAA,GAAC,WAEhE,eAAAlB,OAAA;YAAMiB,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAAc;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACL7B,OAAA;UAAGiB,SAAS,EAAC,yDAAyD;UAAAC,QAAA,EAAC;QAGvE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGb7B,OAAA;QAAKiB,SAAS,EAAC,6CAA6C;QAAAC,QAAA,EACzDhB,SAAS,CAAC4B,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACzBhC,OAAA,CAACV,MAAM,CAAC6B,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,WAAW,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEQ,KAAK,EAAED,KAAK,GAAG;UAAI,CAAE;UAAAd,QAAA,eAElDlB,OAAA,CAACH,IAAI;YAACoB,SAAS,EAAC,+DAA+D;YAAAC,QAAA,gBAC7ElB,OAAA;cAAKiB,SAAS,EAAC,iCAAiC;cAAAC,QAAA,gBAC9ClB,OAAA;gBAAKiB,SAAS,EAAE,8BAA8Bc,IAAI,CAAC1B,KAAK,sGAAuG;gBAAAa,QAAA,eAC7JlB,OAAA,CAAC+B,IAAI,CAAC3B,IAAI;kBAACa,SAAS,EAAC;gBAAoB;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC,CAAC,eACN7B,OAAA;gBAAKiB,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBlB,OAAA;kBAAIiB,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,EAClDa,IAAI,CAAC5B;gBAAI;kBAAAuB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACR,CAAC,eACL7B,OAAA;kBAAGiB,SAAS,EAAC,+BAA+B;kBAAAC,QAAA,EACzCa,IAAI,CAACzB;gBAAW;kBAAAoB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChB,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN7B,OAAA;cAAKiB,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBlB,OAAA;gBAAIiB,SAAS,EAAC,kEAAkE;gBAAAC,QAAA,EAAC;cAEjF;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL7B,OAAA;gBAAKiB,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EACpCa,IAAI,CAACxB,WAAW,CAACuB,GAAG,CAAEI,UAAU,iBAC/BlC,OAAA;kBAAsBiB,SAAS,EAAC,yCAAyC;kBAAAC,QAAA,gBACvElB,OAAA;oBAAKiB,SAAS,EAAC;kBAA8C;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EACnEK,UAAU;gBAAA,GAFHA,UAAU;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAGf,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN7B,OAAA;cAAKiB,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBlB,OAAA;gBAAIiB,SAAS,EAAC,kEAAkE;gBAAAC,QAAA,EAAC;cAEjF;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACL7B,OAAA;gBAAKiB,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACvBa,IAAI,CAACvB,OAAO,CAACsB,GAAG,CAAEK,MAAM,iBACvBnC,OAAA;kBAAwBiB,SAAS,EAAC,6DAA6D;kBAAAC,QAAA,gBAC7FlB,OAAA;oBAAAkB,QAAA,gBACElB,OAAA;sBAAKiB,SAAS,EAAC,2BAA2B;sBAAAC,QAAA,EAAEiB,MAAM,CAAC1B;oBAAI;sBAAAiB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eAC9D7B,OAAA;sBAAKiB,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAEiB,MAAM,CAACzB;oBAAK;sBAAAgB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,EAC1DM,MAAM,CAACxB,MAAM,iBACZX,OAAA;sBAAKiB,SAAS,EAAC,sCAAsC;sBAAAC,QAAA,EAAEiB,MAAM,CAACxB;oBAAM;sBAAAe,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAC3E,EACAM,MAAM,CAACtB,KAAK,iBACXb,OAAA;sBAAKiB,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAAEiB,MAAM,CAACtB;oBAAK;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CACvE;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC,eACN7B,OAAA,CAACJ,IAAI;oBAACqB,SAAS,EAAC;kBAAuB;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC;gBAAA,GAXlCM,MAAM,CAACzB,KAAK;kBAAAgB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAYjB,CACN;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGN7B,OAAA,CAACF,MAAM;cACLsC,OAAO,EAAC,SAAS;cACjBnB,SAAS,EAAE,2BAA2Bc,IAAI,CAAC1B,KAAK,mBAAoB;cACpED,IAAI,EAAET,UAAW;cACjB0C,YAAY,EAAC,OAAO;cAAAnB,QAAA,EAEnBa,IAAI,CAACjB;YAAO;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC,GApEFE,IAAI,CAAC5B,IAAI;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAqEJ,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN7B,OAAA,CAACV,MAAM,CAAC6B,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAAAP,QAAA,eAE9BlB,OAAA,CAACH,IAAI;UAACoB,SAAS,EAAC,iEAAiE;UAAAC,QAAA,eAC/ElB,OAAA;YAAKiB,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BlB,OAAA;cAAIiB,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EAAC;YAEtD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL7B,OAAA;cAAGiB,SAAS,EAAC,sCAAsC;cAAAC,QAAA,EAAC;YAIpD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJ7B,OAAA;cAAKiB,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDlB,OAAA;gBAAKiB,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BlB,OAAA;kBAAKiB,SAAS,EAAC,iHAAiH;kBAAAC,QAAA,eAC9HlB,OAAA,CAACT,KAAK;oBAAC0B,SAAS,EAAC;kBAAoB;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACrC,CAAC,eACN7B,OAAA;kBAAIiB,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAe;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrE7B,OAAA;kBAAGiB,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAA0B;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC,eAEN7B,OAAA;gBAAKiB,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BlB,OAAA;kBAAKiB,SAAS,EAAC,+GAA+G;kBAAAC,QAAA,eAC5HlB,OAAA,CAACR,GAAG;oBAACyB,SAAS,EAAC;kBAAoB;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACN7B,OAAA;kBAAIiB,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvE7B,OAAA;kBAAGiB,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAA0B;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChE,CAAC,eAEN7B,OAAA;gBAAKiB,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1BlB,OAAA;kBAAKiB,SAAS,EAAC,mHAAmH;kBAAAC,QAAA,eAChIlB,OAAA,CAACP,QAAQ;oBAACwB,SAAS,EAAC;kBAAoB;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxC,CAAC,eACN7B,OAAA;kBAAIiB,SAAS,EAAC,kCAAkC;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACtE7B,OAAA;kBAAGiB,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAyB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACS,EAAA,GA3MIrC,WAAqB;AA6M3B,eAAeA,WAAW;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}