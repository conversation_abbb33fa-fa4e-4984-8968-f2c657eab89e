{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/ADFD_Tracking_System/src/components/Footer.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Footer = () => {\n  const footerSections = [{\n    title: 'FinTrack System',\n    content: /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        style: {\n          color: '#9ca3af',\n          marginBottom: '1rem'\n        },\n        children: \"Empowering development through intelligent financial technology.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          gap: '1rem'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"#\",\n          style: {\n            color: '#9ca3af'\n          },\n          children: \"\\uD83D\\uDCBC\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"#\",\n          style: {\n            color: '#9ca3af'\n          },\n          children: \"\\uD83D\\uDC26\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"#\",\n          style: {\n            color: '#9ca3af'\n          },\n          children: \"\\u2709\\uFE0F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true)\n  }, {\n    title: 'Product',\n    links: [{\n      name: 'Features',\n      href: '#features'\n    }, {\n      name: 'Workflow',\n      href: '#workflow'\n    }, {\n      name: 'Technology',\n      href: '#technology'\n    }, {\n      name: 'Demo',\n      href: '#demo'\n    }]\n  }, {\n    title: 'Support',\n    links: [{\n      name: 'Documentation',\n      href: '#'\n    }, {\n      name: 'Training',\n      href: '#'\n    }, {\n      name: 'API Reference',\n      href: '#'\n    }, {\n      name: 'Contact Support',\n      href: '#'\n    }]\n  }, {\n    title: 'Security',\n    links: [{\n      name: 'Privacy Policy',\n      href: '#'\n    }, {\n      name: 'Terms of Service',\n      href: '#'\n    }, {\n      name: 'Security Standards',\n      href: '#'\n    }, {\n      name: 'Compliance',\n      href: '#'\n    }]\n  }];\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-content\",\n        children: footerSections.map((section, index) => {\n          var _section$links;\n          return /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"footer-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: section.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 15\n            }, this), section.content ? section.content : /*#__PURE__*/_jsxDEV(\"ul\", {\n              children: (_section$links = section.links) === null || _section$links === void 0 ? void 0 : _section$links.map((link, linkIndex) => /*#__PURE__*/_jsxDEV(\"li\", {\n                children: /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: link.href,\n                  children: link.name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 62,\n                  columnNumber: 23\n                }, this)\n              }, linkIndex, false, {\n                fileName: _jsxFileName,\n                lineNumber: 61,\n                columnNumber: 21\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 13\n          }, this);\n        })\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"footer-bottom\",\n        children: /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"\\xA9 2025 FinTrack System. All rights reserved. Built for enterprise financial operations.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 50,\n    columnNumber: 5\n  }, this);\n};\n_c = Footer;\nexport default Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Footer", "footerSections", "title", "content", "children", "style", "color", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "display", "gap", "href", "links", "name", "className", "map", "section", "index", "_section$links", "link", "linkIndex", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/ADFD_Tracking_System/src/components/Footer.tsx"], "sourcesContent": ["import React from 'react';\n\nconst Footer: React.FC = () => {\n  const footerSections = [\n    {\n      title: 'FinTrack System',\n      content: (\n        <>\n          <p style={{ color: '#9ca3af', marginBottom: '1rem' }}>\n            Empowering development through intelligent financial technology.\n          </p>\n          <div style={{ display: 'flex', gap: '1rem' }}>\n            <a href=\"#\" style={{ color: '#9ca3af' }}>💼</a>\n            <a href=\"#\" style={{ color: '#9ca3af' }}>🐦</a>\n            <a href=\"#\" style={{ color: '#9ca3af' }}>✉️</a>\n          </div>\n        </>\n      )\n    },\n    {\n      title: 'Product',\n      links: [\n        { name: 'Features', href: '#features' },\n        { name: 'Workflow', href: '#workflow' },\n        { name: 'Technology', href: '#technology' },\n        { name: 'Demo', href: '#demo' }\n      ]\n    },\n    {\n      title: 'Support',\n      links: [\n        { name: 'Documentation', href: '#' },\n        { name: 'Training', href: '#' },\n        { name: 'API Reference', href: '#' },\n        { name: 'Contact Support', href: '#' }\n      ]\n    },\n    {\n      title: 'Security',\n      links: [\n        { name: 'Privacy Policy', href: '#' },\n        { name: 'Terms of Service', href: '#' },\n        { name: 'Security Standards', href: '#' },\n        { name: 'Compliance', href: '#' }\n      ]\n    }\n  ];\n\n  return (\n    <footer>\n      <div className=\"container\">\n        <div className=\"footer-content\">\n          {footerSections.map((section, index) => (\n            <div key={index} className=\"footer-section\">\n              <h4>{section.title}</h4>\n              {section.content ? (\n                section.content\n              ) : (\n                <ul>\n                  {section.links?.map((link, linkIndex) => (\n                    <li key={linkIndex}>\n                      <a href={link.href}>{link.name}</a>\n                    </li>\n                  ))}\n                </ul>\n              )}\n            </div>\n          ))}\n        </div>\n        <div className=\"footer-bottom\">\n          <p>&copy; 2025 FinTrack System. All rights reserved. Built for enterprise financial operations.</p>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAMC,MAAgB,GAAGA,CAAA,KAAM;EAC7B,MAAMC,cAAc,GAAG,CACrB;IACEC,KAAK,EAAE,iBAAiB;IACxBC,OAAO,eACLN,OAAA,CAAAE,SAAA;MAAAK,QAAA,gBACEP,OAAA;QAAGQ,KAAK,EAAE;UAAEC,KAAK,EAAE,SAAS;UAAEC,YAAY,EAAE;QAAO,CAAE;QAAAH,QAAA,EAAC;MAEtD;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJd,OAAA;QAAKQ,KAAK,EAAE;UAAEO,OAAO,EAAE,MAAM;UAAEC,GAAG,EAAE;QAAO,CAAE;QAAAT,QAAA,gBAC3CP,OAAA;UAAGiB,IAAI,EAAC,GAAG;UAACT,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAF,QAAA,EAAC;QAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC/Cd,OAAA;UAAGiB,IAAI,EAAC,GAAG;UAACT,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAF,QAAA,EAAC;QAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAC/Cd,OAAA;UAAGiB,IAAI,EAAC,GAAG;UAACT,KAAK,EAAE;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAAF,QAAA,EAAC;QAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5C,CAAC;IAAA,eACN;EAEN,CAAC,EACD;IACET,KAAK,EAAE,SAAS;IAChBa,KAAK,EAAE,CACL;MAAEC,IAAI,EAAE,UAAU;MAAEF,IAAI,EAAE;IAAY,CAAC,EACvC;MAAEE,IAAI,EAAE,UAAU;MAAEF,IAAI,EAAE;IAAY,CAAC,EACvC;MAAEE,IAAI,EAAE,YAAY;MAAEF,IAAI,EAAE;IAAc,CAAC,EAC3C;MAAEE,IAAI,EAAE,MAAM;MAAEF,IAAI,EAAE;IAAQ,CAAC;EAEnC,CAAC,EACD;IACEZ,KAAK,EAAE,SAAS;IAChBa,KAAK,EAAE,CACL;MAAEC,IAAI,EAAE,eAAe;MAAEF,IAAI,EAAE;IAAI,CAAC,EACpC;MAAEE,IAAI,EAAE,UAAU;MAAEF,IAAI,EAAE;IAAI,CAAC,EAC/B;MAAEE,IAAI,EAAE,eAAe;MAAEF,IAAI,EAAE;IAAI,CAAC,EACpC;MAAEE,IAAI,EAAE,iBAAiB;MAAEF,IAAI,EAAE;IAAI,CAAC;EAE1C,CAAC,EACD;IACEZ,KAAK,EAAE,UAAU;IACjBa,KAAK,EAAE,CACL;MAAEC,IAAI,EAAE,gBAAgB;MAAEF,IAAI,EAAE;IAAI,CAAC,EACrC;MAAEE,IAAI,EAAE,kBAAkB;MAAEF,IAAI,EAAE;IAAI,CAAC,EACvC;MAAEE,IAAI,EAAE,oBAAoB;MAAEF,IAAI,EAAE;IAAI,CAAC,EACzC;MAAEE,IAAI,EAAE,YAAY;MAAEF,IAAI,EAAE;IAAI,CAAC;EAErC,CAAC,CACF;EAED,oBACEjB,OAAA;IAAAO,QAAA,eACEP,OAAA;MAAKoB,SAAS,EAAC,WAAW;MAAAb,QAAA,gBACxBP,OAAA;QAAKoB,SAAS,EAAC,gBAAgB;QAAAb,QAAA,EAC5BH,cAAc,CAACiB,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK;UAAA,IAAAC,cAAA;UAAA,oBACjCxB,OAAA;YAAiBoB,SAAS,EAAC,gBAAgB;YAAAb,QAAA,gBACzCP,OAAA;cAAAO,QAAA,EAAKe,OAAO,CAACjB;YAAK;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,EACvBQ,OAAO,CAAChB,OAAO,GACdgB,OAAO,CAAChB,OAAO,gBAEfN,OAAA;cAAAO,QAAA,GAAAiB,cAAA,GACGF,OAAO,CAACJ,KAAK,cAAAM,cAAA,uBAAbA,cAAA,CAAeH,GAAG,CAAC,CAACI,IAAI,EAAEC,SAAS,kBAClC1B,OAAA;gBAAAO,QAAA,eACEP,OAAA;kBAAGiB,IAAI,EAAEQ,IAAI,CAACR,IAAK;kBAAAV,QAAA,EAAEkB,IAAI,CAACN;gBAAI;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI;cAAC,GAD5BY,SAAS;gBAAAf,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEd,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CACL;UAAA,GAZOS,KAAK;YAAAZ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAaV,CAAC;QAAA,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eACNd,OAAA;QAAKoB,SAAS,EAAC,eAAe;QAAAb,QAAA,eAC5BP,OAAA;UAAAO,QAAA,EAAG;QAA4F;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACa,EAAA,GAzEIxB,MAAgB;AA2EtB,eAAeA,MAAM;AAAC,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}