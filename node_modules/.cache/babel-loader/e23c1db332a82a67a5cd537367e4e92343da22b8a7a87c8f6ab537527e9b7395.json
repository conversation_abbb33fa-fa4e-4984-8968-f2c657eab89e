{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/ADFD_Tracking_System/src/components/Sections/SecuritySection.tsx\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { Shield, Lock, Eye, FileCheck, Database, Key, AlertTriangle, CheckCircle } from 'lucide-react';\nimport Card from '../UI/Card';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SecuritySection = () => {\n  const securityFeatures = [{\n    icon: Shield,\n    title: 'Banking-Grade Security',\n    description: 'Zero permission exceptions with enterprise-level security protocols',\n    features: ['Multi-layer Authentication', 'End-to-end Encryption', 'Secure API Endpoints', 'SSL/TLS Protection']\n  }, {\n    icon: Lock,\n    title: 'Role-Based Access Control',\n    description: 'Strict permissions based on user roles and regional assignments',\n    features: ['Request Creation', 'Review & Approval', 'Processing Rights', 'Observer Access']\n  }, {\n    icon: Database,\n    title: 'Row Level Security',\n    description: 'Database-level security policies ensuring data isolation',\n    features: ['Database RLS', 'User-based Filtering', 'Access Restrictions', 'Audit Compliance']\n  }, {\n    icon: Eye,\n    title: 'Comprehensive Auditing',\n    description: 'Complete audit trail for all system actions and changes',\n    features: ['Action Logging', 'User Tracking', 'Change History', 'Compliance Reports']\n  }];\n  const complianceStandards = [{\n    name: 'ISO 27001',\n    status: 'Compliant',\n    icon: CheckCircle\n  }, {\n    name: 'SOC 2 Type II',\n    status: 'Certified',\n    icon: CheckCircle\n  }, {\n    name: 'PCI DSS',\n    status: 'Compliant',\n    icon: CheckCircle\n  }, {\n    name: 'GDPR',\n    status: 'Compliant',\n    icon: CheckCircle\n  }];\n  const securityMetrics = [{\n    label: 'Uptime SLA',\n    value: '99.9%',\n    icon: CheckCircle\n  }, {\n    label: 'Data Encryption',\n    value: '256-bit',\n    icon: Lock\n  }, {\n    label: 'Security Incidents',\n    value: '0',\n    icon: Shield\n  }, {\n    label: 'Audit Frequency',\n    value: 'Real-time',\n    icon: Eye\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"security\",\n    className: \"section-padding bg-gray-900 text-white min-h-screen\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-custom\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 30\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8\n        },\n        className: \"text-center mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-4xl md:text-5xl font-bold mb-6 text-white\",\n          children: [\"Enterprise-Grade\", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gold-400 block\",\n            children: \"Security & Compliance\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed\",\n          children: \"Enterprise-level security architecture designed for financial institutions with zero-tolerance for security breaches and complete regulatory compliance.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 52,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 gap-8 mb-16\",\n        children: securityFeatures.map((feature, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: index * 0.1\n          },\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"p-8 bg-gray-800 border-gray-700 h-full\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-start space-x-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 bg-gradient-to-r from-gold-500 to-gold-600 rounded-lg flex items-center justify-center flex-shrink-0\",\n                children: /*#__PURE__*/_jsxDEV(feature.icon, {\n                  className: \"w-6 h-6 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 82,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 81,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                  className: \"text-xl font-bold text-white mb-3\",\n                  children: feature.title\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 85,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-300 mb-4 leading-relaxed\",\n                  children: feature.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 88,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n                  className: \"space-y-2\",\n                  children: feature.features.map((item, itemIndex) => /*#__PURE__*/_jsxDEV(motion.li, {\n                    initial: {\n                      opacity: 0,\n                      x: -10\n                    },\n                    whileInView: {\n                      opacity: 1,\n                      x: 0\n                    },\n                    transition: {\n                      delay: index * 0.1 + itemIndex * 0.05\n                    },\n                    className: \"flex items-center text-gray-200\",\n                    children: [/*#__PURE__*/_jsxDEV(CheckCircle, {\n                      className: \"w-4 h-4 text-green-400 mr-2 flex-shrink-0\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 100,\n                      columnNumber: 27\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-sm\",\n                      children: item\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 101,\n                      columnNumber: 27\n                    }, this)]\n                  }, item, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 93,\n                    columnNumber: 25\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 91,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 79,\n            columnNumber: 15\n          }, this)\n        }, feature.title, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 30\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8\n        },\n        className: \"mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-3xl font-bold text-center mb-8\",\n          children: \"Security Performance\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 119,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n          children: securityMetrics.map((metric, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              scale: 0.8\n            },\n            whileInView: {\n              opacity: 1,\n              scale: 1\n            },\n            transition: {\n              delay: index * 0.1\n            },\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: \"p-6 bg-gray-800 border-gray-700 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-12 h-12 bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg flex items-center justify-center mx-auto mb-4\",\n                children: /*#__PURE__*/_jsxDEV(metric.icon, {\n                  className: \"w-6 h-6 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 130,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 129,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-3xl font-bold text-gold-400 mb-2\",\n                children: metric.value\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 132,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-gray-300 text-sm\",\n                children: metric.label\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 135,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 17\n            }, this)\n          }, metric.label, false, {\n            fileName: _jsxFileName,\n            lineNumber: 122,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 113,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 30\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8\n        },\n        className: \"mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"text-3xl font-bold text-center mb-8\",\n          children: \"Compliance Standards\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-4 gap-6\",\n          children: complianceStandards.map((standard, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n            initial: {\n              opacity: 0,\n              y: 20\n            },\n            whileInView: {\n              opacity: 1,\n              y: 0\n            },\n            transition: {\n              delay: index * 0.1\n            },\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              className: \"p-6 bg-gray-800 border-gray-700 text-center\",\n              children: [/*#__PURE__*/_jsxDEV(standard.icon, {\n                className: \"w-8 h-8 text-green-400 mx-auto mb-3\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 161,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                className: \"text-lg font-bold text-white mb-2\",\n                children: standard.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"inline-block bg-green-500 text-white text-xs px-3 py-1 rounded-full\",\n                children: standard.status\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 165,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 17\n            }, this)\n          }, standard.name, false, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 152,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 30\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8\n        },\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"p-8 bg-gradient-to-r from-gray-800 to-gray-700 border-gray-600\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-3xl font-bold mb-6\",\n              children: \"Multi-Layer Security Architecture\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-gray-200 mb-8 max-w-3xl mx-auto\",\n              children: \"Our security architecture implements defense-in-depth principles with multiple layers of protection, ensuring your financial data remains secure at all times.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 185,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-3 gap-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-16 h-16 bg-gradient-to-r from-red-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-4\",\n                  children: /*#__PURE__*/_jsxDEV(AlertTriangle, {\n                    className: \"w-8 h-8 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-lg font-bold text-white mb-2\",\n                  children: \"Threat Detection\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-300 text-sm\",\n                  children: \"Real-time monitoring and automated threat detection with immediate response protocols.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 196,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4\",\n                  children: /*#__PURE__*/_jsxDEV(Key, {\n                    className: \"w-8 h-8 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 203,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-lg font-bold text-white mb-2\",\n                  children: \"Access Control\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 205,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-300 text-sm\",\n                  children: \"Multi-factor authentication and role-based permissions with regional restrictions.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 206,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"text-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4\",\n                  children: /*#__PURE__*/_jsxDEV(FileCheck, {\n                    className: \"w-8 h-8 text-white\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 213,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                  className: \"text-lg font-bold text-white mb-2\",\n                  children: \"Data Protection\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 215,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-gray-300 text-sm\",\n                  children: \"End-to-end encryption, secure backups, and comprehensive audit trails.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 216,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 50,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 49,\n    columnNumber: 5\n  }, this);\n};\n_c = SecuritySection;\nexport default SecuritySection;\nvar _c;\n$RefreshReg$(_c, \"SecuritySection\");", "map": {"version": 3, "names": ["React", "motion", "Shield", "Lock", "Eye", "FileCheck", "Database", "Key", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CheckCircle", "Card", "jsxDEV", "_jsxDEV", "SecuritySection", "securityFeatures", "icon", "title", "description", "features", "complianceStandards", "name", "status", "securityMetrics", "label", "value", "id", "className", "children", "div", "initial", "opacity", "y", "whileInView", "transition", "duration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "feature", "index", "delay", "item", "itemIndex", "li", "x", "metric", "scale", "standard", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/ADFD_Tracking_System/src/components/Sections/SecuritySection.tsx"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { Shield, Lock, Eye, FileCheck, Database, Key, AlertTriangle, CheckCircle } from 'lucide-react';\nimport Card from '../UI/Card';\n\nconst SecuritySection: React.FC = () => {\n  const securityFeatures = [\n    {\n      icon: Shield,\n      title: 'Banking-Grade Security',\n      description: 'Zero permission exceptions with enterprise-level security protocols',\n      features: ['Multi-layer Authentication', 'End-to-end Encryption', 'Secure API Endpoints', 'SSL/TLS Protection']\n    },\n    {\n      icon: Lock,\n      title: 'Role-Based Access Control',\n      description: 'Strict permissions based on user roles and regional assignments',\n      features: ['Request Creation', 'Review & Approval', 'Processing Rights', 'Observer Access']\n    },\n    {\n      icon: Database,\n      title: 'Row Level Security',\n      description: 'Database-level security policies ensuring data isolation',\n      features: ['Database RLS', 'User-based Filtering', 'Access Restrictions', 'Audit Compliance']\n    },\n    {\n      icon: Eye,\n      title: 'Comprehensive Auditing',\n      description: 'Complete audit trail for all system actions and changes',\n      features: ['Action Logging', 'User Tracking', 'Change History', 'Compliance Reports']\n    }\n  ];\n\n  const complianceStandards = [\n    { name: 'ISO 27001', status: 'Compliant', icon: CheckCircle },\n    { name: 'SOC 2 Type II', status: 'Certified', icon: CheckCircle },\n    { name: 'PCI DSS', status: 'Compliant', icon: CheckCircle },\n    { name: 'GDPR', status: 'Compliant', icon: CheckCircle },\n  ];\n\n  const securityMetrics = [\n    { label: 'Uptime SLA', value: '99.9%', icon: CheckCircle },\n    { label: 'Data Encryption', value: '256-bit', icon: Lock },\n    { label: 'Security Incidents', value: '0', icon: Shield },\n    { label: 'Audit Frequency', value: 'Real-time', icon: Eye },\n  ];\n\n  return (\n    <section id=\"security\" className=\"section-padding bg-gray-900 text-white min-h-screen\">\n      <div className=\"container-custom\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl md:text-5xl font-bold mb-6 text-white\">\n            Enterprise-Grade\n            <span className=\"text-gold-400 block\">\n              Security & Compliance\n            </span>\n          </h2>\n          <p className=\"text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed\">\n            Enterprise-level security architecture designed for financial institutions with\n            zero-tolerance for security breaches and complete regulatory compliance.\n          </p>\n        </motion.div>\n\n        {/* Security Features Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 mb-16\">\n          {securityFeatures.map((feature, index) => (\n            <motion.div\n              key={feature.title}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n            >\n              <Card className=\"p-8 bg-gray-800 border-gray-700 h-full\">\n                <div className=\"flex items-start space-x-4\">\n                  <div className=\"w-12 h-12 bg-gradient-to-r from-gold-500 to-gold-600 rounded-lg flex items-center justify-center flex-shrink-0\">\n                    <feature.icon className=\"w-6 h-6 text-white\" />\n                  </div>\n                  <div className=\"flex-1\">\n                    <h3 className=\"text-xl font-bold text-white mb-3\">\n                      {feature.title}\n                    </h3>\n                    <p className=\"text-gray-300 mb-4 leading-relaxed\">\n                      {feature.description}\n                    </p>\n                    <ul className=\"space-y-2\">\n                      {feature.features.map((item, itemIndex) => (\n                        <motion.li\n                          key={item}\n                          initial={{ opacity: 0, x: -10 }}\n                          whileInView={{ opacity: 1, x: 0 }}\n                          transition={{ delay: index * 0.1 + itemIndex * 0.05 }}\n                          className=\"flex items-center text-gray-200\"\n                        >\n                          <CheckCircle className=\"w-4 h-4 text-green-400 mr-2 flex-shrink-0\" />\n                          <span className=\"text-sm\">{item}</span>\n                        </motion.li>\n                      ))}\n                    </ul>\n                  </div>\n                </div>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Security Metrics */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"mb-16\"\n        >\n          <h3 className=\"text-3xl font-bold text-center mb-8\">Security Performance</h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n            {securityMetrics.map((metric, index) => (\n              <motion.div\n                key={metric.label}\n                initial={{ opacity: 0, scale: 0.8 }}\n                whileInView={{ opacity: 1, scale: 1 }}\n                transition={{ delay: index * 0.1 }}\n              >\n                <Card className=\"p-6 bg-gray-800 border-gray-700 text-center\">\n                  <div className=\"w-12 h-12 bg-gradient-to-r from-primary-500 to-primary-600 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                    <metric.icon className=\"w-6 h-6 text-white\" />\n                  </div>\n                  <div className=\"text-3xl font-bold text-gold-400 mb-2\">\n                    {metric.value}\n                  </div>\n                  <div className=\"text-gray-300 text-sm\">\n                    {metric.label}\n                  </div>\n                </Card>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Compliance Standards */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"mb-16\"\n        >\n          <h3 className=\"text-3xl font-bold text-center mb-8\">Compliance Standards</h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6\">\n            {complianceStandards.map((standard, index) => (\n              <motion.div\n                key={standard.name}\n                initial={{ opacity: 0, y: 20 }}\n                whileInView={{ opacity: 1, y: 0 }}\n                transition={{ delay: index * 0.1 }}\n              >\n                <Card className=\"p-6 bg-gray-800 border-gray-700 text-center\">\n                  <standard.icon className=\"w-8 h-8 text-green-400 mx-auto mb-3\" />\n                  <h4 className=\"text-lg font-bold text-white mb-2\">\n                    {standard.name}\n                  </h4>\n                  <span className=\"inline-block bg-green-500 text-white text-xs px-3 py-1 rounded-full\">\n                    {standard.status}\n                  </span>\n                </Card>\n              </motion.div>\n            ))}\n          </div>\n        </motion.div>\n\n        {/* Security Architecture */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n        >\n          <Card className=\"p-8 bg-gradient-to-r from-gray-800 to-gray-700 border-gray-600\">\n            <div className=\"text-center\">\n              <h3 className=\"text-3xl font-bold mb-6\">\n                Multi-Layer Security Architecture\n              </h3>\n              <p className=\"text-gray-200 mb-8 max-w-3xl mx-auto\">\n                Our security architecture implements defense-in-depth principles with multiple\n                layers of protection, ensuring your financial data remains secure at all times.\n              </p>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n                <div className=\"text-center\">\n                  <div className=\"w-16 h-16 bg-gradient-to-r from-red-500 to-red-600 rounded-full flex items-center justify-center mx-auto mb-4\">\n                    <AlertTriangle className=\"w-8 h-8 text-white\" />\n                  </div>\n                  <h4 className=\"text-lg font-bold text-white mb-2\">Threat Detection</h4>\n                  <p className=\"text-gray-300 text-sm\">\n                    Real-time monitoring and automated threat detection with immediate response protocols.\n                  </p>\n                </div>\n\n                <div className=\"text-center\">\n                  <div className=\"w-16 h-16 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4\">\n                    <Key className=\"w-8 h-8 text-white\" />\n                  </div>\n                  <h4 className=\"text-lg font-bold text-white mb-2\">Access Control</h4>\n                  <p className=\"text-gray-300 text-sm\">\n                    Multi-factor authentication and role-based permissions with regional restrictions.\n                  </p>\n                </div>\n\n                <div className=\"text-center\">\n                  <div className=\"w-16 h-16 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4\">\n                    <FileCheck className=\"w-8 h-8 text-white\" />\n                  </div>\n                  <h4 className=\"text-lg font-bold text-white mb-2\">Data Protection</h4>\n                  <p className=\"text-gray-300 text-sm\">\n                    End-to-end encryption, secure backups, and comprehensive audit trails.\n                  </p>\n                </div>\n              </div>\n            </div>\n          </Card>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default SecuritySection;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,MAAM,EAAEC,IAAI,EAAEC,GAAG,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,aAAa,EAAEC,WAAW,QAAQ,cAAc;AACtG,OAAOC,IAAI,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EACtC,MAAMC,gBAAgB,GAAG,CACvB;IACEC,IAAI,EAAEb,MAAM;IACZc,KAAK,EAAE,wBAAwB;IAC/BC,WAAW,EAAE,qEAAqE;IAClFC,QAAQ,EAAE,CAAC,4BAA4B,EAAE,uBAAuB,EAAE,sBAAsB,EAAE,oBAAoB;EAChH,CAAC,EACD;IACEH,IAAI,EAAEZ,IAAI;IACVa,KAAK,EAAE,2BAA2B;IAClCC,WAAW,EAAE,iEAAiE;IAC9EC,QAAQ,EAAE,CAAC,kBAAkB,EAAE,mBAAmB,EAAE,mBAAmB,EAAE,iBAAiB;EAC5F,CAAC,EACD;IACEH,IAAI,EAAET,QAAQ;IACdU,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,0DAA0D;IACvEC,QAAQ,EAAE,CAAC,cAAc,EAAE,sBAAsB,EAAE,qBAAqB,EAAE,kBAAkB;EAC9F,CAAC,EACD;IACEH,IAAI,EAAEX,GAAG;IACTY,KAAK,EAAE,wBAAwB;IAC/BC,WAAW,EAAE,yDAAyD;IACtEC,QAAQ,EAAE,CAAC,gBAAgB,EAAE,eAAe,EAAE,gBAAgB,EAAE,oBAAoB;EACtF,CAAC,CACF;EAED,MAAMC,mBAAmB,GAAG,CAC1B;IAAEC,IAAI,EAAE,WAAW;IAAEC,MAAM,EAAE,WAAW;IAAEN,IAAI,EAAEN;EAAY,CAAC,EAC7D;IAAEW,IAAI,EAAE,eAAe;IAAEC,MAAM,EAAE,WAAW;IAAEN,IAAI,EAAEN;EAAY,CAAC,EACjE;IAAEW,IAAI,EAAE,SAAS;IAAEC,MAAM,EAAE,WAAW;IAAEN,IAAI,EAAEN;EAAY,CAAC,EAC3D;IAAEW,IAAI,EAAE,MAAM;IAAEC,MAAM,EAAE,WAAW;IAAEN,IAAI,EAAEN;EAAY,CAAC,CACzD;EAED,MAAMa,eAAe,GAAG,CACtB;IAAEC,KAAK,EAAE,YAAY;IAAEC,KAAK,EAAE,OAAO;IAAET,IAAI,EAAEN;EAAY,CAAC,EAC1D;IAAEc,KAAK,EAAE,iBAAiB;IAAEC,KAAK,EAAE,SAAS;IAAET,IAAI,EAAEZ;EAAK,CAAC,EAC1D;IAAEoB,KAAK,EAAE,oBAAoB;IAAEC,KAAK,EAAE,GAAG;IAAET,IAAI,EAAEb;EAAO,CAAC,EACzD;IAAEqB,KAAK,EAAE,iBAAiB;IAAEC,KAAK,EAAE,WAAW;IAAET,IAAI,EAAEX;EAAI,CAAC,CAC5D;EAED,oBACEQ,OAAA;IAASa,EAAE,EAAC,UAAU;IAACC,SAAS,EAAC,qDAAqD;IAAAC,QAAA,eACpFf,OAAA;MAAKc,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAE/Bf,OAAA,CAACX,MAAM,CAAC2B,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BR,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAE7Bf,OAAA;UAAIc,SAAS,EAAC,gDAAgD;UAAAC,QAAA,GAAC,kBAE7D,eAAAf,OAAA;YAAMc,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAEtC;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACL1B,OAAA;UAAGc,SAAS,EAAC,yDAAyD;UAAAC,QAAA,EAAC;QAGvE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGb1B,OAAA;QAAKc,SAAS,EAAC,6CAA6C;QAAAC,QAAA,EACzDb,gBAAgB,CAACyB,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBACnC7B,OAAA,CAACX,MAAM,CAAC2B,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,WAAW,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEQ,KAAK,EAAED,KAAK,GAAG;UAAI,CAAE;UAAAd,QAAA,eAElDf,OAAA,CAACF,IAAI;YAACgB,SAAS,EAAC,wCAAwC;YAAAC,QAAA,eACtDf,OAAA;cAAKc,SAAS,EAAC,4BAA4B;cAAAC,QAAA,gBACzCf,OAAA;gBAAKc,SAAS,EAAC,gHAAgH;gBAAAC,QAAA,eAC7Hf,OAAA,CAAC4B,OAAO,CAACzB,IAAI;kBAACW,SAAS,EAAC;gBAAoB;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,eACN1B,OAAA;gBAAKc,SAAS,EAAC,QAAQ;gBAAAC,QAAA,gBACrBf,OAAA;kBAAIc,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAC9Ca,OAAO,CAACxB;gBAAK;kBAAAmB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACZ,CAAC,eACL1B,OAAA;kBAAGc,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,EAC9Ca,OAAO,CAACvB;gBAAW;kBAAAkB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACJ1B,OAAA;kBAAIc,SAAS,EAAC,WAAW;kBAAAC,QAAA,EACtBa,OAAO,CAACtB,QAAQ,CAACqB,GAAG,CAAC,CAACI,IAAI,EAAEC,SAAS,kBACpChC,OAAA,CAACX,MAAM,CAAC4C,EAAE;oBAERhB,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEgB,CAAC,EAAE,CAAC;oBAAG,CAAE;oBAChCd,WAAW,EAAE;sBAAEF,OAAO,EAAE,CAAC;sBAAEgB,CAAC,EAAE;oBAAE,CAAE;oBAClCb,UAAU,EAAE;sBAAES,KAAK,EAAED,KAAK,GAAG,GAAG,GAAGG,SAAS,GAAG;oBAAK,CAAE;oBACtDlB,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,gBAE3Cf,OAAA,CAACH,WAAW;sBAACiB,SAAS,EAAC;oBAA2C;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACrE1B,OAAA;sBAAMc,SAAS,EAAC,SAAS;sBAAAC,QAAA,EAAEgB;oBAAI;sBAAAR,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA,GAPlCK,IAAI;oBAAAR,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAQA,CACZ;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACA,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC,GAjCFE,OAAO,CAACxB,KAAK;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAkCR,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN1B,OAAA,CAACX,MAAM,CAAC2B,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BR,SAAS,EAAC,OAAO;QAAAC,QAAA,gBAEjBf,OAAA;UAAIc,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAoB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7E1B,OAAA;UAAKc,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnDL,eAAe,CAACiB,GAAG,CAAC,CAACQ,MAAM,EAAEN,KAAK,kBACjC7B,OAAA,CAACX,MAAM,CAAC2B,GAAG;YAETC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEkB,KAAK,EAAE;YAAI,CAAE;YACpChB,WAAW,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEkB,KAAK,EAAE;YAAE,CAAE;YACtCf,UAAU,EAAE;cAAES,KAAK,EAAED,KAAK,GAAG;YAAI,CAAE;YAAAd,QAAA,eAEnCf,OAAA,CAACF,IAAI;cAACgB,SAAS,EAAC,6CAA6C;cAAAC,QAAA,gBAC3Df,OAAA;gBAAKc,SAAS,EAAC,qHAAqH;gBAAAC,QAAA,eAClIf,OAAA,CAACmC,MAAM,CAAChC,IAAI;kBAACW,SAAS,EAAC;gBAAoB;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C,CAAC,eACN1B,OAAA;gBAAKc,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,EACnDoB,MAAM,CAACvB;cAAK;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC,eACN1B,OAAA;gBAAKc,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EACnCoB,MAAM,CAACxB;cAAK;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACV,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF;UAAC,GAfFS,MAAM,CAACxB,KAAK;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgBP,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGb1B,OAAA,CAACX,MAAM,CAAC2B,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BR,SAAS,EAAC,OAAO;QAAAC,QAAA,gBAEjBf,OAAA;UAAIc,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAoB;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7E1B,OAAA;UAAKc,SAAS,EAAC,uCAAuC;UAAAC,QAAA,EACnDR,mBAAmB,CAACoB,GAAG,CAAC,CAACU,QAAQ,EAAER,KAAK,kBACvC7B,OAAA,CAACX,MAAM,CAAC2B,GAAG;YAETC,OAAO,EAAE;cAAEC,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAG,CAAE;YAC/BC,WAAW,EAAE;cAAEF,OAAO,EAAE,CAAC;cAAEC,CAAC,EAAE;YAAE,CAAE;YAClCE,UAAU,EAAE;cAAES,KAAK,EAAED,KAAK,GAAG;YAAI,CAAE;YAAAd,QAAA,eAEnCf,OAAA,CAACF,IAAI;cAACgB,SAAS,EAAC,6CAA6C;cAAAC,QAAA,gBAC3Df,OAAA,CAACqC,QAAQ,CAAClC,IAAI;gBAACW,SAAS,EAAC;cAAqC;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACjE1B,OAAA;gBAAIc,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAC9CsB,QAAQ,CAAC7B;cAAI;gBAAAe,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eACL1B,OAAA;gBAAMc,SAAS,EAAC,qEAAqE;gBAAAC,QAAA,EAClFsB,QAAQ,CAAC5B;cAAM;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC,GAbFW,QAAQ,CAAC7B,IAAI;YAAAe,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAcR,CACb;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAGb1B,OAAA,CAACX,MAAM,CAAC2B,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAAAP,QAAA,eAE9Bf,OAAA,CAACF,IAAI;UAACgB,SAAS,EAAC,gEAAgE;UAAAC,QAAA,eAC9Ef,OAAA;YAAKc,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1Bf,OAAA;cAAIc,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAC;YAExC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL1B,OAAA;cAAGc,SAAS,EAAC,sCAAsC;cAAAC,QAAA,EAAC;YAGpD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJ1B,OAAA;cAAKc,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDf,OAAA;gBAAKc,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1Bf,OAAA;kBAAKc,SAAS,EAAC,+GAA+G;kBAAAC,QAAA,eAC5Hf,OAAA,CAACJ,aAAa;oBAACkB,SAAS,EAAC;kBAAoB;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CAAC,eACN1B,OAAA;kBAAIc,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACvE1B,OAAA;kBAAGc,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAErC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAEN1B,OAAA;gBAAKc,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1Bf,OAAA;kBAAKc,SAAS,EAAC,iHAAiH;kBAAAC,QAAA,eAC9Hf,OAAA,CAACL,GAAG;oBAACmB,SAAS,EAAC;kBAAoB;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnC,CAAC,eACN1B,OAAA;kBAAIc,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAc;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACrE1B,OAAA;kBAAGc,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAErC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eAEN1B,OAAA;gBAAKc,SAAS,EAAC,aAAa;gBAAAC,QAAA,gBAC1Bf,OAAA;kBAAKc,SAAS,EAAC,mHAAmH;kBAAAC,QAAA,eAChIf,OAAA,CAACP,SAAS;oBAACqB,SAAS,EAAC;kBAAoB;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACzC,CAAC,eACN1B,OAAA;kBAAIc,SAAS,EAAC,mCAAmC;kBAAAC,QAAA,EAAC;gBAAe;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACtE1B,OAAA;kBAAGc,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAErC;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACY,EAAA,GA7NIrC,eAAyB;AA+N/B,eAAeA,eAAe;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}