{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/ADFD_Tracking_System/src/components/TrustSection.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TrustSection = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"trust-section\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"trust-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"trust-label\",\n          children: \"Trusted by Leading Financial Institutions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 8,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"trust-logos\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"trust-logo\",\n            children: \"Central Bank of UAE\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 10,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"trust-logo\",\n            children: \"Emirates NBD\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 11,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"trust-logo\",\n            children: \"First Abu Dhabi Bank\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 12,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"trust-logo\",\n            children: \"Abu Dhabi Commercial Bank\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 13,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"trust-logo\",\n            children: \"Mashreq Bank\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 14,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 9,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = TrustSection;\nexport default TrustSection;\nvar _c;\n$RefreshReg$(_c, \"TrustSection\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "TrustSection", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/ADFD_Tracking_System/src/components/TrustSection.tsx"], "sourcesContent": ["import React from 'react';\n\nconst TrustSection: React.FC = () => {\n  return (\n    <section className=\"trust-section\">\n      <div className=\"container\">\n        <div className=\"trust-content\">\n          <p className=\"trust-label\">Trusted by Leading Financial Institutions</p>\n          <div className=\"trust-logos\">\n            <div className=\"trust-logo\">Central Bank of UAE</div>\n            <div className=\"trust-logo\">Emirates NBD</div>\n            <div className=\"trust-logo\">First Abu Dhabi Bank</div>\n            <div className=\"trust-logo\">Abu Dhabi Commercial Bank</div>\n            <div className=\"trust-logo\">Mashreq Bank</div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default TrustSection;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EACnC,oBACED,OAAA;IAASE,SAAS,EAAC,eAAe;IAAAC,QAAA,eAChCH,OAAA;MAAKE,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBH,OAAA;QAAKE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BH,OAAA;UAAGE,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAAyC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACxEP,OAAA;UAAKE,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BH,OAAA;YAAKE,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAmB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrDP,OAAA;YAAKE,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC9CP,OAAA;YAAKE,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAoB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACtDP,OAAA;YAAKE,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAyB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC3DP,OAAA;YAAKE,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACC,EAAA,GAjBIP,YAAsB;AAmB5B,eAAeA,YAAY;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}