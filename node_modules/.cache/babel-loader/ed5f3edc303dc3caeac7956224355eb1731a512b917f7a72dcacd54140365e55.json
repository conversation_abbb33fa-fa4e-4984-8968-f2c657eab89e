{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/ADFD_Tracking_System/src/components/TechnologySection.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst TechnologySection = () => {\n  const techFeatures = [{\n    icon: '🧠',\n    title: 'Machine Learning OCR',\n    description: 'Advanced neural networks trained specifically for Arabic and English financial documents, achieving 99.9% accuracy in data extraction.'\n  }, {\n    icon: '💾',\n    title: 'Real-Time Database',\n    description: 'Lightning-fast updates across all users with conflict resolution, ensuring everyone sees the latest status immediately.'\n  }, {\n    icon: '🔒',\n    title: 'Enterprise Security',\n    description: 'Bank-level encryption, multi-factor authentication, and comprehensive audit logging meeting international financial standards.'\n  }, {\n    icon: '📱',\n    title: 'Mobile-First Design',\n    description: 'Responsive interface works seamlessly across desktop, tablet, and mobile devices for on-the-go access.'\n  }, {\n    icon: '🌐',\n    title: 'Multi-Currency Support',\n    description: 'Handle USD, EUR, AED, and other currencies with real-time exchange rates and proper financial formatting.'\n  }, {\n    icon: '📈',\n    title: 'Analytics & Reporting',\n    description: 'Comprehensive dashboards, performance metrics, and custom reports for data-driven decision making.'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"technology\",\n    className: \"technology\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"section-header animate-on-scroll\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Advanced Technology Stack\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Built with cutting-edge technology for reliability and performance\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"tech-features\",\n        children: techFeatures.map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"tech-feature animate-on-scroll\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tech-icon\",\n            children: feature.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 47,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"tech-content\",\n            children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n              children: feature.title\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 51,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: feature.description\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 50,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 46,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 38,\n    columnNumber: 5\n  }, this);\n};\n_c = TechnologySection;\nexport default TechnologySection;\nvar _c;\n$RefreshReg$(_c, \"TechnologySection\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "TechnologySection", "techFeatures", "icon", "title", "description", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "feature", "index", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/ADFD_Tracking_System/src/components/TechnologySection.tsx"], "sourcesContent": ["import React from 'react';\n\nconst TechnologySection: React.FC = () => {\n  const techFeatures = [\n    {\n      icon: '🧠',\n      title: 'Machine Learning OCR',\n      description: 'Advanced neural networks trained specifically for Arabic and English financial documents, achieving 99.9% accuracy in data extraction.'\n    },\n    {\n      icon: '💾',\n      title: 'Real-Time Database',\n      description: 'Lightning-fast updates across all users with conflict resolution, ensuring everyone sees the latest status immediately.'\n    },\n    {\n      icon: '🔒',\n      title: 'Enterprise Security',\n      description: 'Bank-level encryption, multi-factor authentication, and comprehensive audit logging meeting international financial standards.'\n    },\n    {\n      icon: '📱',\n      title: 'Mobile-First Design',\n      description: 'Responsive interface works seamlessly across desktop, tablet, and mobile devices for on-the-go access.'\n    },\n    {\n      icon: '🌐',\n      title: 'Multi-Currency Support',\n      description: 'Handle USD, EUR, AED, and other currencies with real-time exchange rates and proper financial formatting.'\n    },\n    {\n      icon: '📈',\n      title: 'Analytics & Reporting',\n      description: 'Comprehensive dashboards, performance metrics, and custom reports for data-driven decision making.'\n    }\n  ];\n\n  return (\n    <section id=\"technology\" className=\"technology\">\n      <div className=\"container\">\n        <div className=\"section-header animate-on-scroll\">\n          <h2>Advanced Technology Stack</h2>\n          <p>Built with cutting-edge technology for reliability and performance</p>\n        </div>\n        <div className=\"tech-features\">\n          {techFeatures.map((feature, index) => (\n            <div key={index} className=\"tech-feature animate-on-scroll\">\n              <div className=\"tech-icon\">\n                {feature.icon}\n              </div>\n              <div className=\"tech-content\">\n                <h4>{feature.title}</h4>\n                <p>{feature.description}</p>\n              </div>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default TechnologySection;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,iBAA2B,GAAGA,CAAA,KAAM;EACxC,MAAMC,YAAY,GAAG,CACnB;IACEC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,sBAAsB;IAC7BC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,wBAAwB;IAC/BC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,uBAAuB;IAC9BC,WAAW,EAAE;EACf,CAAC,CACF;EAED,oBACEL,OAAA;IAASM,EAAE,EAAC,YAAY;IAACC,SAAS,EAAC,YAAY;IAAAC,QAAA,eAC7CR,OAAA;MAAKO,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBR,OAAA;QAAKO,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/CR,OAAA;UAAAQ,QAAA,EAAI;QAAyB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClCZ,OAAA;UAAAQ,QAAA,EAAG;QAAkE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE,CAAC,eACNZ,OAAA;QAAKO,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC3BN,YAAY,CAACW,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC/Bf,OAAA;UAAiBO,SAAS,EAAC,gCAAgC;UAAAC,QAAA,gBACzDR,OAAA;YAAKO,SAAS,EAAC,WAAW;YAAAC,QAAA,EACvBM,OAAO,CAACX;UAAI;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNZ,OAAA;YAAKO,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BR,OAAA;cAAAQ,QAAA,EAAKM,OAAO,CAACV;YAAK;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACxBZ,OAAA;cAAAQ,QAAA,EAAIM,OAAO,CAACT;YAAW;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA,GAPEG,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAQV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACI,EAAA,GAzDIf,iBAA2B;AA2DjC,eAAeA,iBAAiB;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}