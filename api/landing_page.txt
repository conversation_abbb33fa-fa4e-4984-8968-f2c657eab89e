<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Abu Dhabi Fund - Enterprise Withdrawal Request Platform | Powered by AI</title>
    <meta name="description" content="Transform your fund disbursement operations with AI-powered OCR, intelligent automation, and enterprise-grade security. Trusted by leading financial institutions.">
    <!-- Multiple icon loading strategies -->
    <script src="https://cdn.jsdelivr.net/npm/lucide@latest/dist/umd/lucide.js"></script>
    <script>
        // Backup CDN loading
        window.addEventListener('DOMContentLoaded', function() {
            if (typeof lucide === 'undefined') {
                console.log('Loading backup Lucide CDN...');
                const script = document.createElement('script');
                script.src = 'https://unpkg.com/lucide@latest/dist/umd/lucide.js';
                script.onload = () => initializeIcons();
                document.head.appendChild(script);
            } else {
                initializeIcons();
            }
        });
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%);
            --accent-gradient: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 25%, #1e40af 50%, #1e3a8a 75%, #172554 100%);
            --success-gradient: linear-gradient(135deg, #059669 0%, #047857 25%, #065f46 50%, #064e3b 75%, #022c22 100%);
            --warning-gradient: linear-gradient(135deg, #d97706 0%, #b45309 25%, #92400e 50%, #78350f 75%, #451a03 100%);
            --surface-glass: rgba(255, 255, 255, 0.08);
            --surface-glass-hover: rgba(255, 255, 255, 0.12);
            --text-primary: #0f172a;
            --text-secondary: #475569;
            --text-muted: #64748b;
            --border-subtle: rgba(148, 163, 184, 0.2);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            overflow-x: hidden;
            background: #fafafa;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 2rem;
        }

        /* Enhanced Background */
        .hero-background {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 120vh;
            background: var(--primary-gradient);
            z-index: -3;
        }

        .hero-background::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 60%, rgba(16, 185, 129, 0.2) 0%, transparent 50%);
        }

        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: -2;
        }

        .floating-shape {
            position: absolute;
            opacity: 0.1;
            border-radius: 50%;
            animation: float 20s infinite ease-in-out;
        }

        .floating-shape:nth-child(1) {
            width: 300px;
            height: 300px;
            background: var(--accent-gradient);
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-shape:nth-child(2) {
            width: 200px;
            height: 200px;
            background: var(--success-gradient);
            top: 60%;
            right: 15%;
            animation-delay: -7s;
        }

        .floating-shape:nth-child(3) {
            width: 150px;
            height: 150px;
            background: var(--warning-gradient);
            bottom: 30%;
            left: 20%;
            animation-delay: -14s;
        }

        .floating-shape:nth-child(4) {
            width: 120px;
            height: 120px;
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
            top: 30%;
            right: 30%;
            animation-delay: -3s;
        }

        .floating-shape:nth-child(5) {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #ef4444, #dc2626);
            bottom: 60%;
            left: 60%;
            animation-delay: -10s;
        }

        @keyframes float {
            0%, 100% { 
                transform: translate(0, 0) rotate(0deg) scale(1); 
                opacity: 0.1;
            }
            25% { 
                transform: translate(30px, -30px) rotate(90deg) scale(1.1); 
                opacity: 0.15;
            }
            50% { 
                transform: translate(-20px, 20px) rotate(180deg) scale(0.9); 
                opacity: 0.08;
            }
            75% { 
                transform: translate(40px, 10px) rotate(270deg) scale(1.05); 
                opacity: 0.12;
            }
        }

        /* Enhanced Navigation */
        .navigation {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(15, 23, 42, 0.8);
            backdrop-filter: blur(24px);
            -webkit-backdrop-filter: blur(24px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            z-index: 1000;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .navigation.scrolled {
            background: rgba(15, 23, 42, 0.95);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
        }

        .nav-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1.5rem 0;
        }

        .brand {
            display: flex;
            align-items: center;
            gap: 1rem;
            text-decoration: none;
            color: white;
        }

        .brand-icon {
            width: 48px;
            height: 48px;
            background: var(--accent-gradient);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
        }

        .brand-text {
            display: flex;
            flex-direction: column;
        }

        .brand-name {
            font-size: 1.5rem;
            font-weight: 800;
            letter-spacing: -0.025em;
        }

        .brand-tagline {
            font-size: 0.75rem;
            opacity: 0.7;
            font-weight: 500;
            letter-spacing: 0.05em;
            text-transform: uppercase;
        }

        .nav-menu {
            display: flex;
            gap: 3rem;
            list-style: none;
        }

        .nav-menu a {
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.95rem;
            transition: all 0.3s ease;
            position: relative;
            padding: 0.5rem 0;
        }

        .nav-menu a::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background: var(--accent-gradient);
            transition: width 0.3s ease;
        }

        .nav-menu a:hover {
            color: white;
        }

        .nav-menu a:hover::after {
            width: 100%;
        }

        .nav-actions {
            display: flex;
            gap: 1rem;
            align-items: center;
        }

        .btn-nav {
            padding: 0.75rem 1.5rem;
            border-radius: 12px;
            text-decoration: none;
            font-weight: 600;
            font-size: 0.9rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: none;
            cursor: pointer;
        }

        .btn-nav.secondary {
            color: rgba(255, 255, 255, 0.9);
            border: 1px solid rgba(255, 255, 255, 0.3);
            background: rgba(255, 255, 255, 0.1);
        }

        .btn-nav.secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.4);
            transform: translateY(-1px);
        }

        .btn-nav.primary {
            background: var(--accent-gradient);
            color: white;
            box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
        }

        .btn-nav.primary:hover {
            box-shadow: 0 6px 24px rgba(59, 130, 246, 0.4);
            transform: translateY(-2px);
        }

        /* Hero Section */
        .hero {
            position: relative;
            padding: 12rem 0 8rem;
            color: white;
            text-align: center;
        }

        .hero-content {
            max-width: 900px;
            margin: 0 auto;
        }

        .hero-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(12px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 0.75rem 1.5rem;
            border-radius: 50px;
            font-size: 0.9rem;
            font-weight: 600;
            margin-bottom: 3rem;
            animation: fadeInUp 0.8s ease 0.2s both;
        }

        .hero-badge::before {
            content: '';
            width: 8px;
            height: 8px;
            background: #10b981;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .hero-title {
            font-size: clamp(3rem, 6vw, 5.5rem);
            font-weight: 900;
            line-height: 1.1;
            margin-bottom: 2rem;
            background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 50%, #cbd5e1 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: fadeInUp 0.8s ease 0.4s both;
        }

        .hero-subtitle {
            font-size: 1.5rem;
            line-height: 1.6;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 3rem;
            font-weight: 400;
            animation: fadeInUp 0.8s ease 0.6s both;
        }

        .hero-actions {
            display: flex;
            gap: 1.5rem;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 4rem;
            animation: fadeInUp 0.8s ease 0.8s both;
        }

        .btn-hero {
            display: inline-flex;
            align-items: center;
            gap: 0.75rem;
            padding: 1.25rem 2.5rem;
            border-radius: 16px;
            text-decoration: none;
            font-weight: 700;
            font-size: 1.1rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            border: 2px solid transparent;
            position: relative;
            overflow: hidden;
        }

        .btn-hero::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-hero:hover::before {
            left: 100%;
        }

        .btn-hero.primary {
            background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
            color: #0f172a;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .btn-hero.primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
        }

        .btn-hero.secondary {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            border-color: rgba(255, 255, 255, 0.3);
            backdrop-filter: blur(12px);
        }

        .btn-hero.secondary:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-3px);
        }

        .hero-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 3rem;
            max-width: 800px;
            margin: 0 auto;
            animation: fadeInUp 0.8s ease 1s both;
        }

        .hero-stat {
            text-align: center;
        }

        .hero-stat-number {
            display: block;
            font-size: 3rem;
            font-weight: 900;
            background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 50%, #2563eb 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 0.5rem;
        }

        .hero-stat-label {
            color: rgba(255, 255, 255, 0.7);
            font-weight: 600;
            font-size: 0.95rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        /* Trust Section */
        .trust-section {
            background: white;
            padding: 6rem 0;
            border-top: 1px solid var(--border-subtle);
        }

        .trust-content {
            text-align: center;
            max-width: 800px;
            margin: 0 auto;
        }

        .trust-label {
            color: var(--text-muted);
            font-size: 0.9rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.1em;
            margin-bottom: 3rem;
        }

        .trust-logos {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 3rem;
            align-items: center;
        }

        .trust-logo {
            height: 60px;
            background: var(--surface-glass);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            color: var(--text-secondary);
            border: 1px solid var(--border-subtle);
            transition: all 0.3s ease;
        }

        .trust-logo:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        /* Features Section */
        .features {
            background: linear-gradient(180deg, white 0%, #f8fafc 100%);
            padding: 8rem 0;
        }

        .section-header {
            text-align: center;
            max-width: 800px;
            margin: 0 auto 6rem;
        }

        .section-badge {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            background: rgba(59, 130, 246, 0.1);
            color: #1e40af;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.85rem;
            font-weight: 600;
            margin-bottom: 1.5rem;
        }

        .section-title {
            font-size: 3.5rem;
            font-weight: 900;
            line-height: 1.2;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
        }

        .section-description {
            font-size: 1.25rem;
            color: var(--text-secondary);
            line-height: 1.6;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 3rem;
        }

        .feature-card {
            background: white;
            border-radius: 24px;
            padding: 3rem;
            box-shadow: 0 4px 32px rgba(0, 0, 0, 0.08);
            border: 1px solid var(--border-subtle);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--accent-gradient);
        }

        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 64px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            width: 72px;
            height: 72px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 2rem;
            color: white;
            position: relative;
            overflow: hidden;
        }

        .feature-icon::after {
            content: '';
            position: absolute;
            inset: 0;
            background: inherit;
            opacity: 0.1;
            transform: scale(1.5);
            border-radius: 50%;
        }

        .feature-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 1rem;
        }

        .feature-description {
            color: var(--text-secondary);
            line-height: 1.7;
            font-size: 1rem;
        }

        .feature-link {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            color: #3b82f6;
            font-weight: 600;
            font-size: 0.9rem;
            text-decoration: none;
            margin-top: 1.5rem;
            transition: gap 0.3s ease;
        }

        .feature-link:hover {
            gap: 1rem;
        }

        /* Process Section */
        .process {
            background: var(--primary-gradient);
            padding: 8rem 0;
            position: relative;
            overflow: hidden;
        }

        .process::before {
            content: '';
            position: absolute;
            inset: 0;
            background: 
                radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.2) 0%, transparent 50%);
        }

        .process-content {
            position: relative;
            z-index: 1;
        }

        .process .section-header {
            color: white;
        }

        .process .section-badge {
            background: rgba(255, 255, 255, 0.2);
            color: white;
        }

        .process .section-title {
            color: white;
        }

        .process .section-description {
            color: rgba(255, 255, 255, 0.8);
        }

        .process-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 3rem;
            margin-top: 6rem;
        }

        .process-step {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(16px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 24px;
            padding: 3rem;
            text-align: center;
            position: relative;
            transition: all 0.4s ease;
        }

        .process-step:hover {
            transform: translateY(-8px);
            background: rgba(255, 255, 255, 0.15);
        }

        .step-number {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
            color: var(--text-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2rem;
            font-weight: 900;
            margin: 0 auto 2rem;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
        }

        .step-title {
            font-size: 1.5rem;
            font-weight: 700;
            color: white;
            margin-bottom: 1rem;
        }

        .step-description {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
        }

        /* Platform Section */
        .platform {
            background: white;
            padding: 8rem 0;
        }

        .platform-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 6rem;
            align-items: center;
        }

        .platform-visual {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 24px;
            padding: 3rem;
            position: relative;
            overflow: hidden;
        }

        .platform-mockup {
            background: var(--primary-gradient);
            border-radius: 16px;
            padding: 2rem;
            color: white;
            position: relative;
            z-index: 1;
        }

        .mockup-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .mockup-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: var(--success-gradient);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }

        .mockup-user {
            flex: 1;
        }

        .mockup-name {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .mockup-role {
            font-size: 0.85rem;
            opacity: 0.7;
        }

        .mockup-status {
            background: rgba(16, 185, 129, 0.2);
            color: #10b981;
            padding: 0.5rem 1rem;
            border-radius: 50px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .mockup-content {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 1.5rem;
        }

        .mockup-stat {
            text-align: center;
            padding: 1.5rem;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
        }

        .mockup-stat-number {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }

        .mockup-stat-label {
            font-size: 0.8rem;
            opacity: 0.8;
        }

        .platform-content h3 {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--text-primary);
            margin-bottom: 1.5rem;
        }

        .platform-features {
            list-style: none;
            margin: 2rem 0;
        }

        .platform-features li {
            display: flex;
            align-items: center;
            gap: 1rem;
            color: var(--text-secondary);
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .platform-features li::before {
            content: '✓';
            color: #10b981;
            font-weight: 700;
            font-size: 1.2rem;
        }

        /* Security Section */
        .security {
            background: linear-gradient(180deg, #f8fafc 0%, white 100%);
            padding: 8rem 0;
        }

        .security-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 3rem;
            margin-top: 4rem;
        }

        .security-card {
            background: white;
            border-radius: 20px;
            padding: 2.5rem;
            box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08);
            border: 1px solid var(--border-subtle);
            transition: all 0.3s ease;
        }

        .security-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 40px rgba(0, 0, 0, 0.12);
        }

        .security-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            background: var(--success-gradient);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
        }

        .security-card h4 {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: 1rem;
        }

        .security-card p {
            color: var(--text-secondary);
            line-height: 1.6;
        }

        /* CTA Section */
        .cta {
            background: var(--primary-gradient);
            padding: 8rem 0;
            position: relative;
            overflow: hidden;
            text-align: center;
        }

        .cta::before {
            content: '';
            position: absolute;
            inset: 0;
            background: 
                radial-gradient(circle at 30% 30%, rgba(59, 130, 246, 0.3) 0%, transparent 60%),
                radial-gradient(circle at 70% 70%, rgba(139, 92, 246, 0.3) 0%, transparent 60%);
        }

        .cta-content {
            position: relative;
            z-index: 1;
            max-width: 800px;
            margin: 0 auto;
        }

        .cta h2 {
            font-size: 4rem;
            font-weight: 900;
            color: white;
            margin-bottom: 2rem;
            line-height: 1.2;
        }

        .cta p {
            font-size: 1.5rem;
            color: rgba(255, 255, 255, 0.8);
            margin-bottom: 3rem;
            line-height: 1.6;
        }

        .cta-actions {
            display: flex;
            gap: 2rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        /* Footer */
        .footer {
            background: #0f172a;
            color: rgba(255, 255, 255, 0.8);
            padding: 6rem 0 3rem;
        }

        .footer-content {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 6rem;
            margin-bottom: 4rem;
        }

        .footer-brand {
            max-width: 400px;
        }

        .footer-logo {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
        }

        .footer-logo-icon {
            width: 48px;
            height: 48px;
            background: var(--accent-gradient);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .footer-description {
            line-height: 1.7;
            margin-bottom: 2rem;
        }

        .footer-social {
            display: flex;
            gap: 1rem;
        }

        .social-link {
            width: 44px;
            height: 44px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .social-link:hover {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            transform: translateY(-2px);
        }

        .footer-sections {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 3rem;
        }

        .footer-section h4 {
            color: white;
            font-weight: 700;
            font-size: 1.1rem;
            margin-bottom: 1.5rem;
        }

        .footer-section ul {
            list-style: none;
        }

        .footer-section li {
            margin-bottom: 0.75rem;
        }

        .footer-section a {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            transition: color 0.3s ease;
        }

        .footer-section a:hover {
            color: white;
        }

        .footer-bottom {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding-top: 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .footer-copyright {
            color: rgba(255, 255, 255, 0.6);
        }

        .footer-links {
            display: flex;
            gap: 2rem;
            list-style: none;
        }

        .footer-links a {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            font-size: 0.9rem;
        }

        /* Enhanced Animation System */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(60px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes fadeInLeft {
            from {
                opacity: 0;
                transform: translateX(-60px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes fadeInRight {
            from {
                opacity: 0;
                transform: translateX(60px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes scaleIn {
            from {
                opacity: 0;
                transform: scale(0.8);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.8;
                transform: scale(0.95);
            }
        }

        @keyframes glow {
            0%, 100% {
                box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
            }
            50% {
                box-shadow: 0 0 40px rgba(59, 130, 246, 0.6), 0 0 60px rgba(59, 130, 246, 0.4);
            }
        }

        @keyframes shimmer {
            0% {
                transform: translateX(-100%);
            }
            100% {
                transform: translateX(100%);
            }
        }

        /* Enhanced interaction states */
        .btn-hero, .btn-nav, .feature-card, .security-card, .process-step {
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .btn-hero:hover, .btn-nav:hover {
            transform: translateY(-3px);
            transition: all 0.2s cubic-bezier(0.34, 1.56, 0.64, 1);
        }

        .btn-hero:active, .btn-nav:active {
            transform: translateY(-1px);
            transition: all 0.1s ease;
        }

        .feature-card:hover {
            transform: translateY(-12px) scale(1.02);
            box-shadow: 0 25px 80px rgba(0, 0, 0, 0.15);
        }

        .security-card:hover {
            transform: translateY(-8px) scale(1.01);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12);
        }

        .process-step:hover {
            transform: translateY(-8px);
            background: rgba(255, 255, 255, 0.2);
        }

        /* Loading animation for stats */
        .hero-stat-number {
            position: relative;
        }

        .hero-stat-number.loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            animation: shimmer 2s infinite;
        }

        /* Enhanced trust logos */
        .trust-logo {
            position: relative;
            overflow: hidden;
        }

        .trust-logo::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
            transition: left 0.6s;
        }

        .trust-logo:hover::before {
            left: 100%;
        }

        /* Enhanced navigation */
        .nav-menu a {
            position: relative;
            overflow: hidden;
        }

        .nav-menu a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .nav-menu a:hover::before {
            left: 100%;
        }

        /* Floating animation variants */
        .floating-shape:nth-child(1) {
            animation: float 20s infinite ease-in-out;
        }

        .floating-shape:nth-child(2) {
            animation: float 25s infinite ease-in-out reverse;
        }

        .floating-shape:nth-child(3) {
            animation: float 18s infinite ease-in-out;
        }

        .floating-shape:nth-child(4) {
            animation: float 22s infinite ease-in-out reverse;
        }

        .floating-shape:nth-child(5) {
            animation: float 16s infinite ease-in-out;
        }

        /* Enhanced hero badge animation */
        .hero-badge {
            animation: fadeInUp 0.8s ease 0.2s both, glow 3s infinite;
        }

        /* Staggered animation classes */
        .animate-on-scroll.delay-1 {
            transition-delay: 0.1s;
        }

        .animate-on-scroll.delay-2 {
            transition-delay: 0.2s;
        }

        .animate-on-scroll.delay-3 {
            transition-delay: 0.3s;
        }

        .animate-on-scroll.delay-4 {
            transition-delay: 0.4s;
        }

        .animate-on-scroll.fade-left {
            transform: translateX(-60px);
        }

        .animate-on-scroll.fade-left.visible {
            transform: translateX(0);
        }

        .animate-on-scroll.fade-right {
            transform: translateX(60px);
        }

        .animate-on-scroll.fade-right.visible {
            transform: translateX(0);
        }

        .animate-on-scroll.scale-in {
            transform: scale(0.8);
        }

        .animate-on-scroll.scale-in.visible {
            transform: scale(1);
        }

        /* Enhanced mockup animations */
        .platform-mockup {
            animation: scaleIn 0.8s ease 0.5s both;
        }

        .mockup-stat {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .mockup-stat:hover {
            transform: scale(1.05);
            background: rgba(255, 255, 255, 0.2);
        }

        /* Interactive feature links */
        .feature-link {
            position: relative;
        }

        .feature-link::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 0;
            height: 2px;
            background: #3b82f6;
            transition: width 0.3s ease;
        }

        .feature-link:hover::after {
            width: calc(100% - 20px);
        }

        /* Enhanced section backgrounds */
        .features, .security, .platform {
            position: relative;
        }

        .features::before, .security::before, .platform::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 50% 0%, rgba(59, 130, 246, 0.03) 0%, transparent 70%);
            pointer-events: none;
        }

        /* Loading states */
        .loading-placeholder {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 2s infinite;
        }

        /* Scroll progress indicator */
        .scroll-progress {
            position: fixed;
            top: 0;
            left: 0;
            width: 0%;
            height: 3px;
            background: var(--accent-gradient);
            z-index: 9999;
            transition: width 0.1s ease;
        }

        /* Enhanced responsive animations */
        @media (max-width: 768px) {
            .animate-on-scroll {
                transform: translateY(30px);
            }
            
            .floating-shape {
                display: none;
            }
            
            .hero-stats {
                animation-delay: 0.8s;
            }
        }

        /* Visual feedback for enhanced experience */
        body::after {
            content: var(--icon-status, "🔄 Loading Icons...");
            position: fixed;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            z-index: 9999;
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.3s ease;
            pointer-events: none;
        }

        body.show-status::after {
            opacity: 1;
            transform: translateY(0);
        }

        body.loaded::after {
            opacity: 0;
            transform: translateY(20px);
            transition-delay: 2s;
        }
        @media (prefers-reduced-motion: reduce) {
            *,
            *::before,
            *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
                scroll-behavior: auto !important;
            }
            
            .floating-shape {
                animation: none;
            }
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            .feature-card,
            .security-card,
            .process-step {
                border: 2px solid currentColor;
            }
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .platform-grid {
                grid-template-columns: 1fr;
                gap: 4rem;
            }
            
            .footer-content {
                grid-template-columns: 1fr;
                gap: 4rem;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding: 0 1.5rem;
            }

            .nav-menu {
                display: none;
            }

            .hero {
                padding: 8rem 0 6rem;
            }

            .hero-title {
                font-size: 2.5rem;
            }

            .hero-subtitle {
                font-size: 1.25rem;
            }

            .hero-actions {
                flex-direction: column;
                align-items: center;
            }

            .section-title {
                font-size: 2.5rem;
            }

            .cta h2 {
                font-size: 2.5rem;
            }

            .features-grid,
            .security-grid {
                grid-template-columns: 1fr;
            }

            .footer-bottom {
                flex-direction: column;
                text-align: center;
            }
        }

        @media (max-width: 480px) {
            .hero-actions {
                gap: 1rem;
            }

            .btn-hero {
                width: 100%;
                justify-content: center;
            }

            .hero-stats {
                gap: 2rem;
            }

            .process-steps {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <!-- Scroll Progress Indicator -->
    <div class="scroll-progress"></div>
    
    <div class="hero-background">
        <div class="floating-elements">
            <div class="floating-shape"></div>
            <div class="floating-shape"></div>
            <div class="floating-shape"></div>
            <div class="floating-shape"></div>
            <div class="floating-shape"></div>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navigation">
        <div class="container">
            <div class="nav-container">
                <a href="#" class="brand">
                    <div class="brand-icon">
                        <i data-lucide="building-2"></i>
                    </div>
                    <div class="brand-text">
                        <div class="brand-name">Abu Dhabi Fund</div>
                        <div class="brand-tagline">Enterprise Platform</div>
                    </div>
                </a>
                
                <ul class="nav-menu">
                    <li><a href="#platform">Platform</a></li>
                    <li><a href="#features">Features</a></li>
                    <li><a href="#security">Security</a></li>
                    <li><a href="#enterprise">Enterprise</a></li>
                </ul>
                
                <div class="nav-actions">
                    <a href="#contact" class="btn-nav secondary">Contact Sales</a>
                    <a href="#start" class="btn-nav primary">Get Started</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <div class="hero-badge">
                    <span>AI-Powered Financial Operations</span>
                </div>
                
                <h1 class="hero-title">
                    Transform Fund Disbursement with Intelligent Automation
                </h1>
                
                <p class="hero-subtitle">
                    Enterprise-grade withdrawal request platform powered by AI-driven OCR, regional intelligence, and bank-level security. Trusted by leading financial institutions worldwide.
                </p>
                
                <div class="hero-actions">
                    <a href="#start" class="btn-hero primary">
                        <i data-lucide="rocket"></i>
                        Start Free Enterprise Trial
                    </a>
                    <a href="#platform" class="btn-hero secondary">
                        <i data-lucide="play-circle"></i>
                        See Platform in Action
                    </a>
                </div>
                
                <div class="hero-stats">
                    <div class="hero-stat">
                        <span class="hero-stat-number" data-target="99.9">0</span>
                        <span class="hero-stat-label">Accuracy Rate</span>
                    </div>
                    <div class="hero-stat">
                        <span class="hero-stat-number" data-target="85">0</span>
                        <span class="hero-stat-label">Faster Processing</span>
                    </div>
                    <div class="hero-stat">
                        <span class="hero-stat-number" data-target="24">0</span>
                        <span class="hero-stat-label">Hour Monitoring</span>
                    </div>
                    <div class="hero-stat">
                        <span class="hero-stat-number" data-target="100">0</span>
                        <span class="hero-stat-label">Uptime SLA</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Trust Section -->
    <section class="trust-section">
        <div class="container">
            <div class="trust-content">
                <p class="trust-label">Trusted by Leading Financial Institutions</p>
                <div class="trust-logos">
                    <div class="trust-logo">Central Bank of UAE</div>
                    <div class="trust-logo">Emirates NBD</div>
                    <div class="trust-logo">First Abu Dhabi Bank</div>
                    <div class="trust-logo">Abu Dhabi Commercial Bank</div>
                    <div class="trust-logo">Mashreq Bank</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features">
        <div class="container">
            <div class="section-header animate-on-scroll">
                <div class="section-badge">
                    <i data-lucide="zap"></i>
                    Powerful Features
                </div>
                <h2 class="section-title">Built for Enterprise Financial Operations</h2>
                <p class="section-description">
                    Advanced AI capabilities, intelligent automation, and enterprise-grade security designed for the most demanding financial institutions.
                </p>
            </div>
            
            <div class="features-grid">
                <div class="feature-card animate-on-scroll delay-1">
                    <div class="feature-icon" style="background: var(--accent-gradient);">
                        <i data-lucide="scan-text"></i>
                    </div>
                    <h3 class="feature-title">AI-Powered OCR Intelligence</h3>
                    <p class="feature-description">
                        Advanced neural networks trained on Arabic and English financial documents achieve 99.9% extraction accuracy. Automatically reads, validates, and processes withdrawal requests in milliseconds.
                    </p>
                    <a href="#" class="feature-link">
                        Learn more <i data-lucide="arrow-right"></i>
                    </a>
                </div>
                
                <div class="feature-card animate-on-scroll delay-2">
                    <div class="feature-icon" style="background: var(--success-gradient);">
                        <i data-lucide="map-pin"></i>
                    </div>
                    <h3 class="feature-title">Intelligent Regional Routing</h3>
                    <p class="feature-description">
                        Smart geographic assignment automatically routes requests to specialized regional teams across North Africa, Central Africa, Southeast Asia, and Central Asia for optimal processing efficiency.
                    </p>
                    <a href="#" class="feature-link">
                        Learn more <i data-lucide="arrow-right"></i>
                    </a>
                </div>
                
                <div class="feature-card animate-on-scroll delay-3">
                    <div class="feature-icon" style="background: var(--warning-gradient);">
                        <i data-lucide="shield-check"></i>
                    </div>
                    <h3 class="feature-title">Zero-Exception Security Model</h3>
                    <p class="feature-description">
                        Military-grade role-based access controls with immutable permissions. Archive creates, Operations approves, Core Banking disburses - zero privilege escalation allowed.
                    </p>
                    <a href="#" class="feature-link">
                        Learn more <i data-lucide="arrow-right"></i>
                    </a>
                </div>
                
                <div class="feature-card animate-on-scroll delay-4">
                    <div class="feature-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                        <i data-lucide="activity"></i>
                    </div>
                    <h3 class="feature-title">Real-Time Process Intelligence</h3>
                    <p class="feature-description">
                        Live tracking with predictive analytics, automated bottleneck detection, and intelligent priority routing. Complete audit trails with regulatory compliance built-in.
                    </p>
                    <a href="#" class="feature-link">
                        Learn more <i data-lucide="arrow-right"></i>
                    </a>
                </div>
                
                <div class="feature-card animate-on-scroll delay-1">
                    <div class="feature-icon" style="background: linear-gradient(135deg, #ef4444, #dc2626);">
                        <i data-lucide="zap"></i>
                    </div>
                    <h3 class="feature-title">Intelligent Alert System</h3>
                    <p class="feature-description">
                        ML-powered priority detection with automated escalation. Urgent disbursements get immediate attention with smart notifications and visual indicators.
                    </p>
                    <a href="#" class="feature-link">
                        Learn more <i data-lucide="arrow-right"></i>
                    </a>
                </div>
                
                <div class="feature-card animate-on-scroll delay-2">
                    <div class="feature-icon" style="background: linear-gradient(135deg, #06b6d4, #0891b2);">
                        <i data-lucide="search"></i>
                    </div>
                    <h3 class="feature-title">Advanced Analytics Engine</h3>
                    <p class="feature-description">
                        Multi-dimensional search, predictive insights, and performance analytics. Custom dashboards and automated reporting for data-driven decision making.
                    </p>
                    <a href="#" class="feature-link">
                        Learn more <i data-lucide="arrow-right"></i>
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Process Section -->
    <section class="process">
        <div class="container">
            <div class="process-content">
                <div class="section-header animate-on-scroll">
                    <div class="section-badge">
                        <i data-lucide="workflow"></i>
                        Intelligent Workflow
                    </div>
                    <h2 class="section-title">Streamlined 4-Stage Process</h2>
                    <p class="section-description">
                        Every request follows an optimized workflow designed for maximum efficiency, security, and compliance.
                    </p>
                </div>
                
                <div class="process-steps">
                    <div class="process-step animate-on-scroll">
                        <div class="step-number">1</div>
                        <h4 class="step-title">Intelligent Creation</h4>
                        <p class="step-description">
                            Archive team uploads documents. AI-powered OCR extracts all data with 99.9% accuracy and automatically assigns to regional specialists.
                        </p>
                    </div>
                    
                    <div class="process-step animate-on-scroll">
                        <div class="step-number">2</div>
                        <h4 class="step-title">Expert Technical Review</h4>
                        <p class="step-description">
                            Regional operations teams with specialized knowledge evaluate technical requirements and compliance within their geographic expertise.
                        </p>
                    </div>
                    
                    <div class="process-step animate-on-scroll">
                        <div class="step-number">3</div>
                        <h4 class="step-title">Secure Banking Process</h4>
                        <p class="step-description">
                            Approved requests automatically flow to core banking team for final disbursement with multi-layer security validation and audit logging.
                        </p>
                    </div>
                    
                    <div class="process-step animate-on-scroll">
                        <div class="step-number">4</div>
                        <h4 class="step-title">Completion & Tracking</h4>
                        <p class="step-description">
                            Real-time status updates, stakeholder notifications, and complete audit trail ensure transparency and regulatory compliance.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Platform Section -->
    <section id="platform" class="platform">
        <div class="container">
            <div class="platform-grid">
                <div class="platform-visual animate-on-scroll fade-right">
                    <div class="platform-mockup">
                        <div class="mockup-header">
                            <div class="mockup-avatar">👩‍💻</div>
                            <div class="mockup-user">
                                <div class="mockup-name">Lisa Banking</div>
                                <div class="mockup-role">Core Banking Team</div>
                            </div>
                            <div class="mockup-status">
                                <i data-lucide="check-circle"></i>
                                Active Session
                            </div>
                        </div>
                        <div class="mockup-content">
                            <div class="mockup-stat">
                                <div class="mockup-stat-number">12</div>
                                <div class="mockup-stat-label">Pending Disbursements</div>
                            </div>
                            <div class="mockup-stat">
                                <div class="mockup-stat-number">$2.4M</div>
                                <div class="mockup-stat-label">Total Amount Ready</div>
                            </div>
                            <div class="mockup-stat">
                                <div class="mockup-stat-number">4</div>
                                <div class="mockup-stat-label">Urgent Priority</div>
                            </div>
                            <div class="mockup-stat">
                                <div class="mockup-stat-number">98%</div>
                                <div class="mockup-stat-label">SLA Compliance</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="platform-content animate-on-scroll fade-left">
                    <h3>Real-Time Operations Dashboard</h3>
                    <p class="section-description">
                        Experience unparalleled visibility into your fund disbursement operations with intelligent dashboards, predictive analytics, and automated insights.
                    </p>
                    
                    <ul class="platform-features">
                        <li>Live request tracking with predictive completion times</li>
                        <li>Intelligent priority scoring and automated escalation</li>
                        <li>Regional performance analytics and optimization</li>
                        <li>Automated compliance reporting and audit trails</li>
                        <li>Custom KPI dashboards for executive oversight</li>
                        <li>Mobile-first design for on-the-go management</li>
                    </ul>
                    
                    <a href="#start" class="btn-hero primary">
                        <i data-lucide="arrow-right"></i>
                        Explore Platform
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Security Section -->
    <section id="security" class="security">
        <div class="container">
            <div class="section-header animate-on-scroll">
                <div class="section-badge">
                    <i data-lucide="shield"></i>
                    Enterprise Security
                </div>
                <h2 class="section-title">Bank-Level Security & Compliance</h2>
                <p class="section-description">
                    Built with security-first architecture meeting international banking standards and regulatory requirements.
                </p>
            </div>
            
            <div class="security-grid">
                <div class="security-card animate-on-scroll scale-in delay-1">
                    <div class="security-icon">
                        <i data-lucide="lock"></i>
                    </div>
                    <h4>End-to-End Encryption</h4>
                    <p>AES-256 encryption for data at rest and in transit. Zero-knowledge architecture ensures your sensitive financial data remains completely private.</p>
                </div>
                
                <div class="security-card animate-on-scroll scale-in delay-2">
                    <div class="security-icon">
                        <i data-lucide="shield-check"></i>
                    </div>
                    <h4>Multi-Factor Authentication</h4>
                    <p>Advanced authentication including biometric verification, hardware tokens, and adaptive risk assessment for bulletproof access control.</p>
                </div>
                
                <div class="security-card animate-on-scroll scale-in delay-3">
                    <div class="security-icon">
                        <i data-lucide="eye"></i>
                    </div>
                    <h4>Complete Audit Logging</h4>
                    <p>Immutable audit trails capture every action, access, and change with tamper-proof timestamps meeting regulatory compliance standards.</p>
                </div>
                
                <div class="security-card animate-on-scroll scale-in delay-4">
                    <div class="security-icon">
                        <i data-lucide="server"></i>
                    </div>
                    <h4>SOC 2 Type II Certified</h4>
                    <p>Annual third-party security audits and certifications including SOC 2 Type II, ISO 27001, and PCI DSS compliance validation.</p>
                </div>
                
                <div class="security-card animate-on-scroll scale-in delay-1">
                    <div class="security-icon">
                        <i data-lucide="globe"></i>
                    </div>
                    <h4>Geographic Data Sovereignty</h4>
                    <p>Data residency controls ensure compliance with local regulations. Choose where your data lives and maintain full jurisdictional control.</p>
                </div>
                
                <div class="security-card animate-on-scroll scale-in delay-2">
                    <div class="security-icon">
                        <i data-lucide="users"></i>
                    </div>
                    <h4>Role-Based Access Control</h4>
                    <p>Granular permissions with principle of least privilege. Zero-exception security model prevents unauthorized access or privilege escalation.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section id="start" class="cta">
        <div class="container">
            <div class="cta-content">
                <h2 class="animate-on-scroll">Ready to Transform Your Operations?</h2>
                <p class="animate-on-scroll">
                    Join leading financial institutions who've revolutionized their disbursement operations with our enterprise platform.
                </p>
                <div class="cta-actions animate-on-scroll">
                    <a href="#" class="btn-hero primary">
                        <i data-lucide="rocket"></i>
                        Start Free 30-Day Trial
                    </a>
                    <a href="#" class="btn-hero secondary">
                        <i data-lucide="calendar"></i>
                        Schedule Enterprise Demo
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-brand">
                    <div class="footer-logo">
                        <div class="footer-logo-icon">
                            <i data-lucide="building-2"></i>
                        </div>
                        <div class="brand-text">
                            <div class="brand-name" style="color: white;">Abu Dhabi Fund</div>
                            <div class="brand-tagline">Enterprise Platform</div>
                        </div>
                    </div>
                    <p class="footer-description">
                        Empowering the world's leading financial institutions with intelligent automation, enterprise security, and unparalleled operational efficiency.
                    </p>
                    <div class="footer-social">
                        <a href="#" class="social-link">
                            <i data-lucide="linkedin"></i>
                        </a>
                        <a href="#" class="social-link">
                            <i data-lucide="twitter"></i>
                        </a>
                        <a href="#" class="social-link">
                            <i data-lucide="github"></i>
                        </a>
                        <a href="#" class="social-link">
                            <i data-lucide="mail"></i>
                        </a>
                    </div>
                </div>
                
                <div class="footer-sections">
                    <div class="footer-section">
                        <h4>Platform</h4>
                        <ul>
                            <li><a href="#">Features</a></li>
                            <li><a href="#">Security</a></li>
                            <li><a href="#">Integrations</a></li>
                            <li><a href="#">API Documentation</a></li>
                            <li><a href="#">System Status</a></li>
                        </ul>
                    </div>
                    
                    <div class="footer-section">
                        <h4>Solutions</h4>
                        <ul>
                            <li><a href="#">Enterprise</a></li>
                            <li><a href="#">Banking & Finance</a></li>
                            <li><a href="#">Government</a></li>
                            <li><a href="#">Regional Operations</a></li>
                            <li><a href="#">Compliance</a></li>
                        </ul>
                    </div>
                    
                    <div class="footer-section">
                        <h4>Resources</h4>
                        <ul>
                            <li><a href="#">Documentation</a></li>
                            <li><a href="#">Training Center</a></li>
                            <li><a href="#">Best Practices</a></li>
                            <li><a href="#">Case Studies</a></li>
                            <li><a href="#">Webinars</a></li>
                        </ul>
                    </div>
                    
                    <div class="footer-section">
                        <h4>Support</h4>
                        <ul>
                            <li><a href="#">Help Center</a></li>
                            <li><a href="#">Contact Support</a></li>
                            <li><a href="#">Professional Services</a></li>
                            <li><a href="#">System Health</a></li>
                            <li><a href="#">Community</a></li>
                        </ul>
                    </div>
                    
                    <div class="footer-section">
                        <h4>Company</h4>
                        <ul>
                            <li><a href="#">About Us</a></li>
                            <li><a href="#">Careers</a></li>
                            <li><a href="#">Press Kit</a></li>
                            <li><a href="#">Partners</a></li>
                            <li><a href="#">Investors</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="footer-bottom">
                <div class="footer-copyright">
                    &copy; 2025 Abu Dhabi Fund Enterprise Platform. All rights reserved.
                </div>
                <ul class="footer-links">
                    <li><a href="#">Privacy Policy</a></li>
                    <li><a href="#">Terms of Service</a></li>
                    <li><a href="#">Security</a></li>
                    <li><a href="#">Compliance</a></li>
                </ul>
            </div>
        </div>
    </footer>

    <script>
        // Initialize icons with fallback
        function initializeIcons() {
            try {
                if (typeof lucide !== 'undefined') {
                    lucide.createIcons();
                } else {
                    console.warn('Lucide not loaded, using fallback icons');
                    const fallbackIcons = {
                        'building-2': '🏢', 'rocket': '🚀', 'play-circle': '▶️', 'zap': '⚡',
                        'scan-text': '📄', 'map-pin': '📍', 'shield-check': '🛡️', 'activity': '📊',
                        'search': '🔍', 'workflow': '⚙️', 'shield': '🔒', 'lock': '🔐',
                        'eye': '👁️', 'server': '💾', 'globe': '🌐', 'users': '👥',
                        'arrow-right': '→', 'check-circle': '✅', 'calendar': '📅',
                        'linkedin': '💼', 'twitter': '🐦', 'github': '🔧', 'mail': '✉️'
                    };
                    
                    document.querySelectorAll('[data-lucide]').forEach(el => {
                        const iconName = el.getAttribute('data-lucide');
                        el.innerHTML = fallbackIcons[iconName] || '•';
                    });
                }
            } catch (error) {
                console.warn('Error initializing icons:', error);
            }
        }

        // Initialize when DOM is loaded with enhanced error handling
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeApp);
        } else {
            initializeApp();
        }

        function initializeApp() {
            // Show status indicator
            document.body.classList.add('show-status');
            
            initializeIcons();
            setupAnimations();
            setupInteractions();
            setupScrollEffects();
            setupPerformanceOptimizations();
            
            // Hide status indicator after 3 seconds
            setTimeout(() => {
                document.body.classList.remove('show-status');
            }, 3000);
        }

        // Enhanced scroll animations with intersection observer
        function setupAnimations() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach((entry, index) => {
                    if (entry.isIntersecting) {
                        // Add staggered delay for multiple elements
                        setTimeout(() => {
                            entry.target.classList.add('visible');
                            
                            // Add ripple effect for cards
                            if (entry.target.classList.contains('feature-card') || 
                                entry.target.classList.contains('security-card')) {
                                createRippleEffect(entry.target);
                            }
                        }, index * 100);
                    }
                });
            }, observerOptions);

            // Enhanced animation variants
            document.querySelectorAll('.animate-on-scroll').forEach((el, index) => {
                observer.observe(el);
                
                // Add random animation variants
                const variants = ['fade-up', 'fade-left', 'fade-right', 'scale-in'];
                const randomVariant = variants[index % variants.length];
                
                if (randomVariant !== 'fade-up') {
                    el.classList.add(randomVariant);
                }
                
                // Add staggered delays
                if (index % 4 === 1) el.classList.add('delay-1');
                if (index % 4 === 2) el.classList.add('delay-2');
                if (index % 4 === 3) el.classList.add('delay-3');
            });
        }

        // Ripple effect function
        function createRippleEffect(element) {
            const ripple = document.createElement('div');
            ripple.style.cssText = `
                position: absolute;
                border-radius: 50%;
                background: rgba(59, 130, 246, 0.3);
                pointer-events: none;
                transform: scale(0);
                animation: ripple 0.6s linear;
                top: 50%;
                left: 50%;
                width: 20px;
                height: 20px;
                margin-left: -10px;
                margin-top: -10px;
            `;
            
            const style = document.createElement('style');
            style.textContent = `
                @keyframes ripple {
                    to {
                        transform: scale(4);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style);
            
            element.style.position = 'relative';
            element.style.overflow = 'hidden';
            element.appendChild(ripple);
            
            setTimeout(() => {
                ripple.remove();
            }, 600);
        }

        // Enhanced interaction setup
        function setupInteractions() {
            // Enhanced button hover effects
            document.querySelectorAll('.btn-hero, .btn-nav').forEach(btn => {
                btn.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-3px) scale(1.05)';
                    this.style.boxShadow = '0 10px 40px rgba(0, 0, 0, 0.2)';
                });
                
                btn.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                    this.style.boxShadow = '';
                });
                
                btn.addEventListener('mousedown', function() {
                    this.style.transform = 'translateY(-1px) scale(0.98)';
                });
                
                btn.addEventListener('mouseup', function() {
                    this.style.transform = 'translateY(-3px) scale(1.05)';
                });
            });

            // Enhanced card interactions
            document.querySelectorAll('.feature-card, .security-card, .process-step').forEach(card => {
                card.addEventListener('mouseenter', function() {
                    this.style.transform = this.classList.contains('feature-card') ? 
                        'translateY(-12px) scale(1.02)' : 'translateY(-8px) scale(1.01)';
                    
                    // Add glow effect
                    this.style.boxShadow = '0 25px 80px rgba(59, 130, 246, 0.15)';
                });
                
                card.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                    this.style.boxShadow = '';
                });
            });

            // Interactive trust logos
            document.querySelectorAll('.trust-logo').forEach(logo => {
                logo.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-6px) scale(1.05)';
                    this.style.boxShadow = '0 10px 30px rgba(0, 0, 0, 0.1)';
                });
                
                logo.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0) scale(1)';
                    this.style.boxShadow = '';
                });
            });

            // Interactive mockup stats
            document.querySelectorAll('.mockup-stat').forEach(stat => {
                stat.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.1) rotate(2deg)';
                    this.style.background = 'rgba(255, 255, 255, 0.25)';
                });
                
                stat.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1) rotate(0deg)';
                    this.style.background = 'rgba(255, 255, 255, 0.1)';
                });
            });
        }

        // Enhanced scroll effects
        function setupScrollEffects() {
            const navigation = document.querySelector('.navigation');
            const scrollProgress = document.querySelector('.scroll-progress');
            let lastScrollTop = 0;

            window.addEventListener('scroll', () => {
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                const docHeight = document.documentElement.scrollHeight - window.innerHeight;
                const scrollPercent = (scrollTop / docHeight) * 100;
                
                // Update scroll progress
                scrollProgress.style.width = scrollPercent + '%';
                
                // Enhanced navigation scroll effect
                if (scrollTop > 100) {
                    navigation.classList.add('scrolled');
                    navigation.style.transform = 'translateY(0)';
                } else {
                    navigation.classList.remove('scrolled');
                }
                
                // Hide/show navigation on scroll
                if (scrollTop > lastScrollTop && scrollTop > 200) {
                    navigation.style.transform = 'translateY(-100%)';
                } else {
                    navigation.style.transform = 'translateY(0)';
                }
                
                lastScrollTop = scrollTop;
            });

            // Smooth scrolling for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        }

        // Enhanced hero stats animation
        const animateStats = () => {
            const stats = document.querySelectorAll('.hero-stat-number');
            
            stats.forEach((stat, index) => {
                const target = parseFloat(stat.dataset.target);
                let current = 0;
                const increment = target / 50; // More frames for smoother animation
                const suffix = stat.dataset.target.includes('.') ? '%' : '';
                
                // Add loading effect
                stat.classList.add('loading');
                
                setTimeout(() => {
                    const timer = setInterval(() => {
                        current += increment;
                        if (current >= target) {
                            stat.textContent = target + (suffix === '%' ? '%' : '');
                            stat.classList.remove('loading');
                            clearInterval(timer);
                            
                            // Add completion effect
                            stat.style.transform = 'scale(1.1)';
                            setTimeout(() => {
                                stat.style.transform = 'scale(1)';
                            }, 200);
                        } else {
                            stat.textContent = Math.floor(current) + (suffix === '%' ? '%' : '');
                        }
                    }, 50);
                }, index * 200); // Stagger the animations
            });
        };

        // Trigger stats animation when hero is visible
        const heroObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    setTimeout(animateStats, 1500);
                    heroObserver.disconnect();
                }
            });
        });

        const heroStats = document.querySelector('.hero-stats');
        if (heroStats) {
            heroObserver.observe(heroStats);
        }

        // Enhanced parallax effect for floating shapes
        function setupPerformanceOptimizations() {
            let ticking = false;

            function updateParallax() {
                const scrolled = window.pageYOffset;
                
                document.querySelectorAll('.floating-shape').forEach((shape, index) => {
                    const speed = (index + 1) * 0.1;
                    const yPos = -(scrolled * speed);
                    shape.style.transform = `translate3d(0, ${yPos}px, 0)`;
                });

                ticking = false;
            }

            function requestTick() {
                if (!ticking) {
                    requestAnimationFrame(updateParallax);
                    ticking = true;
                }
            }

            window.addEventListener('scroll', requestTick, { passive: true });
        }

        // Add loading states and error handling
        window.addEventListener('load', () => {
            document.body.classList.add('loaded');
            
            // Remove any loading states
            document.querySelectorAll('.loading-placeholder').forEach(el => {
                el.classList.remove('loading-placeholder');
            });
        });

        // Enhanced error handling
        window.addEventListener('error', (e) => {
            console.error('Script error:', e.error);
        });

        // Add keyboard navigation support
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Tab') {
                document.body.classList.add('keyboard-navigation');
            }
        });

        document.addEventListener('mousedown', () => {
            document.body.classList.remove('keyboard-navigation');
        });

        // Add focus styles for keyboard users
        const style = document.createElement('style');
        style.textContent = `
            body:not(.keyboard-navigation) *:focus {
                outline: none;
            }
            
            .keyboard-navigation *:focus {
                outline: 2px solid #3b82f6;
                outline-offset: 2px;
            }
        `;
        document.head.appendChild(style);
    </script>
</body>
</html>