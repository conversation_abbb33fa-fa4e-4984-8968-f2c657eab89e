{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/ADFD_Tracking_System/src/components/Effects/ScrollEffects.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$(),\n  _s5 = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { motion, useScroll, useTransform } from 'framer-motion';\n\n// Parallax Background Component\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const ParallaxBackground = ({\n  children\n}) => {\n  _s();\n  const {\n    scrollY\n  } = useScroll();\n  const y = useTransform(scrollY, [0, 1000], [0, -200]);\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    style: {\n      y\n    },\n    className: \"relative\",\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 10,\n    columnNumber: 5\n  }, this);\n};\n\n// Scroll Progress Indicator\n_s(ParallaxBackground, \"sZC7D7RRTAF+ZcJsaIkRSEitHwo=\", false, function () {\n  return [useScroll, useTransform];\n});\n_c = ParallaxBackground;\nexport const ScrollProgress = () => {\n  _s2();\n  const {\n    scrollYProgress\n  } = useScroll();\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    className: \"fixed top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-500 to-gold-500 z-50 origin-left\",\n    style: {\n      scaleX: scrollYProgress\n    }\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this);\n};\n\n// Floating Action Button with Scroll to Top\n_s2(ScrollProgress, \"K+vLkUSSogCN887TkXa8xjCRTOU=\", false, function () {\n  return [useScroll];\n});\n_c2 = ScrollProgress;\nexport const ScrollToTop = () => {\n  _s3();\n  const [isVisible, setIsVisible] = useState(false);\n  const {\n    scrollY\n  } = useScroll();\n  useEffect(() => {\n    const unsubscribe = scrollY.onChange(latest => {\n      setIsVisible(latest > 300);\n    });\n    return unsubscribe;\n  }, [scrollY]);\n  const scrollToTop = () => {\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(motion.button, {\n    initial: {\n      opacity: 0,\n      scale: 0\n    },\n    animate: {\n      opacity: isVisible ? 1 : 0,\n      scale: isVisible ? 1 : 0\n    },\n    whileHover: {\n      scale: 1.1\n    },\n    whileTap: {\n      scale: 0.9\n    },\n    onClick: scrollToTop,\n    className: \"fixed bottom-8 right-8 w-12 h-12 bg-gradient-to-r from-primary-600 to-primary-700 text-white rounded-full shadow-lg hover:shadow-xl transition-shadow duration-300 flex items-center justify-center z-40\",\n    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"w-6 h-6\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      viewBox: \"0 0 24 24\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: 2,\n        d: \"M5 10l7-7m0 0l7 7m-7-7v18\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 57,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 56,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 5\n  }, this);\n};\n\n// Reveal Animation on Scroll\n_s3(ScrollToTop, \"X+2if/x6P6mu63nsbDRwU+OEoMI=\", false, function () {\n  return [useScroll];\n});\n_c3 = ScrollToTop;\nexport const RevealOnScroll = ({\n  children,\n  direction = 'up',\n  delay = 0\n}) => {\n  const variants = {\n    hidden: {\n      opacity: 0,\n      y: direction === 'up' ? 50 : direction === 'down' ? -50 : 0,\n      x: direction === 'left' ? 50 : direction === 'right' ? -50 : 0\n    },\n    visible: {\n      opacity: 1,\n      y: 0,\n      x: 0,\n      transition: {\n        duration: 0.8,\n        delay,\n        ease: [0.25, 0.1, 0.25, 1]\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    initial: \"hidden\",\n    whileInView: \"visible\",\n    viewport: {\n      once: true,\n      margin: '-100px'\n    },\n    variants: variants,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 88,\n    columnNumber: 5\n  }, this);\n};\n\n// Stagger Children Animation\n_c4 = RevealOnScroll;\nexport const StaggerContainer = ({\n  children,\n  staggerDelay = 0.1\n}) => {\n  const containerVariants = {\n    hidden: {\n      opacity: 0\n    },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: staggerDelay\n      }\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(motion.div, {\n    initial: \"hidden\",\n    whileInView: \"visible\",\n    viewport: {\n      once: true\n    },\n    variants: containerVariants,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 115,\n    columnNumber: 5\n  }, this);\n};\n\n// Magnetic Button Effect\n_c5 = StaggerContainer;\nexport const MagneticButton = ({\n  children,\n  className = '',\n  onClick\n}) => {\n  _s4();\n  const [position, setPosition] = useState({\n    x: 0,\n    y: 0\n  });\n  const handleMouseMove = e => {\n    const rect = e.currentTarget.getBoundingClientRect();\n    const centerX = rect.left + rect.width / 2;\n    const centerY = rect.top + rect.height / 2;\n    const deltaX = (e.clientX - centerX) * 0.15;\n    const deltaY = (e.clientY - centerY) * 0.15;\n    setPosition({\n      x: deltaX,\n      y: deltaY\n    });\n  };\n  const handleMouseLeave = () => {\n    setPosition({\n      x: 0,\n      y: 0\n    });\n  };\n  return /*#__PURE__*/_jsxDEV(motion.button, {\n    className: className,\n    onClick: onClick,\n    onMouseMove: handleMouseMove,\n    onMouseLeave: handleMouseLeave,\n    animate: {\n      x: position.x,\n      y: position.y\n    },\n    transition: {\n      type: 'spring',\n      stiffness: 300,\n      damping: 30\n    },\n    whileHover: {\n      scale: 1.05\n    },\n    whileTap: {\n      scale: 0.95\n    },\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 148,\n    columnNumber: 5\n  }, this);\n};\n\n// Typewriter Effect\n_s4(MagneticButton, \"0qniUPJTLf8/oonUDLg0GM42ieg=\");\n_c6 = MagneticButton;\nexport const TypewriterText = ({\n  text,\n  delay = 0,\n  speed = 50\n}) => {\n  _s5();\n  const [displayText, setDisplayText] = useState('');\n  const [currentIndex, setCurrentIndex] = useState(0);\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      if (currentIndex < text.length) {\n        setDisplayText(text.slice(0, currentIndex + 1));\n        setCurrentIndex(currentIndex + 1);\n      }\n    }, currentIndex === 0 ? delay : speed);\n    return () => clearTimeout(timer);\n  }, [currentIndex, text, delay, speed]);\n  return /*#__PURE__*/_jsxDEV(\"span\", {\n    children: [displayText, /*#__PURE__*/_jsxDEV(motion.span, {\n      animate: {\n        opacity: [1, 0]\n      },\n      transition: {\n        duration: 0.8,\n        repeat: Infinity\n      },\n      className: \"inline-block w-0.5 h-6 bg-current ml-1\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 184,\n    columnNumber: 5\n  }, this);\n};\n\n// Floating Elements\n_s5(TypewriterText, \"hIUgadE6edYynGHHGmHPDKhBDus=\");\n_c7 = TypewriterText;\nexport const FloatingElements = () => {\n  const elements = Array.from({\n    length: 15\n  }, (_, i) => ({\n    id: i,\n    size: Math.random() * 4 + 2,\n    x: Math.random() * 100,\n    y: Math.random() * 100,\n    duration: Math.random() * 10 + 10,\n    delay: Math.random() * 5\n  }));\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed inset-0 pointer-events-none overflow-hidden\",\n    children: elements.map(element => /*#__PURE__*/_jsxDEV(motion.div, {\n      className: \"absolute rounded-full bg-gradient-to-r from-primary-400/20 to-gold-400/20\",\n      style: {\n        width: element.size,\n        height: element.size,\n        left: `${element.x}%`,\n        top: `${element.y}%`\n      },\n      animate: {\n        y: [-20, 20, -20],\n        x: [-10, 10, -10],\n        scale: [1, 1.2, 1],\n        opacity: [0.3, 0.6, 0.3]\n      },\n      transition: {\n        duration: element.duration,\n        delay: element.delay,\n        repeat: Infinity,\n        ease: 'easeInOut'\n      }\n    }, element.id, false, {\n      fileName: _jsxFileName,\n      lineNumber: 209,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 207,\n    columnNumber: 5\n  }, this);\n};\n_c8 = FloatingElements;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8;\n$RefreshReg$(_c, \"ParallaxBackground\");\n$RefreshReg$(_c2, \"ScrollProgress\");\n$RefreshReg$(_c3, \"ScrollToTop\");\n$RefreshReg$(_c4, \"RevealOnScroll\");\n$RefreshReg$(_c5, \"StaggerContainer\");\n$RefreshReg$(_c6, \"MagneticButton\");\n$RefreshReg$(_c7, \"TypewriterText\");\n$RefreshReg$(_c8, \"FloatingElements\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "motion", "useScroll", "useTransform", "jsxDEV", "_jsxDEV", "ParallaxBackground", "children", "_s", "scrollY", "y", "div", "style", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "ScrollProgress", "_s2", "scrollYProgress", "scaleX", "_c2", "ScrollToTop", "_s3", "isVisible", "setIsVisible", "unsubscribe", "onChange", "latest", "scrollToTop", "window", "scrollTo", "top", "behavior", "button", "initial", "opacity", "scale", "animate", "whileHover", "whileTap", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "_c3", "RevealOnScroll", "direction", "delay", "variants", "hidden", "x", "visible", "transition", "duration", "ease", "whileInView", "viewport", "once", "margin", "_c4", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stagger<PERSON><PERSON><PERSON>", "containerVariants", "stagger<PERSON><PERSON><PERSON><PERSON>", "_c5", "MagneticButton", "_s4", "position", "setPosition", "handleMouseMove", "e", "rect", "currentTarget", "getBoundingClientRect", "centerX", "left", "width", "centerY", "height", "deltaX", "clientX", "deltaY", "clientY", "handleMouseLeave", "onMouseMove", "onMouseLeave", "type", "stiffness", "damping", "_c6", "TypewriterText", "text", "speed", "_s5", "displayText", "setDisplayText", "currentIndex", "setCurrentIndex", "timer", "setTimeout", "length", "slice", "clearTimeout", "span", "repeat", "Infinity", "_c7", "FloatingElements", "elements", "Array", "from", "_", "i", "id", "size", "Math", "random", "map", "element", "_c8", "$RefreshReg$"], "sources": ["/Users/<USER>/ADFD_Tracking_System/src/components/Effects/ScrollEffects.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { motion, useScroll, useTransform } from 'framer-motion';\n\n// Parallax Background Component\nexport const ParallaxBackground: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const { scrollY } = useScroll();\n  const y = useTransform(scrollY, [0, 1000], [0, -200]);\n\n  return (\n    <motion.div style={{ y }} className=\"relative\">\n      {children}\n    </motion.div>\n  );\n};\n\n// Scroll Progress Indicator\nexport const ScrollProgress: React.FC = () => {\n  const { scrollYProgress } = useScroll();\n\n  return (\n    <motion.div\n      className=\"fixed top-0 left-0 right-0 h-1 bg-gradient-to-r from-primary-500 to-gold-500 z-50 origin-left\"\n      style={{ scaleX: scrollYProgress }}\n    />\n  );\n};\n\n// Floating Action Button with Scroll to Top\nexport const ScrollToTop: React.FC = () => {\n  const [isVisible, setIsVisible] = useState(false);\n  const { scrollY } = useScroll();\n\n  useEffect(() => {\n    const unsubscribe = scrollY.onChange((latest) => {\n      setIsVisible(latest > 300);\n    });\n    return unsubscribe;\n  }, [scrollY]);\n\n  const scrollToTop = () => {\n    window.scrollTo({ top: 0, behavior: 'smooth' });\n  };\n\n  return (\n    <motion.button\n      initial={{ opacity: 0, scale: 0 }}\n      animate={{ \n        opacity: isVisible ? 1 : 0, \n        scale: isVisible ? 1 : 0 \n      }}\n      whileHover={{ scale: 1.1 }}\n      whileTap={{ scale: 0.9 }}\n      onClick={scrollToTop}\n      className=\"fixed bottom-8 right-8 w-12 h-12 bg-gradient-to-r from-primary-600 to-primary-700 text-white rounded-full shadow-lg hover:shadow-xl transition-shadow duration-300 flex items-center justify-center z-40\"\n    >\n      <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 10l7-7m0 0l7 7m-7-7v18\" />\n      </svg>\n    </motion.button>\n  );\n};\n\n// Reveal Animation on Scroll\nexport const RevealOnScroll: React.FC<{\n  children: React.ReactNode;\n  direction?: 'up' | 'down' | 'left' | 'right';\n  delay?: number;\n}> = ({ children, direction = 'up', delay = 0 }) => {\n  const variants = {\n    hidden: {\n      opacity: 0,\n      y: direction === 'up' ? 50 : direction === 'down' ? -50 : 0,\n      x: direction === 'left' ? 50 : direction === 'right' ? -50 : 0,\n    },\n    visible: {\n      opacity: 1,\n      y: 0,\n      x: 0,\n      transition: {\n        duration: 0.8,\n        delay,\n        ease: [0.25, 0.1, 0.25, 1],\n      },\n    },\n  };\n\n  return (\n    <motion.div\n      initial=\"hidden\"\n      whileInView=\"visible\"\n      viewport={{ once: true, margin: '-100px' }}\n      variants={variants}\n    >\n      {children}\n    </motion.div>\n  );\n};\n\n// Stagger Children Animation\nexport const StaggerContainer: React.FC<{\n  children: React.ReactNode;\n  staggerDelay?: number;\n}> = ({ children, staggerDelay = 0.1 }) => {\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: staggerDelay,\n      },\n    },\n  };\n\n  return (\n    <motion.div\n      initial=\"hidden\"\n      whileInView=\"visible\"\n      viewport={{ once: true }}\n      variants={containerVariants}\n    >\n      {children}\n    </motion.div>\n  );\n};\n\n// Magnetic Button Effect\nexport const MagneticButton: React.FC<{\n  children: React.ReactNode;\n  className?: string;\n  onClick?: () => void;\n}> = ({ children, className = '', onClick }) => {\n  const [position, setPosition] = useState({ x: 0, y: 0 });\n\n  const handleMouseMove = (e: React.MouseEvent<HTMLButtonElement>) => {\n    const rect = e.currentTarget.getBoundingClientRect();\n    const centerX = rect.left + rect.width / 2;\n    const centerY = rect.top + rect.height / 2;\n    const deltaX = (e.clientX - centerX) * 0.15;\n    const deltaY = (e.clientY - centerY) * 0.15;\n    setPosition({ x: deltaX, y: deltaY });\n  };\n\n  const handleMouseLeave = () => {\n    setPosition({ x: 0, y: 0 });\n  };\n\n  return (\n    <motion.button\n      className={className}\n      onClick={onClick}\n      onMouseMove={handleMouseMove}\n      onMouseLeave={handleMouseLeave}\n      animate={{ x: position.x, y: position.y }}\n      transition={{ type: 'spring', stiffness: 300, damping: 30 }}\n      whileHover={{ scale: 1.05 }}\n      whileTap={{ scale: 0.95 }}\n    >\n      {children}\n    </motion.button>\n  );\n};\n\n// Typewriter Effect\nexport const TypewriterText: React.FC<{\n  text: string;\n  delay?: number;\n  speed?: number;\n}> = ({ text, delay = 0, speed = 50 }) => {\n  const [displayText, setDisplayText] = useState('');\n  const [currentIndex, setCurrentIndex] = useState(0);\n\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      if (currentIndex < text.length) {\n        setDisplayText(text.slice(0, currentIndex + 1));\n        setCurrentIndex(currentIndex + 1);\n      }\n    }, currentIndex === 0 ? delay : speed);\n\n    return () => clearTimeout(timer);\n  }, [currentIndex, text, delay, speed]);\n\n  return (\n    <span>\n      {displayText}\n      <motion.span\n        animate={{ opacity: [1, 0] }}\n        transition={{ duration: 0.8, repeat: Infinity }}\n        className=\"inline-block w-0.5 h-6 bg-current ml-1\"\n      />\n    </span>\n  );\n};\n\n// Floating Elements\nexport const FloatingElements: React.FC = () => {\n  const elements = Array.from({ length: 15 }, (_, i) => ({\n    id: i,\n    size: Math.random() * 4 + 2,\n    x: Math.random() * 100,\n    y: Math.random() * 100,\n    duration: Math.random() * 10 + 10,\n    delay: Math.random() * 5,\n  }));\n\n  return (\n    <div className=\"fixed inset-0 pointer-events-none overflow-hidden\">\n      {elements.map((element) => (\n        <motion.div\n          key={element.id}\n          className=\"absolute rounded-full bg-gradient-to-r from-primary-400/20 to-gold-400/20\"\n          style={{\n            width: element.size,\n            height: element.size,\n            left: `${element.x}%`,\n            top: `${element.y}%`,\n          }}\n          animate={{\n            y: [-20, 20, -20],\n            x: [-10, 10, -10],\n            scale: [1, 1.2, 1],\n            opacity: [0.3, 0.6, 0.3],\n          }}\n          transition={{\n            duration: element.duration,\n            delay: element.delay,\n            repeat: Infinity,\n            ease: 'easeInOut',\n          }}\n        />\n      ))}\n    </div>\n  );\n};\n"], "mappings": ";;;;;;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,SAAS,EAAEC,YAAY,QAAQ,eAAe;;AAE/D;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,OAAO,MAAMC,kBAA2D,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC3F,MAAM;IAAEC;EAAQ,CAAC,GAAGP,SAAS,CAAC,CAAC;EAC/B,MAAMQ,CAAC,GAAGP,YAAY,CAACM,OAAO,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC;EAErD,oBACEJ,OAAA,CAACJ,MAAM,CAACU,GAAG;IAACC,KAAK,EAAE;MAAEF;IAAE,CAAE;IAACG,SAAS,EAAC,UAAU;IAAAN,QAAA,EAC3CA;EAAQ;IAAAO,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEjB,CAAC;;AAED;AAAAT,EAAA,CAXaF,kBAA2D;EAAA,QAClDJ,SAAS,EACnBC,YAAY;AAAA;AAAAe,EAAA,GAFXZ,kBAA2D;AAYxE,OAAO,MAAMa,cAAwB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EAC5C,MAAM;IAAEC;EAAgB,CAAC,GAAGnB,SAAS,CAAC,CAAC;EAEvC,oBACEG,OAAA,CAACJ,MAAM,CAACU,GAAG;IACTE,SAAS,EAAC,+FAA+F;IACzGD,KAAK,EAAE;MAAEU,MAAM,EAAED;IAAgB;EAAE;IAAAP,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpC,CAAC;AAEN,CAAC;;AAED;AAAAG,GAAA,CAXaD,cAAwB;EAAA,QACPjB,SAAS;AAAA;AAAAqB,GAAA,GAD1BJ,cAAwB;AAYrC,OAAO,MAAMK,WAAqB,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACzC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM;IAAES;EAAQ,CAAC,GAAGP,SAAS,CAAC,CAAC;EAE/BH,SAAS,CAAC,MAAM;IACd,MAAM6B,WAAW,GAAGnB,OAAO,CAACoB,QAAQ,CAAEC,MAAM,IAAK;MAC/CH,YAAY,CAACG,MAAM,GAAG,GAAG,CAAC;IAC5B,CAAC,CAAC;IACF,OAAOF,WAAW;EACpB,CAAC,EAAE,CAACnB,OAAO,CAAC,CAAC;EAEb,MAAMsB,WAAW,GAAGA,CAAA,KAAM;IACxBC,MAAM,CAACC,QAAQ,CAAC;MAAEC,GAAG,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EACjD,CAAC;EAED,oBACE9B,OAAA,CAACJ,MAAM,CAACmC,MAAM;IACZC,OAAO,EAAE;MAAEC,OAAO,EAAE,CAAC;MAAEC,KAAK,EAAE;IAAE,CAAE;IAClCC,OAAO,EAAE;MACPF,OAAO,EAAEZ,SAAS,GAAG,CAAC,GAAG,CAAC;MAC1Ba,KAAK,EAAEb,SAAS,GAAG,CAAC,GAAG;IACzB,CAAE;IACFe,UAAU,EAAE;MAAEF,KAAK,EAAE;IAAI,CAAE;IAC3BG,QAAQ,EAAE;MAAEH,KAAK,EAAE;IAAI,CAAE;IACzBI,OAAO,EAAEZ,WAAY;IACrBlB,SAAS,EAAC,0MAA0M;IAAAN,QAAA,eAEpNF,OAAA;MAAKQ,SAAS,EAAC,SAAS;MAAC+B,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,cAAc;MAACC,OAAO,EAAC,WAAW;MAAAvC,QAAA,eAC5EF,OAAA;QAAM0C,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAE,CAAE;QAACC,CAAC,EAAC;MAA2B;QAAApC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB,CAAC;;AAED;AAAAQ,GAAA,CAlCaD,WAAqB;EAAA,QAEZtB,SAAS;AAAA;AAAAiD,GAAA,GAFlB3B,WAAqB;AAmClC,OAAO,MAAM4B,cAIX,GAAGA,CAAC;EAAE7C,QAAQ;EAAE8C,SAAS,GAAG,IAAI;EAAEC,KAAK,GAAG;AAAE,CAAC,KAAK;EAClD,MAAMC,QAAQ,GAAG;IACfC,MAAM,EAAE;MACNlB,OAAO,EAAE,CAAC;MACV5B,CAAC,EAAE2C,SAAS,KAAK,IAAI,GAAG,EAAE,GAAGA,SAAS,KAAK,MAAM,GAAG,CAAC,EAAE,GAAG,CAAC;MAC3DI,CAAC,EAAEJ,SAAS,KAAK,MAAM,GAAG,EAAE,GAAGA,SAAS,KAAK,OAAO,GAAG,CAAC,EAAE,GAAG;IAC/D,CAAC;IACDK,OAAO,EAAE;MACPpB,OAAO,EAAE,CAAC;MACV5B,CAAC,EAAE,CAAC;MACJ+C,CAAC,EAAE,CAAC;MACJE,UAAU,EAAE;QACVC,QAAQ,EAAE,GAAG;QACbN,KAAK;QACLO,IAAI,EAAE,CAAC,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC;MAC3B;IACF;EACF,CAAC;EAED,oBACExD,OAAA,CAACJ,MAAM,CAACU,GAAG;IACT0B,OAAO,EAAC,QAAQ;IAChByB,WAAW,EAAC,SAAS;IACrBC,QAAQ,EAAE;MAAEC,IAAI,EAAE,IAAI;MAAEC,MAAM,EAAE;IAAS,CAAE;IAC3CV,QAAQ,EAAEA,QAAS;IAAAhD,QAAA,EAElBA;EAAQ;IAAAO,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEjB,CAAC;;AAED;AAAAiD,GAAA,GAnCad,cAIX;AAgCF,OAAO,MAAMe,gBAGX,GAAGA,CAAC;EAAE5D,QAAQ;EAAE6D,YAAY,GAAG;AAAI,CAAC,KAAK;EACzC,MAAMC,iBAAiB,GAAG;IACxBb,MAAM,EAAE;MAAElB,OAAO,EAAE;IAAE,CAAC;IACtBoB,OAAO,EAAE;MACPpB,OAAO,EAAE,CAAC;MACVqB,UAAU,EAAE;QACVW,eAAe,EAAEF;MACnB;IACF;EACF,CAAC;EAED,oBACE/D,OAAA,CAACJ,MAAM,CAACU,GAAG;IACT0B,OAAO,EAAC,QAAQ;IAChByB,WAAW,EAAC,SAAS;IACrBC,QAAQ,EAAE;MAAEC,IAAI,EAAE;IAAK,CAAE;IACzBT,QAAQ,EAAEc,iBAAkB;IAAA9D,QAAA,EAE3BA;EAAQ;IAAAO,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEjB,CAAC;;AAED;AAAAsD,GAAA,GA1BaJ,gBAGX;AAwBF,OAAO,MAAMK,cAIX,GAAGA,CAAC;EAAEjE,QAAQ;EAAEM,SAAS,GAAG,EAAE;EAAE8B;AAAQ,CAAC,KAAK;EAAA8B,GAAA;EAC9C,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAG3E,QAAQ,CAAC;IAAEyD,CAAC,EAAE,CAAC;IAAE/C,CAAC,EAAE;EAAE,CAAC,CAAC;EAExD,MAAMkE,eAAe,GAAIC,CAAsC,IAAK;IAClE,MAAMC,IAAI,GAAGD,CAAC,CAACE,aAAa,CAACC,qBAAqB,CAAC,CAAC;IACpD,MAAMC,OAAO,GAAGH,IAAI,CAACI,IAAI,GAAGJ,IAAI,CAACK,KAAK,GAAG,CAAC;IAC1C,MAAMC,OAAO,GAAGN,IAAI,CAAC5C,GAAG,GAAG4C,IAAI,CAACO,MAAM,GAAG,CAAC;IAC1C,MAAMC,MAAM,GAAG,CAACT,CAAC,CAACU,OAAO,GAAGN,OAAO,IAAI,IAAI;IAC3C,MAAMO,MAAM,GAAG,CAACX,CAAC,CAACY,OAAO,GAAGL,OAAO,IAAI,IAAI;IAC3CT,WAAW,CAAC;MAAElB,CAAC,EAAE6B,MAAM;MAAE5E,CAAC,EAAE8E;IAAO,CAAC,CAAC;EACvC,CAAC;EAED,MAAME,gBAAgB,GAAGA,CAAA,KAAM;IAC7Bf,WAAW,CAAC;MAAElB,CAAC,EAAE,CAAC;MAAE/C,CAAC,EAAE;IAAE,CAAC,CAAC;EAC7B,CAAC;EAED,oBACEL,OAAA,CAACJ,MAAM,CAACmC,MAAM;IACZvB,SAAS,EAAEA,SAAU;IACrB8B,OAAO,EAAEA,OAAQ;IACjBgD,WAAW,EAAEf,eAAgB;IAC7BgB,YAAY,EAAEF,gBAAiB;IAC/BlD,OAAO,EAAE;MAAEiB,CAAC,EAAEiB,QAAQ,CAACjB,CAAC;MAAE/C,CAAC,EAAEgE,QAAQ,CAAChE;IAAE,CAAE;IAC1CiD,UAAU,EAAE;MAAEkC,IAAI,EAAE,QAAQ;MAAEC,SAAS,EAAE,GAAG;MAAEC,OAAO,EAAE;IAAG,CAAE;IAC5DtD,UAAU,EAAE;MAAEF,KAAK,EAAE;IAAK,CAAE;IAC5BG,QAAQ,EAAE;MAAEH,KAAK,EAAE;IAAK,CAAE;IAAAhC,QAAA,EAEzBA;EAAQ;IAAAO,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEpB,CAAC;;AAED;AAAAwD,GAAA,CApCaD,cAIX;AAAAwB,GAAA,GAJWxB,cAIX;AAiCF,OAAO,MAAMyB,cAIX,GAAGA,CAAC;EAAEC,IAAI;EAAE5C,KAAK,GAAG,CAAC;EAAE6C,KAAK,GAAG;AAAG,CAAC,KAAK;EAAAC,GAAA;EACxC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGtG,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACuG,YAAY,EAAEC,eAAe,CAAC,GAAGxG,QAAQ,CAAC,CAAC,CAAC;EAEnDD,SAAS,CAAC,MAAM;IACd,MAAM0G,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7B,IAAIH,YAAY,GAAGL,IAAI,CAACS,MAAM,EAAE;QAC9BL,cAAc,CAACJ,IAAI,CAACU,KAAK,CAAC,CAAC,EAAEL,YAAY,GAAG,CAAC,CAAC,CAAC;QAC/CC,eAAe,CAACD,YAAY,GAAG,CAAC,CAAC;MACnC;IACF,CAAC,EAAEA,YAAY,KAAK,CAAC,GAAGjD,KAAK,GAAG6C,KAAK,CAAC;IAEtC,OAAO,MAAMU,YAAY,CAACJ,KAAK,CAAC;EAClC,CAAC,EAAE,CAACF,YAAY,EAAEL,IAAI,EAAE5C,KAAK,EAAE6C,KAAK,CAAC,CAAC;EAEtC,oBACE9F,OAAA;IAAAE,QAAA,GACG8F,WAAW,eACZhG,OAAA,CAACJ,MAAM,CAAC6G,IAAI;MACVtE,OAAO,EAAE;QAAEF,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC;MAAE,CAAE;MAC7BqB,UAAU,EAAE;QAAEC,QAAQ,EAAE,GAAG;QAAEmD,MAAM,EAAEC;MAAS,CAAE;MAChDnG,SAAS,EAAC;IAAwC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEX,CAAC;;AAED;AAAAmF,GAAA,CA/BaH,cAIX;AAAAgB,GAAA,GAJWhB,cAIX;AA4BF,OAAO,MAAMiB,gBAA0B,GAAGA,CAAA,KAAM;EAC9C,MAAMC,QAAQ,GAAGC,KAAK,CAACC,IAAI,CAAC;IAAEV,MAAM,EAAE;EAAG,CAAC,EAAE,CAACW,CAAC,EAAEC,CAAC,MAAM;IACrDC,EAAE,EAAED,CAAC;IACLE,IAAI,EAAEC,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;IAC3BlE,CAAC,EAAEiE,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;IACtBjH,CAAC,EAAEgH,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,GAAG;IACtB/D,QAAQ,EAAE8D,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE;IACjCrE,KAAK,EAAEoE,IAAI,CAACC,MAAM,CAAC,CAAC,GAAG;EACzB,CAAC,CAAC,CAAC;EAEH,oBACEtH,OAAA;IAAKQ,SAAS,EAAC,mDAAmD;IAAAN,QAAA,EAC/D4G,QAAQ,CAACS,GAAG,CAAEC,OAAO,iBACpBxH,OAAA,CAACJ,MAAM,CAACU,GAAG;MAETE,SAAS,EAAC,2EAA2E;MACrFD,KAAK,EAAE;QACLuE,KAAK,EAAE0C,OAAO,CAACJ,IAAI;QACnBpC,MAAM,EAAEwC,OAAO,CAACJ,IAAI;QACpBvC,IAAI,EAAE,GAAG2C,OAAO,CAACpE,CAAC,GAAG;QACrBvB,GAAG,EAAE,GAAG2F,OAAO,CAACnH,CAAC;MACnB,CAAE;MACF8B,OAAO,EAAE;QACP9B,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC;QACjB+C,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC;QACjBlB,KAAK,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QAClBD,OAAO,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG;MACzB,CAAE;MACFqB,UAAU,EAAE;QACVC,QAAQ,EAAEiE,OAAO,CAACjE,QAAQ;QAC1BN,KAAK,EAAEuE,OAAO,CAACvE,KAAK;QACpByD,MAAM,EAAEC,QAAQ;QAChBnD,IAAI,EAAE;MACR;IAAE,GAnBGgE,OAAO,CAACL,EAAE;MAAA1G,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAoBhB,CACF;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAAC6G,GAAA,GAtCWZ,gBAA0B;AAAA,IAAAhG,EAAA,EAAAK,GAAA,EAAA4B,GAAA,EAAAe,GAAA,EAAAK,GAAA,EAAAyB,GAAA,EAAAiB,GAAA,EAAAa,GAAA;AAAAC,YAAA,CAAA7G,EAAA;AAAA6G,YAAA,CAAAxG,GAAA;AAAAwG,YAAA,CAAA5E,GAAA;AAAA4E,YAAA,CAAA7D,GAAA;AAAA6D,YAAA,CAAAxD,GAAA;AAAAwD,YAAA,CAAA/B,GAAA;AAAA+B,YAAA,CAAAd,GAAA;AAAAc,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}