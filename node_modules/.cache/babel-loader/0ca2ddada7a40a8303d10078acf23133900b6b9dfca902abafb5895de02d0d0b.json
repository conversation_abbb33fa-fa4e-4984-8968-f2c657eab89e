{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/ADFD_Tracking_System/src/components/WorkflowSection.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WorkflowSection = () => {\n  const steps = [{\n    number: 1,\n    title: 'Initial Review',\n    description: 'Archive team creates request using OCR document upload. System auto-assigns to regional operations team.'\n  }, {\n    number: 2,\n    title: 'Technical Review',\n    description: 'Regional operations team evaluates technical requirements and makes approval/rejection decision.'\n  }, {\n    number: 3,\n    title: 'Core Banking',\n    description: 'Approved requests automatically move to core banking team for final disbursement processing.'\n  }, {\n    number: 4,\n    title: 'Disbursed',\n    description: 'Request completed successfully with full audit trail and stakeholder notifications.'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"workflow\",\n    className: \"workflow\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"section-header animate-on-scroll\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Streamlined 4-Stage Workflow\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Every request follows a proven process from creation to completion\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 30,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"workflow-steps\",\n        children: steps.map((step, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"workflow-step animate-on-scroll\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"step-number\",\n            children: step.number\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n            children: step.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            children: step.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this);\n};\n_c = WorkflowSection;\nexport default WorkflowSection;\nvar _c;\n$RefreshReg$(_c, \"WorkflowSection\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "WorkflowSection", "steps", "number", "title", "description", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "step", "index", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/ADFD_Tracking_System/src/components/WorkflowSection.tsx"], "sourcesContent": ["import React from 'react';\n\nconst WorkflowSection: React.FC = () => {\n  const steps = [\n    {\n      number: 1,\n      title: 'Initial Review',\n      description: 'Archive team creates request using OCR document upload. System auto-assigns to regional operations team.'\n    },\n    {\n      number: 2,\n      title: 'Technical Review',\n      description: 'Regional operations team evaluates technical requirements and makes approval/rejection decision.'\n    },\n    {\n      number: 3,\n      title: 'Core Banking',\n      description: 'Approved requests automatically move to core banking team for final disbursement processing.'\n    },\n    {\n      number: 4,\n      title: 'Disbursed',\n      description: 'Request completed successfully with full audit trail and stakeholder notifications.'\n    }\n  ];\n\n  return (\n    <section id=\"workflow\" className=\"workflow\">\n      <div className=\"container\">\n        <div className=\"section-header animate-on-scroll\">\n          <h2>Streamlined 4-Stage Workflow</h2>\n          <p>Every request follows a proven process from creation to completion</p>\n        </div>\n        <div className=\"workflow-steps\">\n          {steps.map((step, index) => (\n            <div key={index} className=\"workflow-step animate-on-scroll\">\n              <div className=\"step-number\">{step.number}</div>\n              <h4>{step.title}</h4>\n              <p>{step.description}</p>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default WorkflowSection;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EACtC,MAAMC,KAAK,GAAG,CACZ;IACEC,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE;EACf,CAAC,EACD;IACEF,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE,WAAW;IAClBC,WAAW,EAAE;EACf,CAAC,CACF;EAED,oBACEL,OAAA;IAASM,EAAE,EAAC,UAAU;IAACC,SAAS,EAAC,UAAU;IAAAC,QAAA,eACzCR,OAAA;MAAKO,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBR,OAAA;QAAKO,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/CR,OAAA;UAAAQ,QAAA,EAAI;QAA4B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrCZ,OAAA;UAAAQ,QAAA,EAAG;QAAkE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE,CAAC,eACNZ,OAAA;QAAKO,SAAS,EAAC,gBAAgB;QAAAC,QAAA,EAC5BN,KAAK,CAACW,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrBf,OAAA;UAAiBO,SAAS,EAAC,iCAAiC;UAAAC,QAAA,gBAC1DR,OAAA;YAAKO,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAEM,IAAI,CAACX;UAAM;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChDZ,OAAA;YAAAQ,QAAA,EAAKM,IAAI,CAACV;UAAK;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACrBZ,OAAA;YAAAQ,QAAA,EAAIM,IAAI,CAACT;UAAW;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA,GAHjBG,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAIV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACI,EAAA,GA3CIf,eAAyB;AA6C/B,eAAeA,eAAe;AAAC,IAAAe,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}