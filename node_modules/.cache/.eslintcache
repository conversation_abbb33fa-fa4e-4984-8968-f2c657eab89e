[{"/Users/<USER>/ADFD_Tracking_System/src/index.tsx": "1", "/Users/<USER>/ADFD_Tracking_System/src/reportWebVitals.ts": "2", "/Users/<USER>/ADFD_Tracking_System/src/App.tsx": "3", "/Users/<USER>/ADFD_Tracking_System/src/components/AnimatedBackground.tsx": "4", "/Users/<USER>/ADFD_Tracking_System/src/components/Navigation.tsx": "5", "/Users/<USER>/ADFD_Tracking_System/src/components/HeroSection.tsx": "6", "/Users/<USER>/ADFD_Tracking_System/src/components/StatsSection.tsx": "7", "/Users/<USER>/ADFD_Tracking_System/src/components/FeaturesSection.tsx": "8", "/Users/<USER>/ADFD_Tracking_System/src/components/WorkflowSection.tsx": "9", "/Users/<USER>/ADFD_Tracking_System/src/components/RolesSection.tsx": "10", "/Users/<USER>/ADFD_Tracking_System/src/components/TechnologySection.tsx": "11", "/Users/<USER>/ADFD_Tracking_System/src/components/DemoSection.tsx": "12", "/Users/<USER>/ADFD_Tracking_System/src/components/CTASection.tsx": "13", "/Users/<USER>/ADFD_Tracking_System/src/components/Footer.tsx": "14", "/Users/<USER>/ADFD_Tracking_System/src/components/SecuritySection.tsx": "15"}, {"size": 554, "mtime": 1753110560629, "results": "16", "hashOfConfig": "17"}, {"size": 425, "mtime": 1753110560630, "results": "18", "hashOfConfig": "17"}, {"size": 2196, "mtime": 1753165050249, "results": "19", "hashOfConfig": "17"}, {"size": 376, "mtime": 1753114336197, "results": "20", "hashOfConfig": "17"}, {"size": 933, "mtime": 1753165061510, "results": "21", "hashOfConfig": "17"}, {"size": 1487, "mtime": 1753165473809, "results": "22", "hashOfConfig": "17"}, {"size": 765, "mtime": 1753114365805, "results": "23", "hashOfConfig": "17"}, {"size": 2717, "mtime": 1753164813951, "results": "24", "hashOfConfig": "17"}, {"size": 1506, "mtime": 1753114397011, "results": "25", "hashOfConfig": "17"}, {"size": 2071, "mtime": 1753114412296, "results": "26", "hashOfConfig": "17"}, {"size": 2166, "mtime": 1753114425945, "results": "27", "hashOfConfig": "17"}, {"size": 3443, "mtime": 1753114447806, "results": "28", "hashOfConfig": "17"}, {"size": 720, "mtime": 1753114458877, "results": "29", "hashOfConfig": "17"}, {"size": 3758, "mtime": 1753115457064, "results": "30", "hashOfConfig": "17"}, {"size": 2768, "mtime": 1753165670813, "results": "31", "hashOfConfig": "17"}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "f9paq8", {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 33, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/ADFD_Tracking_System/src/index.tsx", [], [], "/Users/<USER>/ADFD_Tracking_System/src/reportWebVitals.ts", [], [], "/Users/<USER>/ADFD_Tracking_System/src/App.tsx", [], [], "/Users/<USER>/ADFD_Tracking_System/src/components/AnimatedBackground.tsx", [], [], "/Users/<USER>/ADFD_Tracking_System/src/components/Navigation.tsx", ["77"], [], "/Users/<USER>/ADFD_Tracking_System/src/components/HeroSection.tsx", [], [], "/Users/<USER>/ADFD_Tracking_System/src/components/StatsSection.tsx", [], [], "/Users/<USER>/ADFD_Tracking_System/src/components/FeaturesSection.tsx", [], [], "/Users/<USER>/ADFD_Tracking_System/src/components/WorkflowSection.tsx", [], [], "/Users/<USER>/ADFD_Tracking_System/src/components/RolesSection.tsx", [], [], "/Users/<USER>/ADFD_Tracking_System/src/components/TechnologySection.tsx", [], [], "/Users/<USER>/ADFD_Tracking_System/src/components/DemoSection.tsx", [], [], "/Users/<USER>/ADFD_Tracking_System/src/components/CTASection.tsx", ["78", "79"], [], "/Users/<USER>/ADFD_Tracking_System/src/components/Footer.tsx", ["80", "81", "82", "83", "84", "85", "86", "87", "88", "89", "90", "91", "92", "93", "94", "95", "96", "97", "98", "99", "100", "101", "102", "103", "104", "105", "106", "107", "108", "109", "110", "111", "112"], [], "/Users/<USER>/ADFD_Tracking_System/src/components/SecuritySection.tsx", [], [], {"ruleId": "113", "severity": 1, "message": "114", "line": 8, "column": 11, "nodeType": "115", "endLine": 8, "endColumn": 41}, {"ruleId": "113", "severity": 1, "message": "114", "line": 12, "column": 11, "nodeType": "115", "endLine": 12, "endColumn": 45}, {"ruleId": "113", "severity": 1, "message": "114", "line": 15, "column": 11, "nodeType": "115", "endLine": 15, "endColumn": 47}, {"ruleId": "113", "severity": 1, "message": "114", "line": 22, "column": 15, "nodeType": "115", "endLine": 22, "endColumn": 51}, {"ruleId": "113", "severity": 1, "message": "114", "line": 23, "column": 15, "nodeType": "115", "endLine": 23, "endColumn": 51}, {"ruleId": "113", "severity": 1, "message": "114", "line": 24, "column": 15, "nodeType": "115", "endLine": 24, "endColumn": 51}, {"ruleId": "113", "severity": 1, "message": "114", "line": 25, "column": 15, "nodeType": "115", "endLine": 25, "endColumn": 51}, {"ruleId": "113", "severity": 1, "message": "114", "line": 33, "column": 21, "nodeType": "115", "endLine": 33, "endColumn": 33}, {"ruleId": "113", "severity": 1, "message": "114", "line": 34, "column": 21, "nodeType": "115", "endLine": 34, "endColumn": 33}, {"ruleId": "113", "severity": 1, "message": "114", "line": 35, "column": 21, "nodeType": "115", "endLine": 35, "endColumn": 33}, {"ruleId": "113", "severity": 1, "message": "114", "line": 36, "column": 21, "nodeType": "115", "endLine": 36, "endColumn": 33}, {"ruleId": "113", "severity": 1, "message": "114", "line": 37, "column": 21, "nodeType": "115", "endLine": 37, "endColumn": 33}, {"ruleId": "113", "severity": 1, "message": "114", "line": 44, "column": 21, "nodeType": "115", "endLine": 44, "endColumn": 33}, {"ruleId": "113", "severity": 1, "message": "114", "line": 45, "column": 21, "nodeType": "115", "endLine": 45, "endColumn": 33}, {"ruleId": "113", "severity": 1, "message": "114", "line": 46, "column": 21, "nodeType": "115", "endLine": 46, "endColumn": 33}, {"ruleId": "113", "severity": 1, "message": "114", "line": 47, "column": 21, "nodeType": "115", "endLine": 47, "endColumn": 33}, {"ruleId": "113", "severity": 1, "message": "114", "line": 48, "column": 21, "nodeType": "115", "endLine": 48, "endColumn": 33}, {"ruleId": "113", "severity": 1, "message": "114", "line": 55, "column": 21, "nodeType": "115", "endLine": 55, "endColumn": 33}, {"ruleId": "113", "severity": 1, "message": "114", "line": 56, "column": 21, "nodeType": "115", "endLine": 56, "endColumn": 33}, {"ruleId": "113", "severity": 1, "message": "114", "line": 57, "column": 21, "nodeType": "115", "endLine": 57, "endColumn": 33}, {"ruleId": "113", "severity": 1, "message": "114", "line": 58, "column": 21, "nodeType": "115", "endLine": 58, "endColumn": 33}, {"ruleId": "113", "severity": 1, "message": "114", "line": 59, "column": 21, "nodeType": "115", "endLine": 59, "endColumn": 33}, {"ruleId": "113", "severity": 1, "message": "114", "line": 66, "column": 21, "nodeType": "115", "endLine": 66, "endColumn": 33}, {"ruleId": "113", "severity": 1, "message": "114", "line": 67, "column": 21, "nodeType": "115", "endLine": 67, "endColumn": 33}, {"ruleId": "113", "severity": 1, "message": "114", "line": 68, "column": 21, "nodeType": "115", "endLine": 68, "endColumn": 33}, {"ruleId": "113", "severity": 1, "message": "114", "line": 69, "column": 21, "nodeType": "115", "endLine": 69, "endColumn": 33}, {"ruleId": "113", "severity": 1, "message": "114", "line": 70, "column": 21, "nodeType": "115", "endLine": 70, "endColumn": 33}, {"ruleId": "113", "severity": 1, "message": "114", "line": 77, "column": 21, "nodeType": "115", "endLine": 77, "endColumn": 33}, {"ruleId": "113", "severity": 1, "message": "114", "line": 78, "column": 21, "nodeType": "115", "endLine": 78, "endColumn": 33}, {"ruleId": "113", "severity": 1, "message": "114", "line": 79, "column": 21, "nodeType": "115", "endLine": 79, "endColumn": 33}, {"ruleId": "113", "severity": 1, "message": "114", "line": 80, "column": 21, "nodeType": "115", "endLine": 80, "endColumn": 33}, {"ruleId": "113", "severity": 1, "message": "114", "line": 81, "column": 21, "nodeType": "115", "endLine": 81, "endColumn": 33}, {"ruleId": "113", "severity": 1, "message": "114", "line": 92, "column": 17, "nodeType": "115", "endLine": 92, "endColumn": 29}, {"ruleId": "113", "severity": 1, "message": "114", "line": 93, "column": 17, "nodeType": "115", "endLine": 93, "endColumn": 29}, {"ruleId": "113", "severity": 1, "message": "114", "line": 94, "column": 17, "nodeType": "115", "endLine": 94, "endColumn": 29}, {"ruleId": "113", "severity": 1, "message": "114", "line": 95, "column": 17, "nodeType": "115", "endLine": 95, "endColumn": 29}, "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement"]