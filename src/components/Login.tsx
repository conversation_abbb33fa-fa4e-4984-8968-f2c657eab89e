import React, { useState } from 'react';
import logo from '../assets/logo.png';

interface LoginProps {
  onLogin: (user: User) => void;
}

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  department: string;
  region?: string;
  permissions: string[];
}

const Login: React.FC<LoginProps> = ({ onLogin }) => {
  const [email, setEmail] = useState('');
  const [department, setDepartment] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [showResetPassword, setShowResetPassword] = useState(false);
  const [resetEmail, setResetEmail] = useState('');
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);

  const departments = [
    'Treasury Operations',
    'Risk Management',
    'Compliance & Audit',
    'Investment Management',
    'Corporate Banking',
    'Retail Banking',
    'IT & Technology',
    'Human Resources',
    'Legal Affairs',
    'Executive Management'
  ];

  // Demo users based on the production architecture plan
  const demoUsers: User[] = [
    {
      id: 'admin_001',
      name: 'Sarah Al-Mansouri',
      email: '<EMAIL>',
      role: 'admin',
      department: 'Executive Management',
      permissions: ['create', 'approve', 'reject', 'disburse', 'view_all', 'comment', 'manage_users']
    },
    {
      id: 'treasury_001',
      name: 'Mohammed Al-Rashid',
      email: '<EMAIL>',
      role: 'treasury_manager',
      department: 'Treasury Operations',
      permissions: ['approve', 'reject', 'view_assigned', 'comment']
    },
    {
      id: 'risk_001',
      name: 'Fatima Al-Zahra',
      email: '<EMAIL>',
      role: 'risk_analyst',
      department: 'Risk Management',
      permissions: ['view_assigned', 'comment', 'reports']
    }
  ];

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      await new Promise(resolve => setTimeout(resolve, 1000));

      const user = demoUsers.find(u =>
        u.email.toLowerCase() === email.toLowerCase() &&
        u.department === department
      );

      if (!user) {
        setError('Invalid credentials or department mismatch.');
        return;
      }

      if (password.length < 6) {
        setError('Password must be at least 6 characters.');
        return;
      }

      onLogin(user);

    } catch (error) {
      setError('Login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentPassword || !newPassword) {
      setError('Please fill in all password fields');
      return;
    }
    
    // Simulate password change
    alert('Password changed successfully!');
    setShowResetPassword(false);
    setResetEmail('');
    setCurrentPassword('');
    setNewPassword('');
    setError('');
  };

  if (showResetPassword) {
    return (
      <div className="min-h-screen flex">
        {/* Left Side - Password Change Form */}
        <div className="w-1/2 bg-white flex items-center justify-center p-8">
          <div className="max-w-sm w-full space-y-6">
            <div className="text-center mb-8">
              <div className="inline-flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-orange-500 rounded">
                  <div className="w-4 h-4 bg-orange-600 rounded m-1"></div>
                </div>
                <span className="text-xl font-semibold text-gray-900">ADFD</span>
              </div>
              <h1 className="text-2xl font-bold text-gray-900">Change Password</h1>
            </div>
            
            <form onSubmit={handleResetPassword} className="space-y-4">
              {/* Email Field */}
              <div>
                <input
                  id="resetEmail"
                  type="email"
                  autoComplete="email"
                  required
                  value={resetEmail}
                  onChange={(e) => setResetEmail(e.target.value)}
                  className="appearance-none relative block w-full px-3 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                  placeholder="Email address"
                />
              </div>

              {/* Current Password */}
              <div className="relative">
                <input
                  id="currentPassword"
                  type={showCurrentPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  required
                  value={currentPassword}
                  onChange={(e) => setCurrentPassword(e.target.value)}
                  className="appearance-none relative block w-full px-3 py-3 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                  placeholder="Enter current password"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                >
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    {showCurrentPassword ? (
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                    ) : (
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    )}
                  </svg>
                </button>
              </div>

              {/* New Password */}
              <div className="relative">
                <input
                  id="newPassword"
                  type={showNewPassword ? 'text' : 'password'}
                  autoComplete="new-password"
                  required
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  className="appearance-none relative block w-full px-3 py-3 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                  placeholder="Enter new password"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowNewPassword(!showNewPassword)}
                >
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    {showNewPassword ? (
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                    ) : (
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    )}
                  </svg>
                </button>
              </div>

              {/* Error Message */}
              {error && (
                <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
                  {error}
                </div>
              )}

              {/* Submit Button */}
              <div>
                <button
                  type="submit"
                  className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Update Password
                </button>
              </div>
            </form>

            {/* Back to Login */}
            <div className="text-center">
              <button
                type="button"
                onClick={() => {
                  setShowResetPassword(false);
                  setError('');
                }}
                className="text-sm text-blue-600 hover:text-blue-500"
              >
                ← Back to Login
              </button>
            </div>
          </div>
        </div>

        {/* Right Side - Visual */}
        <div className="w-1/2 bg-gradient-to-br from-gray-700 via-gray-600 to-blue-700 flex items-center justify-center relative overflow-hidden" style={{backgroundColor: '#4a5568'}}>
          <div className="absolute inset-0 bg-gradient-to-br from-black/30 to-transparent"></div>
          <div className="relative text-center text-white p-8">
            <div className="mb-4">
              <img src={logo} alt="Quandrox Logo" className="h-12 mx-auto mb-3 filter brightness-0 invert" />
              <h2 className="text-3xl font-bold text-blue-300">Password Reset</h2>
            </div>
            <p className="text-lg opacity-90">Update your credentials safely</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex">
      {/* Left Side - Login Form */}
      <div className="w-1/2 bg-white flex items-center justify-center p-8">
        <div className="max-w-sm w-full space-y-6">
          <div className="text-center mb-8">
            <div className="inline-flex items-center space-x-2 mb-4">
              <img src={logo} alt="Quandrox Logo" className="h-10" />
              <span className="text-xl font-semibold text-blue-600">Quandrox</span>
            </div>
            <h1 className="text-2xl font-bold text-gray-900">Access Quandrox Platform</h1>
            <p className="text-sm text-gray-500 mt-2">Authorized Personnel Only</p>
          </div>

          <form onSubmit={handleLogin} className="space-y-4">
            {/* Email Field */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                Email Address
              </label>
              <input
                id="email"
                type="email"
                autoComplete="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="appearance-none relative block w-full px-3 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                placeholder="<EMAIL>"
              />
            </div>

            {/* Department Selection Dropdown */}
            <div>
              <label htmlFor="department" className="block text-sm font-medium text-gray-700 mb-1">
                Department
              </label>
              <select
                id="department"
                value={department}
                onChange={(e) => setDepartment(e.target.value)}
                required
                className="appearance-none relative block w-full px-3 py-3 border border-gray-300 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
              >
                <option value="">Select your department</option>
                {departments.map((dept) => (
                  <option key={dept} value={dept}>{dept}</option>
                ))}
              </select>
            </div>

            {/* Password Field */}
            <div className="relative">
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                Password
              </label>
              <input
                id="password"
                type={showPassword ? 'text' : 'password'}
                autoComplete="current-password"
                required
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="appearance-none relative block w-full px-3 py-3 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                placeholder="Enter your password"
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
                onClick={() => setShowPassword(!showPassword)}
              >
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  {showPassword ? (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                  ) : (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  )}
                </svg>
              </button>
            </div>

            {/* Error Message */}
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
                {error}
              </div>
            )}

            {/* Submit Button */}
            <div>
              <button
                type="submit"
                disabled={isLoading || !department}
                className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? 'Signing in...' : 'Sign In'}
              </button>
            </div>
          </form>

          {/* Forgot Password Link */}
          <div className="text-center">
            <button
              type="button"
              onClick={() => setShowResetPassword(true)}
              className="text-sm text-blue-600 hover:text-blue-500 font-medium"
            >
              Forgot Password?
            </button>
          </div>
        </div>
      </div>

      {/* Right Side - Visual */}
      <div className="w-1/2 bg-gradient-to-br from-gray-700 via-gray-600 to-blue-700 flex items-center justify-center relative overflow-hidden" style={{backgroundColor: '#4a5568'}}>
        <div className="absolute inset-0 bg-gradient-to-br from-black/30 to-transparent"></div>
        <div className="relative text-center text-white p-8">
          <div className="mb-6">
            <img src={logo} alt="Quandrox Logo" className="h-16 mx-auto mb-4 filter brightness-0 invert" />
            <h2 className="text-4xl font-bold text-blue-300">Quandrox</h2>
            <p className="text-lg text-blue-200">Tracking System</p>
          </div>
          <h3 className="text-3xl font-bold mb-6 text-white">Enterprise Financial Operations</h3>
          <p className="text-lg mb-8 opacity-90">Secure, intelligent tracking for authorized personnel</p>

          {/* Abstract Network Visualization */}
          <div className="relative mx-auto w-64 h-64 mb-8">
            <div className="absolute inset-0 rounded-full border border-blue-300/20 animate-pulse"></div>
            <div className="absolute inset-4 rounded-full border border-blue-300/30 animate-pulse" style={{animationDelay: '0.5s'}}></div>
            <div className="absolute inset-8 rounded-full border border-blue-300/40 animate-pulse" style={{animationDelay: '1s'}}></div>
            <div className="absolute inset-16 rounded-full bg-blue-500/20 backdrop-blur-sm flex items-center justify-center">
              <svg className="w-16 h-16 text-blue-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
            </div>
          </div>

          <div className="space-y-3 opacity-90">
            <div className="flex items-center justify-center space-x-3">
              <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
              <span className="text-sm">Enterprise Security</span>
            </div>
            <div className="flex items-center justify-center space-x-3">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse" style={{animationDelay: '0.3s'}}></div>
              <span className="text-sm">Real-time Tracking</span>
            </div>
            <div className="flex items-center justify-center space-x-3">
              <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse" style={{animationDelay: '0.6s'}}></div>
              <span className="text-sm">Intelligent Automation</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
