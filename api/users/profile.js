// Vercel serverless function for user profile management
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', process.env.FRONTEND_URL || 'http://localhost:3000')
  res.setHeader('Access-Control-Allow-Methods', 'GET, PUT, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization')

  if (req.method === 'OPTIONS') {
    return res.status(200).end()
  }

  if (!['GET', 'PUT'].includes(req.method)) {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const authHeader = req.headers.authorization
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'No valid token provided' })
    }

    const token = authHeader.substring(7)

    // Verify user
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    if (authError || !user) {
      return res.status(401).json({ error: 'Invalid token' })
    }

    if (req.method === 'GET') {
      // Get user profile
      const { data: profile, error: profileError } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', user.id)
        .single()

      if (profileError) {
        return res.status(404).json({ 
          error: 'User profile not found',
          details: profileError.message 
        })
      }

      return res.status(200).json({
        success: true,
        profile: {
          id: profile.id,
          full_name: profile.full_name,
          email: user.email,
          role: profile.role,
          region: profile.region,
          regional_countries: profile.regional_countries,
          permissions: {
            can_create_requests: profile.can_create_requests,
            can_approve_reject: profile.can_approve_reject,
            can_disburse: profile.can_disburse,
            view_only_access: profile.view_only_access
          },
          is_active: profile.is_active,
          avatar_url: profile.avatar_url,
          created_at: profile.created_at,
          updated_at: profile.updated_at
        }
      })

    } else if (req.method === 'PUT') {
      // Update user profile (limited fields)
      const { full_name, avatar_url } = req.body

      if (!full_name) {
        return res.status(400).json({ error: 'Full name is required' })
      }

      const { data: updatedProfile, error: updateError } = await supabase
        .from('user_profiles')
        .update({
          full_name,
          avatar_url,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id)
        .select()
        .single()

      if (updateError) {
        return res.status(500).json({ 
          error: 'Failed to update profile',
          details: updateError.message 
        })
      }

      return res.status(200).json({
        success: true,
        message: 'Profile updated successfully',
        profile: {
          id: updatedProfile.id,
          full_name: updatedProfile.full_name,
          email: user.email,
          role: updatedProfile.role,
          region: updatedProfile.region,
          avatar_url: updatedProfile.avatar_url,
          updated_at: updatedProfile.updated_at
        }
      })
    }

  } catch (error) {
    console.error('Profile management error:', error)
    return res.status(500).json({ 
      error: 'Internal server error',
      details: error.message 
    })
  }
}
