{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/ADFD_Tracking_System/src/components/Sections/RegionalSection.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Globe, MapPin, Users, ArrowRight } from 'lucide-react';\nimport Card from '../UI/Card';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RegionalSection = () => {\n  _s();\n  const [activeRegion, setActiveRegion] = useState(0);\n  const regions = [{\n    name: 'Europe & Latin America',\n    lead: '<PERSON>',\n    email: '<EMAIL>',\n    color: 'from-blue-500 to-blue-600',\n    countries: ['Spain', 'Portugal', 'Italy', 'France', 'Germany', 'UK', 'Brazil', 'Argentina', 'Chile', 'Colombia', 'Mexico', 'Peru'],\n    stats: {\n      countries: 12,\n      projects: '150+',\n      volume: '$2.5B'\n    },\n    description: 'Managing diverse markets across European and Latin American regions with specialized expertise in both developed and emerging economies.'\n  }, {\n    name: 'Africa',\n    lead: '<PERSON>',\n    email: '<EMAIL>',\n    color: 'from-green-500 to-green-600',\n    countries: ['Egypt', 'Libya', 'Tunisia', 'Algeria', 'Morocco', 'Sudan', 'Chad', 'CAR', 'DRC', 'Cameroon', 'Nigeria', 'Kenya', 'Ethiopia', 'Ghana', 'Senegal'],\n    stats: {\n      countries: 15,\n      projects: '200+',\n      volume: '$3.2B'\n    },\n    description: 'Comprehensive coverage across North, Central, and West Africa, supporting sustainable development and economic growth initiatives.'\n  }, {\n    name: 'Asia',\n    lead: 'Abdulla Al Mansoori',\n    email: '<EMAIL>',\n    color: 'from-purple-500 to-purple-600',\n    countries: ['Malaysia', 'Indonesia', 'Thailand', 'Vietnam', 'Philippines', 'Singapore', 'Kazakhstan', 'Uzbekistan', 'Kyrgyzstan', 'Tajikistan', 'Afghanistan', 'Pakistan', 'India', 'Bangladesh'],\n    stats: {\n      countries: 14,\n      projects: '180+',\n      volume: '$2.8B'\n    },\n    description: 'Strategic operations across Southeast Asia and Central Asia, fostering regional cooperation and sustainable development.'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"regions\",\n    className: \"section-padding bg-gradient-to-br from-navy-50 to-primary-50\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-custom\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 30\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8\n        },\n        className: \"text-center mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-4xl md:text-5xl font-bold text-navy-900 mb-6\",\n          children: [\"Global Regional\", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"gradient-text block\",\n            children: \"Operations\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-navy-600 max-w-3xl mx-auto leading-relaxed\",\n          children: \"Three specialized regional teams managing withdrawal requests across 41 countries with dedicated leadership and local expertise.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 20\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.6\n        },\n        className: \"flex flex-wrap justify-center gap-4 mb-12\",\n        children: regions.map((region, index) => /*#__PURE__*/_jsxDEV(motion.button, {\n          onClick: () => setActiveRegion(index),\n          whileHover: {\n            scale: 1.05\n          },\n          whileTap: {\n            scale: 0.95\n          },\n          className: `px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${activeRegion === index ? `bg-gradient-to-r ${region.color} text-white shadow-lg` : 'bg-white text-navy-700 hover:bg-navy-50 border border-navy-200'}`,\n          children: region.name\n        }, region.name, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n        mode: \"wait\",\n        children: /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          animate: {\n            opacity: 1,\n            y: 0\n          },\n          exit: {\n            opacity: 0,\n            y: -30\n          },\n          transition: {\n            duration: 0.5\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                className: \"p-8\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `w-12 h-12 bg-gradient-to-r ${regions[activeRegion].color} rounded-lg flex items-center justify-center mr-4`,\n                    children: /*#__PURE__*/_jsxDEV(Globe, {\n                      className: \"w-6 h-6 text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 119,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 118,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                      className: \"text-2xl font-bold text-navy-900\",\n                      children: regions[activeRegion].name\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 122,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-navy-600\",\n                      children: \"Regional Operations\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 125,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 121,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 117,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-navy-600 mb-6 leading-relaxed\",\n                  children: regions[activeRegion].description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 129,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"bg-navy-50 rounded-lg p-4 mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mb-2\",\n                    children: [/*#__PURE__*/_jsxDEV(Users, {\n                      className: \"w-5 h-5 text-navy-600 mr-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 136,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"font-semibold text-navy-900\",\n                      children: \"Regional Lead\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 137,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 135,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-navy-800 font-medium\",\n                    children: regions[activeRegion].lead\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 139,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-navy-600 text-sm\",\n                    children: regions[activeRegion].email\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 140,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 134,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-3 gap-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-2xl font-bold text-navy-900\",\n                      children: regions[activeRegion].stats.countries\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 146,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-navy-600\",\n                      children: \"Countries\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 149,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 145,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-2xl font-bold text-navy-900\",\n                      children: regions[activeRegion].stats.projects\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 152,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-navy-600\",\n                      children: \"Projects\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 155,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 151,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-center\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-2xl font-bold text-navy-900\",\n                      children: regions[activeRegion].stats.volume\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 158,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"text-sm text-navy-600\",\n                      children: \"Volume\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 161,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 157,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 116,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 115,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: /*#__PURE__*/_jsxDEV(Card, {\n                className: \"p-8\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(MapPin, {\n                    className: \"w-6 h-6 text-navy-600 mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 171,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n                    className: \"text-xl font-bold text-navy-900\",\n                    children: \"Covered Countries\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 172,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"grid grid-cols-2 sm:grid-cols-3 gap-3\",\n                  children: regions[activeRegion].countries.map((country, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n                    initial: {\n                      opacity: 0,\n                      scale: 0.8\n                    },\n                    animate: {\n                      opacity: 1,\n                      scale: 1\n                    },\n                    transition: {\n                      delay: index * 0.05\n                    },\n                    className: \"bg-white border border-navy-200 rounded-lg p-3 text-center hover:shadow-md transition-shadow duration-300\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-navy-700 font-medium text-sm\",\n                      children: country\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 186,\n                      columnNumber: 25\n                    }, this)\n                  }, country, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 179,\n                    columnNumber: 23\n                  }, this))\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 177,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0\n                  },\n                  animate: {\n                    opacity: 1\n                  },\n                  transition: {\n                    delay: 0.5\n                  },\n                  className: \"mt-6 pt-6 border-t border-navy-200\",\n                  children: /*#__PURE__*/_jsxDEV(\"button\", {\n                    className: \"flex items-center text-primary-600 hover:text-primary-700 font-medium transition-colors duration-300\",\n                    children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                      children: \"View Regional Dashboard\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 200,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(ArrowRight, {\n                      className: \"w-4 h-4 ml-2\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 201,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 193,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 113,\n            columnNumber: 13\n          }, this)\n        }, activeRegion, false, {\n          fileName: _jsxFileName,\n          lineNumber: 106,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 105,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 30\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8,\n          delay: 0.3\n        },\n        className: \"mt-16\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"p-8 bg-gradient-to-r from-navy-900 to-navy-800 text-white\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n              className: \"text-3xl font-bold mb-4\",\n              children: \"Global Impact & Reach\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 219,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-navy-200 mb-8 max-w-3xl mx-auto\",\n              children: \"Coordinated operations across three major regions, ensuring efficient fund disbursement and sustainable development impact worldwide.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-4xl font-bold text-gold-400 mb-2\",\n                  children: \"41\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 229,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-navy-300\",\n                  children: \"Total Countries\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-4xl font-bold text-gold-400 mb-2\",\n                  children: \"530+\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-navy-300\",\n                  children: \"Active Projects\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 234,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 232,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-4xl font-bold text-gold-400 mb-2\",\n                  children: \"$8.5B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-navy-300\",\n                  children: \"Total Volume\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 238,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 236,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-4xl font-bold text-gold-400 mb-2\",\n                  children: \"24/7\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 241,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"text-navy-300\",\n                  children: \"Operations\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 242,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 240,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 218,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 217,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 211,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 61,\n    columnNumber: 5\n  }, this);\n};\n_s(RegionalSection, \"yQxim3qADBOmpfLvL5xTemkvW2o=\");\n_c = RegionalSection;\nexport default RegionalSection;\nvar _c;\n$RefreshReg$(_c, \"RegionalSection\");", "map": {"version": 3, "names": ["React", "useState", "motion", "AnimatePresence", "Globe", "MapPin", "Users", "ArrowRight", "Card", "jsxDEV", "_jsxDEV", "RegionalSection", "_s", "activeRegion", "setActiveRegion", "regions", "name", "lead", "email", "color", "countries", "stats", "projects", "volume", "description", "id", "className", "children", "div", "initial", "opacity", "y", "whileInView", "transition", "duration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "region", "index", "button", "onClick", "whileHover", "scale", "whileTap", "mode", "animate", "exit", "country", "delay", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/ADFD_Tracking_System/src/components/Sections/RegionalSection.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { Globe, MapPin, Users, ArrowRight } from 'lucide-react';\nimport Card from '../UI/Card';\n\nconst RegionalSection: React.FC = () => {\n  const [activeRegion, setActiveRegion] = useState(0);\n\n  const regions = [\n    {\n      name: 'Europe & Latin America',\n      lead: '<PERSON>',\n      email: '<EMAIL>',\n      color: 'from-blue-500 to-blue-600',\n      countries: [\n        'Spain', 'Portugal', 'Italy', 'France', 'Germany', 'UK',\n        'Brazil', 'Argentina', 'Chile', 'Colombia', 'Mexico', 'Peru'\n      ],\n      stats: {\n        countries: 12,\n        projects: '150+',\n        volume: '$2.5B'\n      },\n      description: 'Managing diverse markets across European and Latin American regions with specialized expertise in both developed and emerging economies.'\n    },\n    {\n      name: 'Africa',\n      lead: '<PERSON>',\n      email: '<EMAIL>',\n      color: 'from-green-500 to-green-600',\n      countries: [\n        'Egypt', 'Libya', 'Tunisia', 'Algeria', 'Morocco', 'Sudan',\n        'Chad', 'CAR', 'DRC', 'Cameroon', 'Nigeria', 'Kenya', 'Ethiopia', 'Ghana', 'Senegal'\n      ],\n      stats: {\n        countries: 15,\n        projects: '200+',\n        volume: '$3.2B'\n      },\n      description: 'Comprehensive coverage across North, Central, and West Africa, supporting sustainable development and economic growth initiatives.'\n    },\n    {\n      name: 'Asia',\n      lead: 'Abdulla Al Mansoori',\n      email: '<EMAIL>',\n      color: 'from-purple-500 to-purple-600',\n      countries: [\n        'Malaysia', 'Indonesia', 'Thailand', 'Vietnam', 'Philippines', 'Singapore',\n        'Kazakhstan', 'Uzbekistan', 'Kyrgyzstan', 'Tajikistan', 'Afghanistan', 'Pakistan', 'India', 'Bangladesh'\n      ],\n      stats: {\n        countries: 14,\n        projects: '180+',\n        volume: '$2.8B'\n      },\n      description: 'Strategic operations across Southeast Asia and Central Asia, fostering regional cooperation and sustainable development.'\n    }\n  ];\n\n  return (\n    <section id=\"regions\" className=\"section-padding bg-gradient-to-br from-navy-50 to-primary-50\">\n      <div className=\"container-custom\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl md:text-5xl font-bold text-navy-900 mb-6\">\n            Global Regional\n            <span className=\"gradient-text block\">Operations</span>\n          </h2>\n          <p className=\"text-xl text-navy-600 max-w-3xl mx-auto leading-relaxed\">\n            Three specialized regional teams managing withdrawal requests across 41 countries \n            with dedicated leadership and local expertise.\n          </p>\n        </motion.div>\n\n        {/* Region Selector */}\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"flex flex-wrap justify-center gap-4 mb-12\"\n        >\n          {regions.map((region, index) => (\n            <motion.button\n              key={region.name}\n              onClick={() => setActiveRegion(index)}\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className={`px-6 py-3 rounded-lg font-semibold transition-all duration-300 ${\n                activeRegion === index\n                  ? `bg-gradient-to-r ${region.color} text-white shadow-lg`\n                  : 'bg-white text-navy-700 hover:bg-navy-50 border border-navy-200'\n              }`}\n            >\n              {region.name}\n            </motion.button>\n          ))}\n        </motion.div>\n\n        {/* Active Region Content */}\n        <AnimatePresence mode=\"wait\">\n          <motion.div\n            key={activeRegion}\n            initial={{ opacity: 0, y: 30 }}\n            animate={{ opacity: 1, y: 0 }}\n            exit={{ opacity: 0, y: -30 }}\n            transition={{ duration: 0.5 }}\n          >\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\">\n              {/* Region Info */}\n              <div>\n                <Card className=\"p-8\">\n                  <div className=\"flex items-center mb-6\">\n                    <div className={`w-12 h-12 bg-gradient-to-r ${regions[activeRegion].color} rounded-lg flex items-center justify-center mr-4`}>\n                      <Globe className=\"w-6 h-6 text-white\" />\n                    </div>\n                    <div>\n                      <h3 className=\"text-2xl font-bold text-navy-900\">\n                        {regions[activeRegion].name}\n                      </h3>\n                      <p className=\"text-navy-600\">Regional Operations</p>\n                    </div>\n                  </div>\n\n                  <p className=\"text-navy-600 mb-6 leading-relaxed\">\n                    {regions[activeRegion].description}\n                  </p>\n\n                  {/* Regional Lead */}\n                  <div className=\"bg-navy-50 rounded-lg p-4 mb-6\">\n                    <div className=\"flex items-center mb-2\">\n                      <Users className=\"w-5 h-5 text-navy-600 mr-2\" />\n                      <span className=\"font-semibold text-navy-900\">Regional Lead</span>\n                    </div>\n                    <p className=\"text-navy-800 font-medium\">{regions[activeRegion].lead}</p>\n                    <p className=\"text-navy-600 text-sm\">{regions[activeRegion].email}</p>\n                  </div>\n\n                  {/* Stats */}\n                  <div className=\"grid grid-cols-3 gap-4\">\n                    <div className=\"text-center\">\n                      <div className=\"text-2xl font-bold text-navy-900\">\n                        {regions[activeRegion].stats.countries}\n                      </div>\n                      <div className=\"text-sm text-navy-600\">Countries</div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className=\"text-2xl font-bold text-navy-900\">\n                        {regions[activeRegion].stats.projects}\n                      </div>\n                      <div className=\"text-sm text-navy-600\">Projects</div>\n                    </div>\n                    <div className=\"text-center\">\n                      <div className=\"text-2xl font-bold text-navy-900\">\n                        {regions[activeRegion].stats.volume}\n                      </div>\n                      <div className=\"text-sm text-navy-600\">Volume</div>\n                    </div>\n                  </div>\n                </Card>\n              </div>\n\n              {/* Countries Grid */}\n              <div>\n                <Card className=\"p-8\">\n                  <div className=\"flex items-center mb-6\">\n                    <MapPin className=\"w-6 h-6 text-navy-600 mr-2\" />\n                    <h4 className=\"text-xl font-bold text-navy-900\">\n                      Covered Countries\n                    </h4>\n                  </div>\n                  \n                  <div className=\"grid grid-cols-2 sm:grid-cols-3 gap-3\">\n                    {regions[activeRegion].countries.map((country, index) => (\n                      <motion.div\n                        key={country}\n                        initial={{ opacity: 0, scale: 0.8 }}\n                        animate={{ opacity: 1, scale: 1 }}\n                        transition={{ delay: index * 0.05 }}\n                        className=\"bg-white border border-navy-200 rounded-lg p-3 text-center hover:shadow-md transition-shadow duration-300\"\n                      >\n                        <span className=\"text-navy-700 font-medium text-sm\">\n                          {country}\n                        </span>\n                      </motion.div>\n                    ))}\n                  </div>\n\n                  <motion.div\n                    initial={{ opacity: 0 }}\n                    animate={{ opacity: 1 }}\n                    transition={{ delay: 0.5 }}\n                    className=\"mt-6 pt-6 border-t border-navy-200\"\n                  >\n                    <button className=\"flex items-center text-primary-600 hover:text-primary-700 font-medium transition-colors duration-300\">\n                      <span>View Regional Dashboard</span>\n                      <ArrowRight className=\"w-4 h-4 ml-2\" />\n                    </button>\n                  </motion.div>\n                </Card>\n              </div>\n            </div>\n          </motion.div>\n        </AnimatePresence>\n\n        {/* Global Overview */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.3 }}\n          className=\"mt-16\"\n        >\n          <Card className=\"p-8 bg-gradient-to-r from-navy-900 to-navy-800 text-white\">\n            <div className=\"text-center\">\n              <h3 className=\"text-3xl font-bold mb-4\">\n                Global Impact & Reach\n              </h3>\n              <p className=\"text-navy-200 mb-8 max-w-3xl mx-auto\">\n                Coordinated operations across three major regions, ensuring efficient \n                fund disbursement and sustainable development impact worldwide.\n              </p>\n              \n              <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n                <div>\n                  <div className=\"text-4xl font-bold text-gold-400 mb-2\">41</div>\n                  <div className=\"text-navy-300\">Total Countries</div>\n                </div>\n                <div>\n                  <div className=\"text-4xl font-bold text-gold-400 mb-2\">530+</div>\n                  <div className=\"text-navy-300\">Active Projects</div>\n                </div>\n                <div>\n                  <div className=\"text-4xl font-bold text-gold-400 mb-2\">$8.5B</div>\n                  <div className=\"text-navy-300\">Total Volume</div>\n                </div>\n                <div>\n                  <div className=\"text-4xl font-bold text-gold-400 mb-2\">24/7</div>\n                  <div className=\"text-navy-300\">Operations</div>\n                </div>\n              </div>\n            </div>\n          </Card>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default RegionalSection;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,SAASC,KAAK,EAAEC,MAAM,EAAEC,KAAK,EAAEC,UAAU,QAAQ,cAAc;AAC/D,OAAOC,IAAI,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGb,QAAQ,CAAC,CAAC,CAAC;EAEnD,MAAMc,OAAO,GAAG,CACd;IACEC,IAAI,EAAE,wBAAwB;IAC9BC,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,kBAAkB;IACzBC,KAAK,EAAE,2BAA2B;IAClCC,SAAS,EAAE,CACT,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,IAAI,EACvD,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,MAAM,CAC7D;IACDC,KAAK,EAAE;MACLD,SAAS,EAAE,EAAE;MACbE,QAAQ,EAAE,MAAM;MAChBC,MAAM,EAAE;IACV,CAAC;IACDC,WAAW,EAAE;EACf,CAAC,EACD;IACER,IAAI,EAAE,QAAQ;IACdC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE,oBAAoB;IAC3BC,KAAK,EAAE,6BAA6B;IACpCC,SAAS,EAAE,CACT,OAAO,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAC1D,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,CACrF;IACDC,KAAK,EAAE;MACLD,SAAS,EAAE,EAAE;MACbE,QAAQ,EAAE,MAAM;MAChBC,MAAM,EAAE;IACV,CAAC;IACDC,WAAW,EAAE;EACf,CAAC,EACD;IACER,IAAI,EAAE,MAAM;IACZC,IAAI,EAAE,qBAAqB;IAC3BC,KAAK,EAAE,sBAAsB;IAC7BC,KAAK,EAAE,+BAA+B;IACtCC,SAAS,EAAE,CACT,UAAU,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,aAAa,EAAE,WAAW,EAC1E,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,UAAU,EAAE,OAAO,EAAE,YAAY,CACzG;IACDC,KAAK,EAAE;MACLD,SAAS,EAAE,EAAE;MACbE,QAAQ,EAAE,MAAM;MAChBC,MAAM,EAAE;IACV,CAAC;IACDC,WAAW,EAAE;EACf,CAAC,CACF;EAED,oBACEd,OAAA;IAASe,EAAE,EAAC,SAAS;IAACC,SAAS,EAAC,8DAA8D;IAAAC,QAAA,eAC5FjB,OAAA;MAAKgB,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAE/BjB,OAAA,CAACR,MAAM,CAAC0B,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BR,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAE7BjB,OAAA;UAAIgB,SAAS,EAAC,mDAAmD;UAAAC,QAAA,GAAC,iBAEhE,eAAAjB,OAAA;YAAMgB,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAAU;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eACL5B,OAAA;UAAGgB,SAAS,EAAC,yDAAyD;UAAAC,QAAA,EAAC;QAGvE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGb5B,OAAA,CAACR,MAAM,CAAC0B,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BR,SAAS,EAAC,2CAA2C;QAAAC,QAAA,EAEpDZ,OAAO,CAACwB,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBACzB/B,OAAA,CAACR,MAAM,CAACwC,MAAM;UAEZC,OAAO,EAAEA,CAAA,KAAM7B,eAAe,CAAC2B,KAAK,CAAE;UACtCG,UAAU,EAAE;YAAEC,KAAK,EAAE;UAAK,CAAE;UAC5BC,QAAQ,EAAE;YAAED,KAAK,EAAE;UAAK,CAAE;UAC1BnB,SAAS,EAAE,kEACTb,YAAY,KAAK4B,KAAK,GAClB,oBAAoBD,MAAM,CAACrB,KAAK,uBAAuB,GACvD,gEAAgE,EACnE;UAAAQ,QAAA,EAEFa,MAAM,CAACxB;QAAI,GAVPwB,MAAM,CAACxB,IAAI;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAWH,CAChB;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACQ,CAAC,eAGb5B,OAAA,CAACP,eAAe;QAAC4C,IAAI,EAAC,MAAM;QAAApB,QAAA,eAC1BjB,OAAA,CAACR,MAAM,CAAC0B,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BiB,OAAO,EAAE;YAAElB,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAC9BkB,IAAI,EAAE;YAAEnB,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE,CAAC;UAAG,CAAE;UAC7BE,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAAAP,QAAA,eAE9BjB,OAAA;YAAKgB,SAAS,EAAC,qDAAqD;YAAAC,QAAA,gBAElEjB,OAAA;cAAAiB,QAAA,eACEjB,OAAA,CAACF,IAAI;gBAACkB,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBACnBjB,OAAA;kBAAKgB,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCjB,OAAA;oBAAKgB,SAAS,EAAE,8BAA8BX,OAAO,CAACF,YAAY,CAAC,CAACM,KAAK,mDAAoD;oBAAAQ,QAAA,eAC3HjB,OAAA,CAACN,KAAK;sBAACsB,SAAS,EAAC;oBAAoB;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrC,CAAC,eACN5B,OAAA;oBAAAiB,QAAA,gBACEjB,OAAA;sBAAIgB,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAC7CZ,OAAO,CAACF,YAAY,CAAC,CAACG;oBAAI;sBAAAmB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACzB,CAAC,eACL5B,OAAA;sBAAGgB,SAAS,EAAC,eAAe;sBAAAC,QAAA,EAAC;oBAAmB;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAEN5B,OAAA;kBAAGgB,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,EAC9CZ,OAAO,CAACF,YAAY,CAAC,CAACW;gBAAW;kBAAAW,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eAGJ5B,OAAA;kBAAKgB,SAAS,EAAC,gCAAgC;kBAAAC,QAAA,gBAC7CjB,OAAA;oBAAKgB,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrCjB,OAAA,CAACJ,KAAK;sBAACoB,SAAS,EAAC;oBAA4B;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eAChD5B,OAAA;sBAAMgB,SAAS,EAAC,6BAA6B;sBAAAC,QAAA,EAAC;oBAAa;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC/D,CAAC,eACN5B,OAAA;oBAAGgB,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAEZ,OAAO,CAACF,YAAY,CAAC,CAACI;kBAAI;oBAAAkB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC,eACzE5B,OAAA;oBAAGgB,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,EAAEZ,OAAO,CAACF,YAAY,CAAC,CAACK;kBAAK;oBAAAiB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnE,CAAC,eAGN5B,OAAA;kBAAKgB,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCjB,OAAA;oBAAKgB,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAC1BjB,OAAA;sBAAKgB,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAC9CZ,OAAO,CAACF,YAAY,CAAC,CAACQ,KAAK,CAACD;oBAAS;sBAAAe,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnC,CAAC,eACN5B,OAAA;sBAAKgB,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAS;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACnD,CAAC,eACN5B,OAAA;oBAAKgB,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAC1BjB,OAAA;sBAAKgB,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAC9CZ,OAAO,CAACF,YAAY,CAAC,CAACQ,KAAK,CAACC;oBAAQ;sBAAAa,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClC,CAAC,eACN5B,OAAA;sBAAKgB,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAQ;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC,eACN5B,OAAA;oBAAKgB,SAAS,EAAC,aAAa;oBAAAC,QAAA,gBAC1BjB,OAAA;sBAAKgB,SAAS,EAAC,kCAAkC;sBAAAC,QAAA,EAC9CZ,OAAO,CAACF,YAAY,CAAC,CAACQ,KAAK,CAACE;oBAAM;sBAAAY,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAChC,CAAC,eACN5B,OAAA;sBAAKgB,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAM;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAK,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eAGN5B,OAAA;cAAAiB,QAAA,eACEjB,OAAA,CAACF,IAAI;gBAACkB,SAAS,EAAC,KAAK;gBAAAC,QAAA,gBACnBjB,OAAA;kBAAKgB,SAAS,EAAC,wBAAwB;kBAAAC,QAAA,gBACrCjB,OAAA,CAACL,MAAM;oBAACqB,SAAS,EAAC;kBAA4B;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eACjD5B,OAAA;oBAAIgB,SAAS,EAAC,iCAAiC;oBAAAC,QAAA,EAAC;kBAEhD;oBAAAQ,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAI,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF,CAAC,eAEN5B,OAAA;kBAAKgB,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,EACnDZ,OAAO,CAACF,YAAY,CAAC,CAACO,SAAS,CAACmB,GAAG,CAAC,CAACW,OAAO,EAAET,KAAK,kBAClD/B,OAAA,CAACR,MAAM,CAAC0B,GAAG;oBAETC,OAAO,EAAE;sBAAEC,OAAO,EAAE,CAAC;sBAAEe,KAAK,EAAE;oBAAI,CAAE;oBACpCG,OAAO,EAAE;sBAAElB,OAAO,EAAE,CAAC;sBAAEe,KAAK,EAAE;oBAAE,CAAE;oBAClCZ,UAAU,EAAE;sBAAEkB,KAAK,EAAEV,KAAK,GAAG;oBAAK,CAAE;oBACpCf,SAAS,EAAC,2GAA2G;oBAAAC,QAAA,eAErHjB,OAAA;sBAAMgB,SAAS,EAAC,mCAAmC;sBAAAC,QAAA,EAChDuB;oBAAO;sBAAAf,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACJ;kBAAC,GARFY,OAAO;oBAAAf,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OASF,CACb;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC,eAEN5B,OAAA,CAACR,MAAM,CAAC0B,GAAG;kBACTC,OAAO,EAAE;oBAAEC,OAAO,EAAE;kBAAE,CAAE;kBACxBkB,OAAO,EAAE;oBAAElB,OAAO,EAAE;kBAAE,CAAE;kBACxBG,UAAU,EAAE;oBAAEkB,KAAK,EAAE;kBAAI,CAAE;kBAC3BzB,SAAS,EAAC,oCAAoC;kBAAAC,QAAA,eAE9CjB,OAAA;oBAAQgB,SAAS,EAAC,sGAAsG;oBAAAC,QAAA,gBACtHjB,OAAA;sBAAAiB,QAAA,EAAM;oBAAuB;sBAAAQ,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC,eACpC5B,OAAA,CAACH,UAAU;sBAACmB,SAAS,EAAC;oBAAc;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjC;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACT;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC,GAnGDzB,YAAY;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAoGP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGlB5B,OAAA,CAACR,MAAM,CAAC0B,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEiB,KAAK,EAAE;QAAI,CAAE;QAC1CzB,SAAS,EAAC,OAAO;QAAAC,QAAA,eAEjBjB,OAAA,CAACF,IAAI;UAACkB,SAAS,EAAC,2DAA2D;UAAAC,QAAA,eACzEjB,OAAA;YAAKgB,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BjB,OAAA;cAAIgB,SAAS,EAAC,yBAAyB;cAAAC,QAAA,EAAC;YAExC;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL5B,OAAA;cAAGgB,SAAS,EAAC,sCAAsC;cAAAC,QAAA,EAAC;YAGpD;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAEJ5B,OAAA;cAAKgB,SAAS,EAAC,uCAAuC;cAAAC,QAAA,gBACpDjB,OAAA;gBAAAiB,QAAA,gBACEjB,OAAA;kBAAKgB,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,EAAC;gBAAE;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC/D5B,OAAA;kBAAKgB,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAe;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACN5B,OAAA;gBAAAiB,QAAA,gBACEjB,OAAA;kBAAKgB,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,EAAC;gBAAI;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjE5B,OAAA;kBAAKgB,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAe;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC,eACN5B,OAAA;gBAAAiB,QAAA,gBACEjB,OAAA;kBAAKgB,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,EAAC;gBAAK;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAClE5B,OAAA;kBAAKgB,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAY;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C,CAAC,eACN5B,OAAA;gBAAAiB,QAAA,gBACEjB,OAAA;kBAAKgB,SAAS,EAAC,uCAAuC;kBAAAC,QAAA,EAAC;gBAAI;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACjE5B,OAAA;kBAAKgB,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAAC;gBAAU;kBAAAQ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAAC1B,EAAA,CArPID,eAAyB;AAAAyC,EAAA,GAAzBzC,eAAyB;AAuP/B,eAAeA,eAAe;AAAC,IAAAyC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}