{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/ADFD_Tracking_System/src/components/3D/WorkflowVisualization.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\n\n// Animated Workflow Component\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WorkflowFallback = () => {\n  _s();\n  const [activeStage, setActiveStage] = React.useState(0);\n  React.useEffect(() => {\n    const interval = setInterval(() => {\n      setActiveStage(prev => (prev + 1) % 4);\n    }, 2000);\n    return () => clearInterval(interval);\n  }, []);\n  const stages = [{\n    name: 'Request\\nSubmission',\n    color: 'from-blue-500 to-blue-600',\n    icon: '📝'\n  }, {\n    name: 'Technical\\nReview',\n    color: 'from-yellow-500 to-yellow-600',\n    icon: '🔍'\n  }, {\n    name: 'Approval\\nProcess',\n    color: 'from-green-500 to-green-600',\n    icon: '✅'\n  }, {\n    name: 'Fund\\nDisbursement',\n    color: 'from-purple-500 to-purple-600',\n    icon: '💰'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-full h-96 lg:h-[500px] bg-gradient-to-br from-gray-50 to-blue-50 rounded-2xl flex items-center justify-center p-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full max-w-4xl\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-8\",\n        children: stages.map((stage, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col items-center relative\",\n          children: [index < stages.length - 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-8 left-full w-full h-1 bg-gray-200 z-0\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `h-full bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-1000 ${activeStage > index ? 'w-full' : 'w-0'}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 33,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `relative z-10 w-16 h-16 rounded-full bg-gradient-to-r ${stage.color}\n                flex items-center justify-center text-white text-2xl font-bold shadow-lg\n                transform transition-all duration-500 ${activeStage >= index ? 'scale-110 shadow-xl' : 'scale-100'}`,\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl\",\n              children: stage.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-3 text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: `text-sm font-semibold whitespace-pre-line transition-colors duration-300 ${activeStage >= index ? 'text-gray-900' : 'text-gray-500'}`,\n              children: stage.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 52,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center space-x-2 mb-4\",\n          children: stages.map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `w-2 h-2 rounded-full transition-all duration-300 ${activeStage >= index ? 'bg-blue-500' : 'bg-gray-300'}`\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 64,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 font-medium\",\n          children: \"Real-time Workflow Tracking\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 74,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 24,\n    columnNumber: 5\n  }, this);\n};\n\n// Main Component - Using animated fallback for reliability\n_s(WorkflowFallback, \"QvhuEkVAg+XDAXXTDx9NAA7JQIw=\");\n_c = WorkflowFallback;\nconst WorkflowVisualization = () => {\n  return /*#__PURE__*/_jsxDEV(WorkflowFallback, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 83,\n    columnNumber: 10\n  }, this);\n};\n_c2 = WorkflowVisualization;\nexport default WorkflowVisualization;\nvar _c, _c2;\n$RefreshReg$(_c, \"WorkflowFallback\");\n$RefreshReg$(_c2, \"WorkflowVisualization\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "WorkflowFallback", "_s", "activeStage", "setActiveStage", "useState", "useEffect", "interval", "setInterval", "prev", "clearInterval", "stages", "name", "color", "icon", "className", "children", "map", "stage", "index", "length", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_", "_c", "WorkflowVisualization", "_c2", "$RefreshReg$"], "sources": ["/Users/<USER>/ADFD_Tracking_System/src/components/3D/WorkflowVisualization.tsx"], "sourcesContent": ["import React from 'react';\n\n\n\n// Animated Workflow Component\nconst WorkflowFallback: React.FC = () => {\n  const [activeStage, setActiveStage] = React.useState(0);\n\n  React.useEffect(() => {\n    const interval = setInterval(() => {\n      setActiveStage((prev) => (prev + 1) % 4);\n    }, 2000);\n    return () => clearInterval(interval);\n  }, []);\n\n  const stages = [\n    { name: 'Request\\nSubmission', color: 'from-blue-500 to-blue-600', icon: '📝' },\n    { name: 'Technical\\nReview', color: 'from-yellow-500 to-yellow-600', icon: '🔍' },\n    { name: 'Approval\\nProcess', color: 'from-green-500 to-green-600', icon: '✅' },\n    { name: 'Fund\\nDisbursement', color: 'from-purple-500 to-purple-600', icon: '💰' },\n  ];\n\n  return (\n    <div className=\"w-full h-96 lg:h-[500px] bg-gradient-to-br from-gray-50 to-blue-50 rounded-2xl flex items-center justify-center p-8\">\n      <div className=\"w-full max-w-4xl\">\n        {/* Workflow Steps */}\n        <div className=\"flex items-center justify-between mb-8\">\n          {stages.map((stage, index) => (\n            <div key={index} className=\"flex flex-col items-center relative\">\n              {/* Connection Line */}\n              {index < stages.length - 1 && (\n                <div className=\"absolute top-8 left-full w-full h-1 bg-gray-200 z-0\">\n                  <div\n                    className={`h-full bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-1000 ${\n                      activeStage > index ? 'w-full' : 'w-0'\n                    }`}\n                  />\n                </div>\n              )}\n\n              {/* Stage Circle */}\n              <div className={`relative z-10 w-16 h-16 rounded-full bg-gradient-to-r ${stage.color}\n                flex items-center justify-center text-white text-2xl font-bold shadow-lg\n                transform transition-all duration-500 ${\n                  activeStage >= index ? 'scale-110 shadow-xl' : 'scale-100'\n                }`}>\n                <span className=\"text-2xl\">{stage.icon}</span>\n              </div>\n\n              {/* Stage Label */}\n              <div className=\"mt-3 text-center\">\n                <p className={`text-sm font-semibold whitespace-pre-line transition-colors duration-300 ${\n                  activeStage >= index ? 'text-gray-900' : 'text-gray-500'\n                }`}>\n                  {stage.name}\n                </p>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Progress Indicator */}\n        <div className=\"text-center\">\n          <div className=\"flex justify-center space-x-2 mb-4\">\n            {stages.map((_, index) => (\n              <div\n                key={index}\n                className={`w-2 h-2 rounded-full transition-all duration-300 ${\n                  activeStage >= index ? 'bg-blue-500' : 'bg-gray-300'\n                }`}\n              />\n            ))}\n          </div>\n          <p className=\"text-gray-600 font-medium\">Real-time Workflow Tracking</p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Main Component - Using animated fallback for reliability\nconst WorkflowVisualization: React.FC = () => {\n  return <WorkflowFallback />;\n};\n\nexport default WorkflowVisualization;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;;AAIzB;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGN,KAAK,CAACO,QAAQ,CAAC,CAAC,CAAC;EAEvDP,KAAK,CAACQ,SAAS,CAAC,MAAM;IACpB,MAAMC,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCJ,cAAc,CAAEK,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMC,aAAa,CAACH,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,MAAM,GAAG,CACb;IAAEC,IAAI,EAAE,qBAAqB;IAAEC,KAAK,EAAE,2BAA2B;IAAEC,IAAI,EAAE;EAAK,CAAC,EAC/E;IAAEF,IAAI,EAAE,mBAAmB;IAAEC,KAAK,EAAE,+BAA+B;IAAEC,IAAI,EAAE;EAAK,CAAC,EACjF;IAAEF,IAAI,EAAE,mBAAmB;IAAEC,KAAK,EAAE,6BAA6B;IAAEC,IAAI,EAAE;EAAI,CAAC,EAC9E;IAAEF,IAAI,EAAE,oBAAoB;IAAEC,KAAK,EAAE,+BAA+B;IAAEC,IAAI,EAAE;EAAK,CAAC,CACnF;EAED,oBACEd,OAAA;IAAKe,SAAS,EAAC,qHAAqH;IAAAC,QAAA,eAClIhB,OAAA;MAAKe,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAE/BhB,OAAA;QAAKe,SAAS,EAAC,wCAAwC;QAAAC,QAAA,EACpDL,MAAM,CAACM,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBACvBnB,OAAA;UAAiBe,SAAS,EAAC,qCAAqC;UAAAC,QAAA,GAE7DG,KAAK,GAAGR,MAAM,CAACS,MAAM,GAAG,CAAC,iBACxBpB,OAAA;YAAKe,SAAS,EAAC,qDAAqD;YAAAC,QAAA,eAClEhB,OAAA;cACEe,SAAS,EAAE,oFACTZ,WAAW,GAAGgB,KAAK,GAAG,QAAQ,GAAG,KAAK;YACrC;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,eAGDxB,OAAA;YAAKe,SAAS,EAAE,yDAAyDG,KAAK,CAACL,KAAK;AAClG;AACA,wDACkBV,WAAW,IAAIgB,KAAK,GAAG,qBAAqB,GAAG,WAAW,EACzD;YAAAH,QAAA,eACHhB,OAAA;cAAMe,SAAS,EAAC,UAAU;cAAAC,QAAA,EAAEE,KAAK,CAACJ;YAAI;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eAGNxB,OAAA;YAAKe,SAAS,EAAC,kBAAkB;YAAAC,QAAA,eAC/BhB,OAAA;cAAGe,SAAS,EAAE,4EACZZ,WAAW,IAAIgB,KAAK,GAAG,eAAe,GAAG,eAAe,EACvD;cAAAH,QAAA,EACAE,KAAK,CAACN;YAAI;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA,GA5BEL,KAAK;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA6BV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNxB,OAAA;QAAKe,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAC1BhB,OAAA;UAAKe,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAChDL,MAAM,CAACM,GAAG,CAAC,CAACQ,CAAC,EAAEN,KAAK,kBACnBnB,OAAA;YAEEe,SAAS,EAAE,oDACTZ,WAAW,IAAIgB,KAAK,GAAG,aAAa,GAAG,aAAa;UACnD,GAHEA,KAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIX,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNxB,OAAA;UAAGe,SAAS,EAAC,2BAA2B;UAAAC,QAAA,EAAC;QAA2B;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAtB,EAAA,CA3EMD,gBAA0B;AAAAyB,EAAA,GAA1BzB,gBAA0B;AA4EhC,MAAM0B,qBAA+B,GAAGA,CAAA,KAAM;EAC5C,oBAAO3B,OAAA,CAACC,gBAAgB;IAAAoB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC7B,CAAC;AAACI,GAAA,GAFID,qBAA+B;AAIrC,eAAeA,qBAAqB;AAAC,IAAAD,EAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAH,EAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}