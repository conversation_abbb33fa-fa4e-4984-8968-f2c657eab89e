{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/ADFD_Tracking_System/src/components/Layout/Footer.tsx\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { Shield, Globe } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Footer = () => {\n  const currentYear = new Date().getFullYear();\n  const footerSections = [{\n    title: 'System',\n    links: [{\n      name: 'Features',\n      href: '#features'\n    }, {\n      name: 'Security',\n      href: '#security'\n    }, {\n      name: 'Documentation',\n      href: '#docs'\n    }, {\n      name: 'API Reference',\n      href: '#api'\n    }]\n  }, {\n    title: 'Support',\n    links: [{\n      name: 'Help Center',\n      href: '#help'\n    }, {\n      name: 'Training',\n      href: '#training'\n    }, {\n      name: 'Contact',\n      href: '#contact'\n    }, {\n      name: 'System Status',\n      href: '#status'\n    }]\n  }];\n  return /*#__PURE__*/_jsxDEV(\"footer\", {\n    className: \"bg-gray-900 text-white\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-custom section-padding\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12\",\n        children: [/*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6\n          },\n          className: \"lg:col-span-1\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3 mb-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-gradient-to-r from-primary-600 to-gold-500 rounded-lg flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(Shield, {\n                className: \"w-7 h-7 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 42,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-2xl font-bold\",\n                children: \"FinTrack\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 45,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-300\",\n                children: \"Request System\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-300 mb-6 leading-relaxed\",\n            children: \"Enterprise-grade financial request tracking system with advanced security and intelligent automation capabilities.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-3\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center space-x-3 text-gray-300\",\n              children: [/*#__PURE__*/_jsxDEV(Globe, {\n                className: \"w-4 h-4 text-gold-400\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 54,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-sm\",\n                children: \"Secure \\u2022 Reliable \\u2022 Efficient\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 55,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), footerSections.map((section, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 20\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: index * 0.1\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n            className: \"text-lg font-semibold mb-6 text-white\",\n            children: section.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"space-y-3\",\n            children: section.links.map(link => /*#__PURE__*/_jsxDEV(\"li\", {\n              children: /*#__PURE__*/_jsxDEV(motion.a, {\n                href: link.href,\n                whileHover: {\n                  x: 5\n                },\n                className: \"text-navy-300 hover:text-gold-400 transition-colors duration-300 text-sm\",\n                children: link.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 72,\n                columnNumber: 21\n              }, this)\n            }, link.name, false, {\n              fileName: _jsxFileName,\n              lineNumber: 71,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 69,\n            columnNumber: 15\n          }, this)]\n        }, section.title, true, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 13\n        }, this))]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        whileInView: {\n          opacity: 1\n        },\n        transition: {\n          duration: 0.6,\n          delay: 0.4\n        },\n        className: \"border-t border-navy-700 mt-12 pt-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-navy-400 text-sm\",\n            children: [\"\\xA9 \", currentYear, \" Abu Dhabi Fund for Development. All rights reserved.\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-6\",\n            children: [/*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#privacy\",\n              className: \"text-navy-400 hover:text-gold-400 transition-colors duration-300 text-sm\",\n              children: \"Privacy Policy\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 98,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#terms\",\n              className: \"text-navy-400 hover:text-gold-400 transition-colors duration-300 text-sm\",\n              children: \"Terms of Service\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#security\",\n              className: \"text-navy-400 hover:text-gold-400 transition-colors duration-300 text-sm\",\n              children: \"Security\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 97,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 31,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 30,\n    columnNumber: 5\n  }, this);\n};\n_c = Footer;\nexport default Footer;\nvar _c;\n$RefreshReg$(_c, \"Footer\");", "map": {"version": 3, "names": ["React", "motion", "Shield", "Globe", "jsxDEV", "_jsxDEV", "Footer", "currentYear", "Date", "getFullYear", "footerSections", "title", "links", "name", "href", "className", "children", "div", "initial", "opacity", "y", "whileInView", "transition", "duration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "section", "index", "delay", "link", "a", "whileHover", "x", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/ADFD_Tracking_System/src/components/Layout/Footer.tsx"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { Shield, MapPin, Globe } from 'lucide-react';\n\nconst Footer: React.FC = () => {\n  const currentYear = new Date().getFullYear();\n\n  const footerSections = [\n    {\n      title: 'System',\n      links: [\n        { name: 'Features', href: '#features' },\n        { name: 'Security', href: '#security' },\n        { name: 'Documentation', href: '#docs' },\n        { name: 'API Reference', href: '#api' },\n      ]\n    },\n    {\n      title: 'Support',\n      links: [\n        { name: 'Help Center', href: '#help' },\n        { name: 'Training', href: '#training' },\n        { name: 'Contact', href: '#contact' },\n        { name: 'System Status', href: '#status' },\n      ]\n    }\n  ];\n\n  return (\n    <footer className=\"bg-gray-900 text-white\">\n      <div className=\"container-custom section-padding\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12\">\n          {/* Brand Section */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            whileInView={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n            className=\"lg:col-span-1\"\n          >\n            <div className=\"flex items-center space-x-3 mb-6\">\n              <div className=\"w-12 h-12 bg-gradient-to-r from-primary-600 to-gold-500 rounded-lg flex items-center justify-center\">\n                <Shield className=\"w-7 h-7 text-white\" />\n              </div>\n              <div>\n                <h3 className=\"text-2xl font-bold\">FinTrack</h3>\n                <p className=\"text-gray-300\">Request System</p>\n              </div>\n            </div>\n            <p className=\"text-gray-300 mb-6 leading-relaxed\">\n              Enterprise-grade financial request tracking system with advanced security and intelligent automation capabilities.\n            </p>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center space-x-3 text-gray-300\">\n                <Globe className=\"w-4 h-4 text-gold-400\" />\n                <span className=\"text-sm\">Secure • Reliable • Efficient</span>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Footer Links */}\n          {footerSections.map((section, index) => (\n            <motion.div\n              key={section.title}\n              initial={{ opacity: 0, y: 20 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n            >\n              <h4 className=\"text-lg font-semibold mb-6 text-white\">{section.title}</h4>\n              <ul className=\"space-y-3\">\n                {section.links.map((link) => (\n                  <li key={link.name}>\n                    <motion.a\n                      href={link.href}\n                      whileHover={{ x: 5 }}\n                      className=\"text-navy-300 hover:text-gold-400 transition-colors duration-300 text-sm\"\n                    >\n                      {link.name}\n                    </motion.a>\n                  </li>\n                ))}\n              </ul>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Bottom Section */}\n        <motion.div\n          initial={{ opacity: 0 }}\n          whileInView={{ opacity: 1 }}\n          transition={{ duration: 0.6, delay: 0.4 }}\n          className=\"border-t border-navy-700 mt-12 pt-8\"\n        >\n          <div className=\"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0\">\n            <div className=\"text-navy-400 text-sm\">\n              © {currentYear} Abu Dhabi Fund for Development. All rights reserved.\n            </div>\n            <div className=\"flex items-center space-x-6\">\n              <a href=\"#privacy\" className=\"text-navy-400 hover:text-gold-400 transition-colors duration-300 text-sm\">\n                Privacy Policy\n              </a>\n              <a href=\"#terms\" className=\"text-navy-400 hover:text-gold-400 transition-colors duration-300 text-sm\">\n                Terms of Service\n              </a>\n              <a href=\"#security\" className=\"text-navy-400 hover:text-gold-400 transition-colors duration-300 text-sm\">\n                Security\n              </a>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </footer>\n  );\n};\n\nexport default Footer;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,MAAM,EAAUC,KAAK,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErD,MAAMC,MAAgB,GAAGA,CAAA,KAAM;EAC7B,MAAMC,WAAW,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;EAE5C,MAAMC,cAAc,GAAG,CACrB;IACEC,KAAK,EAAE,QAAQ;IACfC,KAAK,EAAE,CACL;MAAEC,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAY,CAAC,EACvC;MAAED,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAY,CAAC,EACvC;MAAED,IAAI,EAAE,eAAe;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACxC;MAAED,IAAI,EAAE,eAAe;MAAEC,IAAI,EAAE;IAAO,CAAC;EAE3C,CAAC,EACD;IACEH,KAAK,EAAE,SAAS;IAChBC,KAAK,EAAE,CACL;MAAEC,IAAI,EAAE,aAAa;MAAEC,IAAI,EAAE;IAAQ,CAAC,EACtC;MAAED,IAAI,EAAE,UAAU;MAAEC,IAAI,EAAE;IAAY,CAAC,EACvC;MAAED,IAAI,EAAE,SAAS;MAAEC,IAAI,EAAE;IAAW,CAAC,EACrC;MAAED,IAAI,EAAE,eAAe;MAAEC,IAAI,EAAE;IAAU,CAAC;EAE9C,CAAC,CACF;EAED,oBACET,OAAA;IAAQU,SAAS,EAAC,wBAAwB;IAAAC,QAAA,eACxCX,OAAA;MAAKU,SAAS,EAAC,kCAAkC;MAAAC,QAAA,gBAC/CX,OAAA;QAAKU,SAAS,EAAC,gEAAgE;QAAAC,QAAA,gBAE7EX,OAAA,CAACJ,MAAM,CAACgB,GAAG;UACTC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,WAAW,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE;UAC9BR,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAEzBX,OAAA;YAAKU,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/CX,OAAA;cAAKU,SAAS,EAAC,qGAAqG;cAAAC,QAAA,eAClHX,OAAA,CAACH,MAAM;gBAACa,SAAS,EAAC;cAAoB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC,eACNtB,OAAA;cAAAW,QAAA,gBACEX,OAAA;gBAAIU,SAAS,EAAC,oBAAoB;gBAAAC,QAAA,EAAC;cAAQ;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChDtB,OAAA;gBAAGU,SAAS,EAAC,eAAe;gBAAAC,QAAA,EAAC;cAAc;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNtB,OAAA;YAAGU,SAAS,EAAC,oCAAoC;YAAAC,QAAA,EAAC;UAElD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJtB,OAAA;YAAKU,SAAS,EAAC,WAAW;YAAAC,QAAA,eACxBX,OAAA;cAAKU,SAAS,EAAC,2CAA2C;cAAAC,QAAA,gBACxDX,OAAA,CAACF,KAAK;gBAACY,SAAS,EAAC;cAAuB;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAC3CtB,OAAA;gBAAMU,SAAS,EAAC,SAAS;gBAAAC,QAAA,EAAC;cAA6B;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3D;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC,EAGZjB,cAAc,CAACkB,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBACjCzB,OAAA,CAACJ,MAAM,CAACgB,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,WAAW,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEQ,KAAK,EAAED,KAAK,GAAG;UAAI,CAAE;UAAAd,QAAA,gBAElDX,OAAA;YAAIU,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAEa,OAAO,CAAClB;UAAK;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1EtB,OAAA;YAAIU,SAAS,EAAC,WAAW;YAAAC,QAAA,EACtBa,OAAO,CAACjB,KAAK,CAACgB,GAAG,CAAEI,IAAI,iBACtB3B,OAAA;cAAAW,QAAA,eACEX,OAAA,CAACJ,MAAM,CAACgC,CAAC;gBACPnB,IAAI,EAAEkB,IAAI,CAAClB,IAAK;gBAChBoB,UAAU,EAAE;kBAAEC,CAAC,EAAE;gBAAE,CAAE;gBACrBpB,SAAS,EAAC,0EAA0E;gBAAAC,QAAA,EAEnFgB,IAAI,CAACnB;cAAI;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC,GAPJK,IAAI,CAACnB,IAAI;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAQd,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA,GAlBAE,OAAO,CAAClB,KAAK;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAmBR,CACb,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNtB,OAAA,CAACJ,MAAM,CAACgB,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBE,WAAW,EAAE;UAAEF,OAAO,EAAE;QAAE,CAAE;QAC5BG,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEQ,KAAK,EAAE;QAAI,CAAE;QAC1ChB,SAAS,EAAC,qCAAqC;QAAAC,QAAA,eAE/CX,OAAA;UAAKU,SAAS,EAAC,+EAA+E;UAAAC,QAAA,gBAC5FX,OAAA;YAAKU,SAAS,EAAC,uBAAuB;YAAAC,QAAA,GAAC,OACnC,EAACT,WAAW,EAAC,uDACjB;UAAA;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNtB,OAAA;YAAKU,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1CX,OAAA;cAAGS,IAAI,EAAC,UAAU;cAACC,SAAS,EAAC,0EAA0E;cAAAC,QAAA,EAAC;YAExG;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJtB,OAAA;cAAGS,IAAI,EAAC,QAAQ;cAACC,SAAS,EAAC,0EAA0E;cAAAC,QAAA,EAAC;YAEtG;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACJtB,OAAA;cAAGS,IAAI,EAAC,WAAW;cAACC,SAAS,EAAC,0EAA0E;cAAAC,QAAA,EAAC;YAEzG;cAAAQ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACS,EAAA,GA5GI9B,MAAgB;AA8GtB,eAAeA,MAAM;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}