{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/ADFD_Tracking_System/src/components/HeroSection.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HeroSection = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"hero\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-content\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-badge\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            children: \"AI-Powered Financial Operations\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 9,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 8,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"hero-title\",\n          children: \"Transform Fund Disbursement with Intelligent Automation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 12,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"hero-subtitle\",\n          children: \"Enterprise-grade withdrawal request platform powered by AI-driven OCR, regional intelligence, and bank-level security. Trusted by leading financial institutions worldwide.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hero-stats\",\n          style: {\n            marginTop: '4rem'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hero-stat\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hero-stat-number\",\n              \"data-target\": \"99.9\",\n              children: \"99.9%\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hero-stat-label\",\n              children: \"Accuracy Rate\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hero-stat\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hero-stat-number\",\n              \"data-target\": \"85\",\n              children: \"85%\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hero-stat-label\",\n              children: \"Faster Processing\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hero-stat\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hero-stat-number\",\n              \"data-target\": \"24\",\n              children: \"24/7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hero-stat-label\",\n              children: \"Hour Monitoring\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 31,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hero-stat\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hero-stat-number\",\n              \"data-target\": \"100\",\n              children: \"100%\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 34,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"hero-stat-label\",\n              children: \"Uptime SLA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 35,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 33,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = HeroSection;\nexport default HeroSection;\nvar _c;\n$RefreshReg$(_c, \"HeroSection\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "HeroSection", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "marginTop", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/ADFD_Tracking_System/src/components/HeroSection.tsx"], "sourcesContent": ["import React from 'react';\n\nconst HeroSection: React.FC = () => {\n  return (\n    <section className=\"hero\">\n      <div className=\"container\">\n        <div className=\"hero-content\">\n          <div className=\"hero-badge\">\n            <span>AI-Powered Financial Operations</span>\n          </div>\n\n          <h1 className=\"hero-title\">\n            Transform Fund Disbursement with Intelligent Automation\n          </h1>\n\n          <p className=\"hero-subtitle\">\n            Enterprise-grade withdrawal request platform powered by AI-driven OCR, regional intelligence, and bank-level security. Trusted by leading financial institutions worldwide.\n          </p>\n\n          <div className=\"hero-stats\" style={{ marginTop: '4rem' }}>\n            <div className=\"hero-stat\">\n              <span className=\"hero-stat-number\" data-target=\"99.9\">99.9%</span>\n              <span className=\"hero-stat-label\">Accuracy Rate</span>\n            </div>\n            <div className=\"hero-stat\">\n              <span className=\"hero-stat-number\" data-target=\"85\">85%</span>\n              <span className=\"hero-stat-label\">Faster Processing</span>\n            </div>\n            <div className=\"hero-stat\">\n              <span className=\"hero-stat-number\" data-target=\"24\">24/7</span>\n              <span className=\"hero-stat-label\">Hour Monitoring</span>\n            </div>\n            <div className=\"hero-stat\">\n              <span className=\"hero-stat-number\" data-target=\"100\">100%</span>\n              <span className=\"hero-stat-label\">Uptime SLA</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default HeroSection;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAClC,oBACED,OAAA;IAASE,SAAS,EAAC,MAAM;IAAAC,QAAA,eACvBH,OAAA;MAAKE,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBH,OAAA;QAAKE,SAAS,EAAC,cAAc;QAAAC,QAAA,gBAC3BH,OAAA;UAAKE,SAAS,EAAC,YAAY;UAAAC,QAAA,eACzBH,OAAA;YAAAG,QAAA,EAAM;UAA+B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzC,CAAC,eAENP,OAAA;UAAIE,SAAS,EAAC,YAAY;UAAAC,QAAA,EAAC;QAE3B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAELP,OAAA;UAAGE,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAE7B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAEJP,OAAA;UAAKE,SAAS,EAAC,YAAY;UAACM,KAAK,EAAE;YAAEC,SAAS,EAAE;UAAO,CAAE;UAAAN,QAAA,gBACvDH,OAAA;YAAKE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBH,OAAA;cAAME,SAAS,EAAC,kBAAkB;cAAC,eAAY,MAAM;cAAAC,QAAA,EAAC;YAAK;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClEP,OAAA;cAAME,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAa;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACNP,OAAA;YAAKE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBH,OAAA;cAAME,SAAS,EAAC,kBAAkB;cAAC,eAAY,IAAI;cAAAC,QAAA,EAAC;YAAG;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC9DP,OAAA;cAAME,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAiB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACvD,CAAC,eACNP,OAAA;YAAKE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBH,OAAA;cAAME,SAAS,EAAC,kBAAkB;cAAC,eAAY,IAAI;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAC/DP,OAAA;cAAME,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACNP,OAAA;YAAKE,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBH,OAAA;cAAME,SAAS,EAAC,kBAAkB;cAAC,eAAY,KAAK;cAAAC,QAAA,EAAC;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChEP,OAAA;cAAME,SAAS,EAAC,iBAAiB;cAAAC,QAAA,EAAC;YAAU;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACG,EAAA,GAvCIT,WAAqB;AAyC3B,eAAeA,WAAW;AAAC,IAAAS,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}