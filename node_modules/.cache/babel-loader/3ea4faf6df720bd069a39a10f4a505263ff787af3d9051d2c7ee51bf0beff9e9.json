{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/ADFD_Tracking_System/src/components/AnimatedBackground.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AnimatedBackground = () => {\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"animated-bg\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"floating-shapes\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"shape shape-1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"shape shape-2\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"shape shape-3\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_c = AnimatedBackground;\nexport default AnimatedBackground;\nvar _c;\n$RefreshReg$(_c, \"AnimatedBackground\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AnimatedBackground", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/ADFD_Tracking_System/src/components/AnimatedBackground.tsx"], "sourcesContent": ["import React from 'react';\n\nconst AnimatedBackground: React.FC = () => {\n  return (\n    <>\n      <div className=\"animated-bg\"></div>\n      <div className=\"floating-shapes\">\n        <div className=\"shape shape-1\"></div>\n        <div className=\"shape shape-2\"></div>\n        <div className=\"shape shape-3\"></div>\n      </div>\n    </>\n  );\n};\n\nexport default AnimatedBackground;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1B,MAAMC,kBAA4B,GAAGA,CAAA,KAAM;EACzC,oBACEH,OAAA,CAAAE,SAAA;IAAAE,QAAA,gBACEJ,OAAA;MAAKK,SAAS,EAAC;IAAa;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACnCT,OAAA;MAAKK,SAAS,EAAC,iBAAiB;MAAAD,QAAA,gBAC9BJ,OAAA;QAAKK,SAAS,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACrCT,OAAA;QAAKK,SAAS,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACrCT,OAAA;QAAKK,SAAS,EAAC;MAAe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClC,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACC,EAAA,GAXIP,kBAA4B;AAalC,eAAeA,kBAAkB;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}