{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/ADFD_Tracking_System/src/components/3D/WorkflowVisualization.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\nimport React from 'react';\n\n// Workflow Stage Component\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst WorkflowStage = ({\n  position,\n  color,\n  label,\n  active,\n  index\n}) => {\n  _s();\n  const meshRef = useRef(null);\n  const textRef = useRef(null);\n  useFrame(state => {\n    if (meshRef.current) {\n      meshRef.current.rotation.y = state.clock.elapsedTime * 0.5;\n      meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime + index) * 0.1;\n    }\n  });\n  return /*#__PURE__*/_jsxDEV(\"group\", {\n    position: position,\n    children: [/*#__PURE__*/_jsxDEV(Sphere, {\n      ref: meshRef,\n      args: [0.8, 32, 32],\n      children: /*#__PURE__*/_jsxDEV(\"meshStandardMaterial\", {\n        color: color,\n        emissive: active ? color : '#000000',\n        emissiveIntensity: active ? 0.3 : 0,\n        metalness: 0.8,\n        roughness: 0.2\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 25,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this), active && /*#__PURE__*/_jsxDEV(Sphere, {\n      args: [1.2, 32, 32],\n      children: /*#__PURE__*/_jsxDEV(\"meshBasicMaterial\", {\n        color: color,\n        transparent: true,\n        opacity: 0.2\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 36,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      ref: textRef,\n      position: [0, -1.5, 0],\n      fontSize: 0.3,\n      color: \"#1e293b\",\n      anchorX: \"center\",\n      anchorY: \"middle\",\n      font: \"/fonts/inter-bold.woff\",\n      children: label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 22,\n    columnNumber: 5\n  }, this);\n};\n\n// Connection Line Component\n_s(WorkflowStage, \"VWmtHyqzMccLFoZW2FWfqf2z0G8=\", true);\n_c = WorkflowStage;\nconst ConnectionLine = ({\n  start,\n  end,\n  active\n}) => {\n  _s2();\n  const points = useMemo(() => [new THREE.Vector3(...start), new THREE.Vector3(...end)], [start, end]);\n  return /*#__PURE__*/_jsxDEV(Line, {\n    points: points,\n    color: active ? \"#0ea5e9\" : \"#94a3b8\",\n    lineWidth: active ? 4 : 2,\n    transparent: true,\n    opacity: active ? 0.8 : 0.4\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 5\n  }, this);\n};\n\n// Floating Particles Component\n_s2(ConnectionLine, \"G95GpPLhY4BpTJPrFAeCtZA4AeI=\");\n_c2 = ConnectionLine;\nconst FloatingParticles = () => {\n  _s3();\n  const particlesRef = useRef(null);\n  const particles = useMemo(() => {\n    const positions = new Float32Array(100 * 3);\n    for (let i = 0; i < 100; i++) {\n      positions[i * 3] = (Math.random() - 0.5) * 20;\n      positions[i * 3 + 1] = (Math.random() - 0.5) * 20;\n      positions[i * 3 + 2] = (Math.random() - 0.5) * 20;\n    }\n    return positions;\n  }, []);\n  useFrame(state => {\n    if (particlesRef.current) {\n      particlesRef.current.rotation.y = state.clock.elapsedTime * 0.1;\n    }\n  });\n  return /*#__PURE__*/_jsxDEV(\"points\", {\n    ref: particlesRef,\n    children: [/*#__PURE__*/_jsxDEV(\"bufferGeometry\", {\n      children: /*#__PURE__*/_jsxDEV(\"bufferAttribute\", {\n        attach: \"attributes-position\",\n        args: [particles, 3]\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 106,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 105,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"pointsMaterial\", {\n      size: 0.05,\n      color: \"#f59e0b\",\n      transparent: true,\n      opacity: 0.6\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 111,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 104,\n    columnNumber: 5\n  }, this);\n};\n\n// Main 3D Scene Component\n_s3(FloatingParticles, \"6U3X4nuEaYm1tAaVFlZXvVGwQm4=\", true);\n_c3 = FloatingParticles;\nconst WorkflowScene = ({\n  activeStage\n}) => {\n  const stages = [{\n    label: \"Initial\\nReview\",\n    color: \"#f97316\",\n    position: [-6, 0, 0]\n  }, {\n    label: \"Technical\\nReview\",\n    color: \"#eab308\",\n    position: [-2, 0, 0]\n  }, {\n    label: \"Core\\nBanking\",\n    color: \"#22c55e\",\n    position: [2, 0, 0]\n  }, {\n    label: \"Disbursed\",\n    color: \"#06b6d4\",\n    position: [6, 0, 0]\n  }];\n  const connections = [{\n    start: [-6, 0, 0],\n    end: [-2, 0, 0]\n  }, {\n    start: [-2, 0, 0],\n    end: [2, 0, 0]\n  }, {\n    start: [2, 0, 0],\n    end: [6, 0, 0]\n  }];\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"ambientLight\", {\n      intensity: 0.6\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 138,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"pointLight\", {\n      position: [10, 10, 10],\n      intensity: 1\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 139,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"pointLight\", {\n      position: [-10, -10, -10],\n      intensity: 0.5\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 140,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"directionalLight\", {\n      position: [0, 10, 5],\n      intensity: 0.5\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FloatingParticles, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this), stages.map((stage, index) => /*#__PURE__*/_jsxDEV(WorkflowStage, {\n      position: stage.position,\n      color: stage.color,\n      label: stage.label,\n      active: index <= activeStage,\n      index: index\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 147,\n      columnNumber: 9\n    }, this)), connections.map((connection, index) => /*#__PURE__*/_jsxDEV(ConnectionLine, {\n      start: connection.start,\n      end: connection.end,\n      active: index < activeStage\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 9\n    }, this)), /*#__PURE__*/_jsxDEV(OrbitControls, {\n      enableZoom: false,\n      enablePan: false,\n      maxPolarAngle: Math.PI / 2,\n      minPolarAngle: Math.PI / 2,\n      autoRotate: true,\n      autoRotateSpeed: 0.5\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 167,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// Animated Fallback Component\n_c4 = WorkflowScene;\nconst WorkflowFallback = () => {\n  _s4();\n  const [activeStage, setActiveStage] = React.useState(0);\n  React.useEffect(() => {\n    const interval = setInterval(() => {\n      setActiveStage(prev => (prev + 1) % 4);\n    }, 2000);\n    return () => clearInterval(interval);\n  }, []);\n  const stages = [{\n    name: 'Request\\nSubmission',\n    color: 'from-blue-500 to-blue-600',\n    icon: '📝'\n  }, {\n    name: 'Technical\\nReview',\n    color: 'from-yellow-500 to-yellow-600',\n    icon: '🔍'\n  }, {\n    name: 'Approval\\nProcess',\n    color: 'from-green-500 to-green-600',\n    icon: '✅'\n  }, {\n    name: 'Fund\\nDisbursement',\n    color: 'from-purple-500 to-purple-600',\n    icon: '💰'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-full h-96 lg:h-[500px] bg-gradient-to-br from-gray-50 to-blue-50 rounded-2xl flex items-center justify-center p-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full max-w-4xl\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-8\",\n        children: stages.map((stage, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col items-center relative\",\n          children: [index < stages.length - 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-8 left-full w-full h-1 bg-gray-200 z-0\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `h-full bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-1000 ${activeStage > index ? 'w-full' : 'w-0'}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 207,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 206,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `relative z-10 w-16 h-16 rounded-full bg-gradient-to-r ${stage.color}\n                flex items-center justify-center text-white text-2xl font-bold shadow-lg\n                transform transition-all duration-500 ${activeStage >= index ? 'scale-110 shadow-xl' : 'scale-100'}`,\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl\",\n              children: stage.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 221,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 216,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-3 text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: `text-sm font-semibold whitespace-pre-line transition-colors duration-300 ${activeStage >= index ? 'text-gray-900' : 'text-gray-500'}`,\n              children: stage.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 226,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 203,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center space-x-2 mb-4\",\n          children: stages.map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `w-2 h-2 rounded-full transition-all duration-300 ${activeStage >= index ? 'bg-blue-500' : 'bg-gray-300'}`\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 240,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 238,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 font-medium\",\n          children: \"Real-time Workflow Tracking\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 248,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 237,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 198,\n    columnNumber: 5\n  }, this);\n};\n\n// Main Component - Using animated fallback for reliability\n_s4(WorkflowFallback, \"QvhuEkVAg+XDAXXTDx9NAA7JQIw=\");\n_c5 = WorkflowFallback;\nconst WorkflowVisualization = () => {\n  return /*#__PURE__*/_jsxDEV(WorkflowFallback, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 257,\n    columnNumber: 10\n  }, this);\n};\n_c6 = WorkflowVisualization;\nexport default WorkflowVisualization;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"WorkflowStage\");\n$RefreshReg$(_c2, \"ConnectionLine\");\n$RefreshReg$(_c3, \"FloatingParticles\");\n$RefreshReg$(_c4, \"WorkflowScene\");\n$RefreshReg$(_c5, \"WorkflowFallback\");\n$RefreshReg$(_c6, \"WorkflowVisualization\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "WorkflowStage", "position", "color", "label", "active", "index", "_s", "meshRef", "useRef", "textRef", "useFrame", "state", "current", "rotation", "y", "clock", "elapsedTime", "Math", "sin", "children", "Sphere", "ref", "args", "emissive", "emissiveIntensity", "metalness", "roughness", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "transparent", "opacity", "Text", "fontSize", "anchorX", "anchorY", "font", "_c", "ConnectionLine", "start", "end", "_s2", "points", "useMemo", "THREE", "Vector3", "Line", "lineWidth", "_c2", "FloatingParticles", "_s3", "particlesRef", "particles", "positions", "Float32Array", "i", "random", "attach", "size", "_c3", "WorkflowScene", "activeStage", "stages", "connections", "intensity", "map", "stage", "connection", "OrbitControls", "enableZoom", "enablePan", "maxPolarAngle", "PI", "minPolarAngle", "autoRotate", "autoRotateSpeed", "_c4", "WorkflowFallback", "_s4", "setActiveStage", "useState", "useEffect", "interval", "setInterval", "prev", "clearInterval", "name", "icon", "className", "length", "_", "_c5", "WorkflowVisualization", "_c6", "$RefreshReg$"], "sources": ["/Users/<USER>/ADFD_Tracking_System/src/components/3D/WorkflowVisualization.tsx"], "sourcesContent": ["import React from 'react';\n\n// Workflow Stage Component\nconst WorkflowStage: React.FC<{\n  position: [number, number, number];\n  color: string;\n  label: string;\n  active: boolean;\n  index: number;\n}> = ({ position, color, label, active, index }) => {\n  const meshRef = useRef<THREE.Mesh>(null);\n  const textRef = useRef<THREE.Mesh>(null);\n\n  useFrame((state) => {\n    if (meshRef.current) {\n      meshRef.current.rotation.y = state.clock.elapsedTime * 0.5;\n      meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime + index) * 0.1;\n    }\n  });\n\n  return (\n    <group position={position}>\n      {/* Stage Sphere */}\n      <Sphere ref={meshRef} args={[0.8, 32, 32]}>\n        <meshStandardMaterial\n          color={color}\n          emissive={active ? color : '#000000'}\n          emissiveIntensity={active ? 0.3 : 0}\n          metalness={0.8}\n          roughness={0.2}\n        />\n      </Sphere>\n      \n      {/* Glow Effect */}\n      {active && (\n        <Sphere args={[1.2, 32, 32]}>\n          <meshBasicMaterial\n            color={color}\n            transparent\n            opacity={0.2}\n          />\n        </Sphere>\n      )}\n      \n      {/* Stage Label */}\n      <Text\n        ref={textRef}\n        position={[0, -1.5, 0]}\n        fontSize={0.3}\n        color=\"#1e293b\"\n        anchorX=\"center\"\n        anchorY=\"middle\"\n        font=\"/fonts/inter-bold.woff\"\n      >\n        {label}\n      </Text>\n    </group>\n  );\n};\n\n// Connection Line Component\nconst ConnectionLine: React.FC<{\n  start: [number, number, number];\n  end: [number, number, number];\n  active: boolean;\n}> = ({ start, end, active }) => {\n  const points = useMemo(() => [\n    new THREE.Vector3(...start),\n    new THREE.Vector3(...end),\n  ], [start, end]);\n\n  return (\n    <Line\n      points={points}\n      color={active ? \"#0ea5e9\" : \"#94a3b8\"}\n      lineWidth={active ? 4 : 2}\n      transparent\n      opacity={active ? 0.8 : 0.4}\n    />\n  );\n};\n\n// Floating Particles Component\nconst FloatingParticles: React.FC = () => {\n  const particlesRef = useRef<THREE.Points>(null);\n  \n  const particles = useMemo(() => {\n    const positions = new Float32Array(100 * 3);\n    for (let i = 0; i < 100; i++) {\n      positions[i * 3] = (Math.random() - 0.5) * 20;\n      positions[i * 3 + 1] = (Math.random() - 0.5) * 20;\n      positions[i * 3 + 2] = (Math.random() - 0.5) * 20;\n    }\n    return positions;\n  }, []);\n\n  useFrame((state) => {\n    if (particlesRef.current) {\n      particlesRef.current.rotation.y = state.clock.elapsedTime * 0.1;\n    }\n  });\n\n  return (\n    <points ref={particlesRef}>\n      <bufferGeometry>\n        <bufferAttribute\n          attach=\"attributes-position\"\n          args={[particles, 3]}\n        />\n      </bufferGeometry>\n      <pointsMaterial\n        size={0.05}\n        color=\"#f59e0b\"\n        transparent\n        opacity={0.6}\n      />\n    </points>\n  );\n};\n\n// Main 3D Scene Component\nconst WorkflowScene: React.FC<{ activeStage: number }> = ({ activeStage }) => {\n  const stages = [\n    { label: \"Initial\\nReview\", color: \"#f97316\", position: [-6, 0, 0] as [number, number, number] },\n    { label: \"Technical\\nReview\", color: \"#eab308\", position: [-2, 0, 0] as [number, number, number] },\n    { label: \"Core\\nBanking\", color: \"#22c55e\", position: [2, 0, 0] as [number, number, number] },\n    { label: \"Disbursed\", color: \"#06b6d4\", position: [6, 0, 0] as [number, number, number] },\n  ];\n\n  const connections = [\n    { start: [-6, 0, 0] as [number, number, number], end: [-2, 0, 0] as [number, number, number] },\n    { start: [-2, 0, 0] as [number, number, number], end: [2, 0, 0] as [number, number, number] },\n    { start: [2, 0, 0] as [number, number, number], end: [6, 0, 0] as [number, number, number] },\n  ];\n\n  return (\n    <>\n      <ambientLight intensity={0.6} />\n      <pointLight position={[10, 10, 10]} intensity={1} />\n      <pointLight position={[-10, -10, -10]} intensity={0.5} />\n      <directionalLight position={[0, 10, 5]} intensity={0.5} />\n      \n      <FloatingParticles />\n      \n      {/* Workflow Stages */}\n      {stages.map((stage, index) => (\n        <WorkflowStage\n          key={index}\n          position={stage.position}\n          color={stage.color}\n          label={stage.label}\n          active={index <= activeStage}\n          index={index}\n        />\n      ))}\n      \n      {/* Connection Lines */}\n      {connections.map((connection, index) => (\n        <ConnectionLine\n          key={index}\n          start={connection.start}\n          end={connection.end}\n          active={index < activeStage}\n        />\n      ))}\n      \n      <OrbitControls\n        enableZoom={false}\n        enablePan={false}\n        maxPolarAngle={Math.PI / 2}\n        minPolarAngle={Math.PI / 2}\n        autoRotate\n        autoRotateSpeed={0.5}\n      />\n    </>\n  );\n};\n\n// Animated Fallback Component\nconst WorkflowFallback: React.FC = () => {\n  const [activeStage, setActiveStage] = React.useState(0);\n\n  React.useEffect(() => {\n    const interval = setInterval(() => {\n      setActiveStage((prev) => (prev + 1) % 4);\n    }, 2000);\n    return () => clearInterval(interval);\n  }, []);\n\n  const stages = [\n    { name: 'Request\\nSubmission', color: 'from-blue-500 to-blue-600', icon: '📝' },\n    { name: 'Technical\\nReview', color: 'from-yellow-500 to-yellow-600', icon: '🔍' },\n    { name: 'Approval\\nProcess', color: 'from-green-500 to-green-600', icon: '✅' },\n    { name: 'Fund\\nDisbursement', color: 'from-purple-500 to-purple-600', icon: '💰' },\n  ];\n\n  return (\n    <div className=\"w-full h-96 lg:h-[500px] bg-gradient-to-br from-gray-50 to-blue-50 rounded-2xl flex items-center justify-center p-8\">\n      <div className=\"w-full max-w-4xl\">\n        {/* Workflow Steps */}\n        <div className=\"flex items-center justify-between mb-8\">\n          {stages.map((stage, index) => (\n            <div key={index} className=\"flex flex-col items-center relative\">\n              {/* Connection Line */}\n              {index < stages.length - 1 && (\n                <div className=\"absolute top-8 left-full w-full h-1 bg-gray-200 z-0\">\n                  <div\n                    className={`h-full bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-1000 ${\n                      activeStage > index ? 'w-full' : 'w-0'\n                    }`}\n                  />\n                </div>\n              )}\n\n              {/* Stage Circle */}\n              <div className={`relative z-10 w-16 h-16 rounded-full bg-gradient-to-r ${stage.color}\n                flex items-center justify-center text-white text-2xl font-bold shadow-lg\n                transform transition-all duration-500 ${\n                  activeStage >= index ? 'scale-110 shadow-xl' : 'scale-100'\n                }`}>\n                <span className=\"text-2xl\">{stage.icon}</span>\n              </div>\n\n              {/* Stage Label */}\n              <div className=\"mt-3 text-center\">\n                <p className={`text-sm font-semibold whitespace-pre-line transition-colors duration-300 ${\n                  activeStage >= index ? 'text-gray-900' : 'text-gray-500'\n                }`}>\n                  {stage.name}\n                </p>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Progress Indicator */}\n        <div className=\"text-center\">\n          <div className=\"flex justify-center space-x-2 mb-4\">\n            {stages.map((_, index) => (\n              <div\n                key={index}\n                className={`w-2 h-2 rounded-full transition-all duration-300 ${\n                  activeStage >= index ? 'bg-blue-500' : 'bg-gray-300'\n                }`}\n              />\n            ))}\n          </div>\n          <p className=\"text-gray-600 font-medium\">Real-time Workflow Tracking</p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Main Component - Using animated fallback for reliability\nconst WorkflowVisualization: React.FC = () => {\n  return <WorkflowFallback />;\n};\n\nexport default WorkflowVisualization;\n"], "mappings": ";;;;;AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,aAMJ,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,KAAK;EAAEC,KAAK;EAAEC,MAAM;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EAClD,MAAMC,OAAO,GAAGC,MAAM,CAAa,IAAI,CAAC;EACxC,MAAMC,OAAO,GAAGD,MAAM,CAAa,IAAI,CAAC;EAExCE,QAAQ,CAAEC,KAAK,IAAK;IAClB,IAAIJ,OAAO,CAACK,OAAO,EAAE;MACnBL,OAAO,CAACK,OAAO,CAACC,QAAQ,CAACC,CAAC,GAAGH,KAAK,CAACI,KAAK,CAACC,WAAW,GAAG,GAAG;MAC1DT,OAAO,CAACK,OAAO,CAACX,QAAQ,CAACa,CAAC,GAAGb,QAAQ,CAAC,CAAC,CAAC,GAAGgB,IAAI,CAACC,GAAG,CAACP,KAAK,CAACI,KAAK,CAACC,WAAW,GAAGX,KAAK,CAAC,GAAG,GAAG;IAC5F;EACF,CAAC,CAAC;EAEF,oBACER,OAAA;IAAOI,QAAQ,EAAEA,QAAS;IAAAkB,QAAA,gBAExBtB,OAAA,CAACuB,MAAM;MAACC,GAAG,EAAEd,OAAQ;MAACe,IAAI,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAE;MAAAH,QAAA,eACxCtB,OAAA;QACEK,KAAK,EAAEA,KAAM;QACbqB,QAAQ,EAAEnB,MAAM,GAAGF,KAAK,GAAG,SAAU;QACrCsB,iBAAiB,EAAEpB,MAAM,GAAG,GAAG,GAAG,CAAE;QACpCqB,SAAS,EAAE,GAAI;QACfC,SAAS,EAAE;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,EAGR1B,MAAM,iBACLP,OAAA,CAACuB,MAAM;MAACE,IAAI,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAE;MAAAH,QAAA,eAC1BtB,OAAA;QACEK,KAAK,EAAEA,KAAM;QACb6B,WAAW;QACXC,OAAO,EAAE;MAAI;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACT,eAGDjC,OAAA,CAACoC,IAAI;MACHZ,GAAG,EAAEZ,OAAQ;MACbR,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAE;MACvBiC,QAAQ,EAAE,GAAI;MACdhC,KAAK,EAAC,SAAS;MACfiC,OAAO,EAAC,QAAQ;MAChBC,OAAO,EAAC,QAAQ;MAChBC,IAAI,EAAC,wBAAwB;MAAAlB,QAAA,EAE5BhB;IAAK;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEZ,CAAC;;AAED;AAAAxB,EAAA,CAzDMN,aAMJ;AAAAsC,EAAA,GANItC,aAMJ;AAoDF,MAAMuC,cAIJ,GAAGA,CAAC;EAAEC,KAAK;EAAEC,GAAG;EAAErC;AAAO,CAAC,KAAK;EAAAsC,GAAA;EAC/B,MAAMC,MAAM,GAAGC,OAAO,CAAC,MAAM,CAC3B,IAAIC,KAAK,CAACC,OAAO,CAAC,GAAGN,KAAK,CAAC,EAC3B,IAAIK,KAAK,CAACC,OAAO,CAAC,GAAGL,GAAG,CAAC,CAC1B,EAAE,CAACD,KAAK,EAAEC,GAAG,CAAC,CAAC;EAEhB,oBACE5C,OAAA,CAACkD,IAAI;IACHJ,MAAM,EAAEA,MAAO;IACfzC,KAAK,EAAEE,MAAM,GAAG,SAAS,GAAG,SAAU;IACtC4C,SAAS,EAAE5C,MAAM,GAAG,CAAC,GAAG,CAAE;IAC1B2B,WAAW;IACXC,OAAO,EAAE5B,MAAM,GAAG,GAAG,GAAG;EAAI;IAAAuB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC7B,CAAC;AAEN,CAAC;;AAED;AAAAY,GAAA,CArBMH,cAIJ;AAAAU,GAAA,GAJIV,cAIJ;AAkBF,MAAMW,iBAA2B,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACxC,MAAMC,YAAY,GAAG5C,MAAM,CAAe,IAAI,CAAC;EAE/C,MAAM6C,SAAS,GAAGT,OAAO,CAAC,MAAM;IAC9B,MAAMU,SAAS,GAAG,IAAIC,YAAY,CAAC,GAAG,GAAG,CAAC,CAAC;IAC3C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;MAC5BF,SAAS,CAACE,CAAC,GAAG,CAAC,CAAC,GAAG,CAACvC,IAAI,CAACwC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,EAAE;MAC7CH,SAAS,CAACE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAACvC,IAAI,CAACwC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,EAAE;MACjDH,SAAS,CAACE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAACvC,IAAI,CAACwC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,EAAE;IACnD;IACA,OAAOH,SAAS;EAClB,CAAC,EAAE,EAAE,CAAC;EAEN5C,QAAQ,CAAEC,KAAK,IAAK;IAClB,IAAIyC,YAAY,CAACxC,OAAO,EAAE;MACxBwC,YAAY,CAACxC,OAAO,CAACC,QAAQ,CAACC,CAAC,GAAGH,KAAK,CAACI,KAAK,CAACC,WAAW,GAAG,GAAG;IACjE;EACF,CAAC,CAAC;EAEF,oBACEnB,OAAA;IAAQwB,GAAG,EAAE+B,YAAa;IAAAjC,QAAA,gBACxBtB,OAAA;MAAAsB,QAAA,eACEtB,OAAA;QACE6D,MAAM,EAAC,qBAAqB;QAC5BpC,IAAI,EAAE,CAAC+B,SAAS,EAAE,CAAC;MAAE;QAAA1B,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACY,CAAC,eACjBjC,OAAA;MACE8D,IAAI,EAAE,IAAK;MACXzD,KAAK,EAAC,SAAS;MACf6B,WAAW;MACXC,OAAO,EAAE;IAAI;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEb,CAAC;;AAED;AAAAqB,GAAA,CArCMD,iBAA2B;AAAAU,GAAA,GAA3BV,iBAA2B;AAsCjC,MAAMW,aAAgD,GAAGA,CAAC;EAAEC;AAAY,CAAC,KAAK;EAC5E,MAAMC,MAAM,GAAG,CACb;IAAE5D,KAAK,EAAE,iBAAiB;IAAED,KAAK,EAAE,SAAS;IAAED,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;EAA8B,CAAC,EAChG;IAAEE,KAAK,EAAE,mBAAmB;IAAED,KAAK,EAAE,SAAS;IAAED,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;EAA8B,CAAC,EAClG;IAAEE,KAAK,EAAE,eAAe;IAAED,KAAK,EAAE,SAAS;IAAED,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;EAA8B,CAAC,EAC7F;IAAEE,KAAK,EAAE,WAAW;IAAED,KAAK,EAAE,SAAS;IAAED,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;EAA8B,CAAC,CAC1F;EAED,MAAM+D,WAAW,GAAG,CAClB;IAAExB,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAA6B;IAAEC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;EAA8B,CAAC,EAC9F;IAAED,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAA6B;IAAEC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;EAA8B,CAAC,EAC7F;IAAED,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAA6B;IAAEC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;EAA8B,CAAC,CAC7F;EAED,oBACE5C,OAAA,CAAAE,SAAA;IAAAoB,QAAA,gBACEtB,OAAA;MAAcoE,SAAS,EAAE;IAAI;MAAAtC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChCjC,OAAA;MAAYI,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;MAACgE,SAAS,EAAE;IAAE;MAAAtC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpDjC,OAAA;MAAYI,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAE;MAACgE,SAAS,EAAE;IAAI;MAAAtC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACzDjC,OAAA;MAAkBI,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAE;MAACgE,SAAS,EAAE;IAAI;MAAAtC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAE1DjC,OAAA,CAACqD,iBAAiB;MAAAvB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGpBiC,MAAM,CAACG,GAAG,CAAC,CAACC,KAAK,EAAE9D,KAAK,kBACvBR,OAAA,CAACG,aAAa;MAEZC,QAAQ,EAAEkE,KAAK,CAAClE,QAAS;MACzBC,KAAK,EAAEiE,KAAK,CAACjE,KAAM;MACnBC,KAAK,EAAEgE,KAAK,CAAChE,KAAM;MACnBC,MAAM,EAAEC,KAAK,IAAIyD,WAAY;MAC7BzD,KAAK,EAAEA;IAAM,GALRA,KAAK;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAMX,CACF,CAAC,EAGDkC,WAAW,CAACE,GAAG,CAAC,CAACE,UAAU,EAAE/D,KAAK,kBACjCR,OAAA,CAAC0C,cAAc;MAEbC,KAAK,EAAE4B,UAAU,CAAC5B,KAAM;MACxBC,GAAG,EAAE2B,UAAU,CAAC3B,GAAI;MACpBrC,MAAM,EAAEC,KAAK,GAAGyD;IAAY,GAHvBzD,KAAK;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAIX,CACF,CAAC,eAEFjC,OAAA,CAACwE,aAAa;MACZC,UAAU,EAAE,KAAM;MAClBC,SAAS,EAAE,KAAM;MACjBC,aAAa,EAAEvD,IAAI,CAACwD,EAAE,GAAG,CAAE;MAC3BC,aAAa,EAAEzD,IAAI,CAACwD,EAAE,GAAG,CAAE;MAC3BE,UAAU;MACVC,eAAe,EAAE;IAAI;MAAAjD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC;EAAA,eACF,CAAC;AAEP,CAAC;;AAED;AAAA+C,GAAA,GAzDMhB,aAAgD;AA0DtD,MAAMiB,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACvC,MAAM,CAACjB,WAAW,EAAEkB,cAAc,CAAC,GAAGrF,KAAK,CAACsF,QAAQ,CAAC,CAAC,CAAC;EAEvDtF,KAAK,CAACuF,SAAS,CAAC,MAAM;IACpB,MAAMC,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCJ,cAAc,CAAEK,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMC,aAAa,CAACH,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMpB,MAAM,GAAG,CACb;IAAEwB,IAAI,EAAE,qBAAqB;IAAErF,KAAK,EAAE,2BAA2B;IAAEsF,IAAI,EAAE;EAAK,CAAC,EAC/E;IAAED,IAAI,EAAE,mBAAmB;IAAErF,KAAK,EAAE,+BAA+B;IAAEsF,IAAI,EAAE;EAAK,CAAC,EACjF;IAAED,IAAI,EAAE,mBAAmB;IAAErF,KAAK,EAAE,6BAA6B;IAAEsF,IAAI,EAAE;EAAI,CAAC,EAC9E;IAAED,IAAI,EAAE,oBAAoB;IAAErF,KAAK,EAAE,+BAA+B;IAAEsF,IAAI,EAAE;EAAK,CAAC,CACnF;EAED,oBACE3F,OAAA;IAAK4F,SAAS,EAAC,qHAAqH;IAAAtE,QAAA,eAClItB,OAAA;MAAK4F,SAAS,EAAC,kBAAkB;MAAAtE,QAAA,gBAE/BtB,OAAA;QAAK4F,SAAS,EAAC,wCAAwC;QAAAtE,QAAA,EACpD4C,MAAM,CAACG,GAAG,CAAC,CAACC,KAAK,EAAE9D,KAAK,kBACvBR,OAAA;UAAiB4F,SAAS,EAAC,qCAAqC;UAAAtE,QAAA,GAE7Dd,KAAK,GAAG0D,MAAM,CAAC2B,MAAM,GAAG,CAAC,iBACxB7F,OAAA;YAAK4F,SAAS,EAAC,qDAAqD;YAAAtE,QAAA,eAClEtB,OAAA;cACE4F,SAAS,EAAE,oFACT3B,WAAW,GAAGzD,KAAK,GAAG,QAAQ,GAAG,KAAK;YACrC;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,eAGDjC,OAAA;YAAK4F,SAAS,EAAE,yDAAyDtB,KAAK,CAACjE,KAAK;AAClG;AACA,wDACkB4D,WAAW,IAAIzD,KAAK,GAAG,qBAAqB,GAAG,WAAW,EACzD;YAAAc,QAAA,eACHtB,OAAA;cAAM4F,SAAS,EAAC,UAAU;cAAAtE,QAAA,EAAEgD,KAAK,CAACqB;YAAI;cAAA7D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eAGNjC,OAAA;YAAK4F,SAAS,EAAC,kBAAkB;YAAAtE,QAAA,eAC/BtB,OAAA;cAAG4F,SAAS,EAAE,4EACZ3B,WAAW,IAAIzD,KAAK,GAAG,eAAe,GAAG,eAAe,EACvD;cAAAc,QAAA,EACAgD,KAAK,CAACoB;YAAI;cAAA5D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA,GA5BEzB,KAAK;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA6BV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNjC,OAAA;QAAK4F,SAAS,EAAC,aAAa;QAAAtE,QAAA,gBAC1BtB,OAAA;UAAK4F,SAAS,EAAC,oCAAoC;UAAAtE,QAAA,EAChD4C,MAAM,CAACG,GAAG,CAAC,CAACyB,CAAC,EAAEtF,KAAK,kBACnBR,OAAA;YAEE4F,SAAS,EAAE,oDACT3B,WAAW,IAAIzD,KAAK,GAAG,aAAa,GAAG,aAAa;UACnD,GAHEA,KAAK;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIX,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACNjC,OAAA;UAAG4F,SAAS,EAAC,2BAA2B;UAAAtE,QAAA,EAAC;QAA2B;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAAiD,GAAA,CA3EMD,gBAA0B;AAAAc,GAAA,GAA1Bd,gBAA0B;AA4EhC,MAAMe,qBAA+B,GAAGA,CAAA,KAAM;EAC5C,oBAAOhG,OAAA,CAACiF,gBAAgB;IAAAnD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC7B,CAAC;AAACgE,GAAA,GAFID,qBAA+B;AAIrC,eAAeA,qBAAqB;AAAC,IAAAvD,EAAA,EAAAW,GAAA,EAAAW,GAAA,EAAAiB,GAAA,EAAAe,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAAzD,EAAA;AAAAyD,YAAA,CAAA9C,GAAA;AAAA8C,YAAA,CAAAnC,GAAA;AAAAmC,YAAA,CAAAlB,GAAA;AAAAkB,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}