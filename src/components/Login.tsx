import React, { useState } from 'react';

interface LoginProps {
  onLogin: (user: User) => void;
}

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  region?: string;
  permissions: string[];
}

const Login: React.FC<LoginProps> = ({ onLogin }) => {
  const [email, setEmail] = useState('');
  const [selectedRole, setSelectedRole] = useState('');
  const [password, setPassword] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [showResetPassword, setShowResetPassword] = useState(false);
  const [resetEmail, setResetEmail] = useState('');
  const [currentPassword, setCurrentPassword] = useState('');
  const [newPassword, setNewPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showCurrentPassword, setShowCurrentPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);

  // Demo users based on the production architecture plan
  const demoUsers: User[] = [
    {
      id: 'archive_001',
      name: 'Ahmed Al Zaabi',
      email: '<EMAIL>',
      role: 'archive_team',
      permissions: ['create_requests', 'ocr_access']
    },
    {
      id: 'ops_africa',
      name: 'Ahmed Al Kalbani',
      email: '<EMAIL>',
      role: 'operations_team',
      region: 'africa',
      permissions: ['approve_reject', 'view_regional']
    },
    {
      id: 'loan_admin_001',
      name: 'Fatima Al Hammadi',
      email: '<EMAIL>',
      role: 'loan_administrator',
      permissions: ['view_all', 'comment', 'reports']
    },
    {
      id: 'head_ops_001',
      name: 'Adel Al Hosani',
      email: '<EMAIL>',
      role: 'head_of_operations',
      permissions: ['view_all', 'comment', 'analytics']
    },
    {
      id: 'core_banking_001',
      name: 'Ahmed Siddique',
      email: '<EMAIL>',
      role: 'core_banking',
      permissions: ['disburse', 'view_assigned', 'comment']
    }
  ];

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError('');

    try {
      await new Promise(resolve => setTimeout(resolve, 1000));

      const user = demoUsers.find(u => u.email.toLowerCase() === email.toLowerCase() && u.role === selectedRole);
      
      if (!user) {
        setError('Invalid credentials or role mismatch.');
        return;
      }

      if (password.length < 4) {
        setError('Password must be at least 4 characters.');
        return;
      }

      onLogin(user);
      
    } catch (error) {
      setError('Login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResetPassword = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!currentPassword || !newPassword) {
      setError('Please fill in all password fields');
      return;
    }
    
    // Simulate password change
    alert('Password changed successfully!');
    setShowResetPassword(false);
    setResetEmail('');
    setCurrentPassword('');
    setNewPassword('');
    setError('');
  };

  if (showResetPassword) {
    return (
      <div className="min-h-screen flex">
        {/* Left Side - Password Change Form */}
        <div className="w-1/2 bg-white flex items-center justify-center p-8">
          <div className="max-w-sm w-full space-y-6">
            <div className="text-center mb-8">
              <div className="inline-flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-orange-500 rounded">
                  <div className="w-4 h-4 bg-orange-600 rounded m-1"></div>
                </div>
                <span className="text-xl font-semibold text-gray-900">ADFD</span>
              </div>
              <h1 className="text-2xl font-bold text-gray-900">Change Password</h1>
            </div>
            
            <form onSubmit={handleResetPassword} className="space-y-4">
              {/* Email Field */}
              <div>
                <input
                  id="resetEmail"
                  type="email"
                  autoComplete="email"
                  required
                  value={resetEmail}
                  onChange={(e) => setResetEmail(e.target.value)}
                  className="appearance-none relative block w-full px-3 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                  placeholder="Email address"
                />
              </div>

              {/* Current Password */}
              <div className="relative">
                <input
                  id="currentPassword"
                  type={showCurrentPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  required
                  value={currentPassword}
                  onChange={(e) => setCurrentPassword(e.target.value)}
                  className="appearance-none relative block w-full px-3 py-3 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                  placeholder="Enter current password"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                >
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    {showCurrentPassword ? (
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                    ) : (
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    )}
                  </svg>
                </button>
              </div>

              {/* New Password */}
              <div className="relative">
                <input
                  id="newPassword"
                  type={showNewPassword ? 'text' : 'password'}
                  autoComplete="new-password"
                  required
                  value={newPassword}
                  onChange={(e) => setNewPassword(e.target.value)}
                  className="appearance-none relative block w-full px-3 py-3 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                  placeholder="Enter new password"
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 pr-3 flex items-center"
                  onClick={() => setShowNewPassword(!showNewPassword)}
                >
                  <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    {showNewPassword ? (
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                    ) : (
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    )}
                  </svg>
                </button>
              </div>

              {/* Error Message */}
              {error && (
                <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
                  {error}
                </div>
              )}

              {/* Submit Button */}
              <div>
                <button
                  type="submit"
                  className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                >
                  Update Password
                </button>
              </div>
            </form>

            {/* Back to Login */}
            <div className="text-center">
              <button
                type="button"
                onClick={() => {
                  setShowResetPassword(false);
                  setError('');
                }}
                className="text-sm text-indigo-600 hover:text-indigo-500"
              >
                ← Back to Login
              </button>
            </div>
          </div>
        </div>

        {/* Right Side - Visual */}
        <div className="w-1/2 bg-gradient-to-br from-indigo-600 via-purple-600 to-blue-700 flex items-center justify-center relative overflow-hidden">
          <div className="absolute inset-0 bg-gradient-to-br from-black/20 to-transparent"></div>
          <div className="relative text-center text-white p-8">
            <h2 className="text-4xl font-bold mb-4">Secure Access</h2>
            <p className="text-lg opacity-90">Update your credentials safely</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex">
      {/* Left Side - Login Form */}
      <div className="w-1/2 bg-white flex items-center justify-center p-8">
        <div className="max-w-sm w-full space-y-6">
          <div className="text-center mb-8">
            <div className="inline-flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-orange-500 rounded">
                <div className="w-4 h-4 bg-orange-600 rounded m-1"></div>
              </div>
              <span className="text-xl font-semibold text-gray-900">ADFD</span>
            </div>
            <h1 className="text-2xl font-bold text-gray-900">Access ADFD Platform</h1>
          </div>
          
          <form onSubmit={handleLogin} className="space-y-4">
            {/* Email Field */}
            <div>
              <input
                id="email"
                type="email"
                autoComplete="email"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="appearance-none relative block w-full px-3 py-3 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                placeholder="Email address"
              />
            </div>

            {/* Role Selection Dropdown */}
            <div>
              <select
                id="role"
                value={selectedRole}
                onChange={(e) => setSelectedRole(e.target.value)}
                required
                className="appearance-none relative block w-full px-3 py-3 border border-gray-300 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
              >
                <option value="">Select your team</option>
                <option value="archive_team">Archive Team</option>
                <option value="operations_team">Operations Team</option>
                <option value="loan_administrator">Loan Administrator</option>
                <option value="head_of_operations">Head of Operations</option>
                <option value="core_banking">Core Banking Team</option>
              </select>
            </div>

            {/* Password Field */}
            <div className="relative">
              <input
                id="password"
                type={showPassword ? 'text' : 'password'}
                autoComplete="current-password"
                required
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="appearance-none relative block w-full px-3 py-3 pr-10 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
                placeholder="Password"
              />
              <button
                type="button"
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
                onClick={() => setShowPassword(!showPassword)}
              >
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  {showPassword ? (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                  ) : (
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  )}
                </svg>
              </button>
            </div>

            {/* Error Message */}
            {error && (
              <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-md text-sm">
                {error}
              </div>
            )}

            {/* Submit Button */}
            <div>
              <button
                type="submit"
                disabled={isLoading || !selectedRole}
                className="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? 'Signing in...' : 'Sign in'}
              </button>
            </div>
          </form>

          {/* Divider */}
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-300" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-white text-gray-500">Or</span>
            </div>
          </div>

          {/* Demo Quick Access */}
          <div className="space-y-2">
            {demoUsers.slice(0, 3).map((user) => (
              <button
                key={user.id}
                type="button"
                onClick={() => {
                  setEmail(user.email);
                  setSelectedRole(user.role);
                  setPassword('demo1234');
                }}
                className="w-full text-left px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded-md border border-gray-200 hover:border-gray-300 transition-colors"
              >
                <div className="font-medium">{user.name}</div>
                <div className="text-xs text-gray-500">{user.role.replace('_', ' ')}</div>
              </button>
            ))}
          </div>

          {/* Change Password Link */}
          <div className="text-center">
            <button
              type="button"
              onClick={() => setShowResetPassword(true)}
              className="text-sm text-indigo-600 hover:text-indigo-500"
            >
              Change Password
            </button>
          </div>
        </div>
      </div>

      {/* Right Side - Visual */}
      <div className="w-1/2 bg-gradient-to-br from-indigo-600 via-purple-600 to-blue-700 flex items-center justify-center relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-black/20 to-transparent"></div>
        <div className="relative text-center text-white p-8">
          <h2 className="text-5xl font-bold mb-6">Enterprise Fund Management</h2>
          <p className="text-xl mb-8 opacity-90">Streamline withdrawal requests with intelligent automation</p>
          
          {/* Abstract Globe/Network Visualization */}
          <div className="relative mx-auto w-80 h-80 mb-8">
            <div className="absolute inset-0 rounded-full border border-white/20 animate-pulse"></div>
            <div className="absolute inset-4 rounded-full border border-white/30 animate-pulse" style={{animationDelay: '0.5s'}}></div>
            <div className="absolute inset-8 rounded-full border border-white/40 animate-pulse" style={{animationDelay: '1s'}}></div>
            <div className="absolute inset-16 rounded-full bg-white/10 backdrop-blur-sm flex items-center justify-center">
              <svg className="w-24 h-24 text-white/80" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9 3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
              </svg>
            </div>
          </div>
          
          <div className="space-y-2 opacity-80">
            <div className="flex items-center justify-center space-x-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              <span className="text-sm">Secure Banking Integration</span>
            </div>
            <div className="flex items-center justify-center space-x-2">
              <div className="w-2 h-2 bg-blue-400 rounded-full animate-pulse" style={{animationDelay: '0.3s'}}></div>
              <span className="text-sm">Real-time Processing</span>
            </div>
            <div className="flex items-center justify-center space-x-2">
              <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse" style={{animationDelay: '0.6s'}}></div>
              <span className="text-sm">Global Operations</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
