{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/ADFD_Tracking_System/src/components/HeroSection.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HeroSection = () => {\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    className: \"hero\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"fade-in\",\n        children: \"Intelligent Financial Request Tracking\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        className: \"fade-in\",\n        children: \"Streamline your fund disbursement process with AI-powered OCR, regional auto-assignment, and real-time tracking. Built for demanding financial operations with enterprise-grade security.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hero-buttons fade-in\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"#demo\",\n          className: \"btn-primary\",\n          children: \"\\u25B6\\uFE0F Watch Demo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"#features\",\n          className: \"btn-secondary\",\n          children: \"Learn More\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 12,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = HeroSection;\nexport default HeroSection;\nvar _c;\n$RefreshReg$(_c, \"HeroSection\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "HeroSection", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "href", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/ADFD_Tracking_System/src/components/HeroSection.tsx"], "sourcesContent": ["import React from 'react';\n\nconst HeroSection: React.FC = () => {\n  return (\n    <section className=\"hero\">\n      <div className=\"container\">\n        <h1 className=\"fade-in\">Intelligent Financial Request Tracking</h1>\n        <p className=\"fade-in\">\n          Streamline your fund disbursement process with AI-powered OCR, regional auto-assignment, \n          and real-time tracking. Built for demanding financial operations with enterprise-grade security.\n        </p>\n        <div className=\"hero-buttons fade-in\">\n          <a href=\"#demo\" className=\"btn-primary\">\n            ▶️ Watch Demo\n          </a>\n          <a href=\"#features\" className=\"btn-secondary\">Learn More</a>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default HeroSection;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAClC,oBACED,OAAA;IAASE,SAAS,EAAC,MAAM;IAAAC,QAAA,eACvBH,OAAA;MAAKE,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBH,OAAA;QAAIE,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAAsC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACnEP,OAAA;QAAGE,SAAS,EAAC,SAAS;QAAAC,QAAA,EAAC;MAGvB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACJP,OAAA;QAAKE,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBACnCH,OAAA;UAAGQ,IAAI,EAAC,OAAO;UAACN,SAAS,EAAC,aAAa;UAAAC,QAAA,EAAC;QAExC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJP,OAAA;UAAGQ,IAAI,EAAC,WAAW;UAACN,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACE,EAAA,GAlBIR,WAAqB;AAoB3B,eAAeA,WAAW;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}