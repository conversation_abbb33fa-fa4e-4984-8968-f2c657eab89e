# Supabase Configuration
SUPABASE_URL=your_supabase_project_url
SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Frontend Configuration
FRONTEND_URL=http://localhost:3000

# Production Environment Variables for Vercel
# Set these in your Vercel dashboard:
# - SUPABASE_URL
# - SUPABASE_ANON_KEY  
# - SUPABASE_SERVICE_ROLE_KEY
# - FRONTEND_URL (your production domain)

# Development Notes:
# 1. Copy this file to .env.local for local development
# 2. Get your Supabase credentials from your Supabase dashboard
# 3. Never commit .env.local to version control
# 4. For Vercel deployment, set these as environment variables in Vercel dashboard
