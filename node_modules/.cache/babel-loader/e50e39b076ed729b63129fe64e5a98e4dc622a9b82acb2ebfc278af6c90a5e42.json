{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/ADFD_Tracking_System/src/components/RolesSection.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst RolesSection = () => {\n  const roles = [{\n    avatar: '👩‍💼',\n    name: 'Sarah Archive',\n    title: 'Archive Team',\n    permissions: ['Create new withdrawal requests', 'Upload documents with OCR processing', 'Add comments to all requests']\n  }, {\n    avatar: '👨‍💼',\n    name: 'John Administrator',\n    title: 'Loan Administrator',\n    permissions: ['View-only access to all requests', 'Add comments for tracking', 'Generate reports and analytics']\n  }, {\n    avatar: '👨‍🔧',\n    name: 'Operations Teams',\n    title: 'Regional Specialists',\n    permissions: ['Approve/reject regional requests only', 'North Africa, Central Africa, SEA, Central Asia', 'Cannot access other regions']\n  }, {\n    avatar: '👩‍💻',\n    name: 'Lisa Banking',\n    title: 'Core Banking Team',\n    permissions: ['Process final disbursements', 'Mark requests as completed', 'Handle urgent disbursement alerts']\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"roles\",\n    className: \"roles\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"section-header animate-on-scroll\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"User Roles & Permissions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: \"Each role has specific responsibilities and strict access controls\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"roles-grid\",\n        children: roles.map((role, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"role-card animate-on-scroll\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"role-avatar\",\n            children: role.avatar\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h4\", {\n            children: role.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"role-title\",\n            children: role.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n            className: \"role-permissions\",\n            children: role.permissions.map((permission, permIndex) => /*#__PURE__*/_jsxDEV(\"li\", {\n              children: permission\n            }, permIndex, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 60,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 48,\n    columnNumber: 5\n  }, this);\n};\n_c = RolesSection;\nexport default RolesSection;\nvar _c;\n$RefreshReg$(_c, \"RolesSection\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "RolesSection", "roles", "avatar", "name", "title", "permissions", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "role", "index", "permission", "permIndex", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/ADFD_Tracking_System/src/components/RolesSection.tsx"], "sourcesContent": ["import React from 'react';\n\nconst RolesSection: React.FC = () => {\n  const roles = [\n    {\n      avatar: '👩‍💼',\n      name: 'Sarah Archive',\n      title: 'Archive Team',\n      permissions: [\n        'Create new withdrawal requests',\n        'Upload documents with OCR processing',\n        'Add comments to all requests'\n      ]\n    },\n    {\n      avatar: '👨‍💼',\n      name: 'John Administrator',\n      title: 'Loan Administrator',\n      permissions: [\n        'View-only access to all requests',\n        'Add comments for tracking',\n        'Generate reports and analytics'\n      ]\n    },\n    {\n      avatar: '👨‍🔧',\n      name: 'Operations Teams',\n      title: 'Regional Specialists',\n      permissions: [\n        'Approve/reject regional requests only',\n        'North Africa, Central Africa, SEA, Central Asia',\n        'Cannot access other regions'\n      ]\n    },\n    {\n      avatar: '👩‍💻',\n      name: 'Lisa Banking',\n      title: 'Core Banking Team',\n      permissions: [\n        'Process final disbursements',\n        'Mark requests as completed',\n        'Handle urgent disbursement alerts'\n      ]\n    }\n  ];\n\n  return (\n    <section id=\"roles\" className=\"roles\">\n      <div className=\"container\">\n        <div className=\"section-header animate-on-scroll\">\n          <h2>User Roles & Permissions</h2>\n          <p>Each role has specific responsibilities and strict access controls</p>\n        </div>\n        <div className=\"roles-grid\">\n          {roles.map((role, index) => (\n            <div key={index} className=\"role-card animate-on-scroll\">\n              <div className=\"role-avatar\">{role.avatar}</div>\n              <h4>{role.name}</h4>\n              <div className=\"role-title\">{role.title}</div>\n              <ul className=\"role-permissions\">\n                {role.permissions.map((permission, permIndex) => (\n                  <li key={permIndex}>{permission}</li>\n                ))}\n              </ul>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default RolesSection;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EACnC,MAAMC,KAAK,GAAG,CACZ;IACEC,MAAM,EAAE,OAAO;IACfC,IAAI,EAAE,eAAe;IACrBC,KAAK,EAAE,cAAc;IACrBC,WAAW,EAAE,CACX,gCAAgC,EAChC,sCAAsC,EACtC,8BAA8B;EAElC,CAAC,EACD;IACEH,MAAM,EAAE,OAAO;IACfC,IAAI,EAAE,oBAAoB;IAC1BC,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,CACX,kCAAkC,EAClC,2BAA2B,EAC3B,gCAAgC;EAEpC,CAAC,EACD;IACEH,MAAM,EAAE,OAAO;IACfC,IAAI,EAAE,kBAAkB;IACxBC,KAAK,EAAE,sBAAsB;IAC7BC,WAAW,EAAE,CACX,uCAAuC,EACvC,iDAAiD,EACjD,6BAA6B;EAEjC,CAAC,EACD;IACEH,MAAM,EAAE,OAAO;IACfC,IAAI,EAAE,cAAc;IACpBC,KAAK,EAAE,mBAAmB;IAC1BC,WAAW,EAAE,CACX,6BAA6B,EAC7B,4BAA4B,EAC5B,mCAAmC;EAEvC,CAAC,CACF;EAED,oBACEN,OAAA;IAASO,EAAE,EAAC,OAAO;IAACC,SAAS,EAAC,OAAO;IAAAC,QAAA,eACnCT,OAAA;MAAKQ,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBT,OAAA;QAAKQ,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/CT,OAAA;UAAAS,QAAA,EAAI;QAAwB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACjCb,OAAA;UAAAS,QAAA,EAAG;QAAkE;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtE,CAAC,eACNb,OAAA;QAAKQ,SAAS,EAAC,YAAY;QAAAC,QAAA,EACxBP,KAAK,CAACY,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACrBhB,OAAA;UAAiBQ,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBACtDT,OAAA;YAAKQ,SAAS,EAAC,aAAa;YAAAC,QAAA,EAAEM,IAAI,CAACZ;UAAM;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAChDb,OAAA;YAAAS,QAAA,EAAKM,IAAI,CAACX;UAAI;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACpBb,OAAA;YAAKQ,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAEM,IAAI,CAACV;UAAK;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9Cb,OAAA;YAAIQ,SAAS,EAAC,kBAAkB;YAAAC,QAAA,EAC7BM,IAAI,CAACT,WAAW,CAACQ,GAAG,CAAC,CAACG,UAAU,EAAEC,SAAS,kBAC1ClB,OAAA;cAAAS,QAAA,EAAqBQ;YAAU,GAAtBC,SAAS;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAkB,CACrC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC;QAAA,GARGG,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACM,EAAA,GApEIlB,YAAsB;AAsE5B,eAAeA,YAAY;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}