// Vercel serverless function for approving withdrawal requests
import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
)

export default async function handler(req, res) {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', process.env.FRONTEND_URL || 'http://localhost:3000')
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS')
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization')

  if (req.method === 'OPTIONS') {
    return res.status(200).end()
  }

  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  try {
    const authHeader = req.headers.authorization
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ error: 'No valid token provided' })
    }

    const token = authHeader.substring(7)

    // Verify user and get profile
    const { data: { user }, error: authError } = await supabase.auth.getUser(token)
    if (authError || !user) {
      return res.status(401).json({ error: 'Invalid token' })
    }

    const { data: profile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('id', user.id)
      .single()

    if (profileError || !profile) {
      return res.status(403).json({ error: 'User profile not found' })
    }

    const { request_id, comments } = req.body

    if (!request_id) {
      return res.status(400).json({ error: 'Request ID is required' })
    }

    // Get the withdrawal request
    const { data: request, error: requestError } = await supabase
      .from('withdrawal_requests')
      .select('*')
      .eq('id', request_id)
      .single()

    if (requestError || !request) {
      return res.status(404).json({ error: 'Withdrawal request not found' })
    }

    // Validate user permissions based on role and stage
    let canApprove = false
    let nextStage = ''
    let nextStatus = ''

    if (profile.role === 'operations_team' && profile.can_approve_reject) {
      // Operations team can approve in technical_review stage for their region
      if (request.current_stage === 'technical_review' && 
          request.region === profile.region &&
          request.assigned_to === user.id) {
        canApprove = true
        nextStage = 'core_banking'
        nextStatus = 'approved'
      }
    } else if (profile.role === 'core_banking' && profile.can_disburse) {
      // Core banking can process disbursements in core_banking stage
      if (request.current_stage === 'core_banking') {
        canApprove = true
        nextStage = 'disbursed'
        nextStatus = 'completed'
      }
    }

    if (!canApprove) {
      return res.status(403).json({ 
        error: 'Insufficient permissions to approve this request at current stage',
        details: {
          user_role: profile.role,
          request_stage: request.current_stage,
          user_region: profile.region,
          request_region: request.region
        }
      })
    }

    // Auto-assign to Core Banking team if moving to core_banking stage
    let assignedTo = request.assigned_to
    if (nextStage === 'core_banking') {
      // Get first available Core Banking team member
      const { data: coreBankingUsers, error: cbError } = await supabase
        .from('user_profiles')
        .select('id')
        .eq('role', 'core_banking')
        .eq('is_active', true)
        .limit(1)

      if (cbError || !coreBankingUsers || coreBankingUsers.length === 0) {
        return res.status(500).json({ 
          error: 'No Core Banking team members available for assignment' 
        })
      }

      assignedTo = coreBankingUsers[0].id
    }

    // Update the withdrawal request
    const { data: updatedRequest, error: updateError } = await supabase
      .from('withdrawal_requests')
      .update({
        status: nextStatus,
        current_stage: nextStage,
        assigned_to: assignedTo,
        updated_at: new Date().toISOString(),
        ...(nextStage === 'disbursed' && { completed_at: new Date().toISOString() })
      })
      .eq('id', request_id)
      .select()
      .single()

    if (updateError) {
      return res.status(500).json({ 
        error: 'Failed to update withdrawal request',
        details: updateError.message 
      })
    }

    // Log audit trail
    await supabase
      .from('audit_logs')
      .insert({
        request_id: request_id,
        user_id: user.id,
        action_type: nextStage === 'disbursed' ? 'disburse' : 'approve',
        action_details: `Request ${nextStage === 'disbursed' ? 'disbursed' : 'approved'} by ${profile.full_name}${comments ? ` - ${comments}` : ''}`,
        previous_stage: request.current_stage,
        new_stage: nextStage,
        previous_status: request.status,
        new_status: nextStatus,
        amount_involved: request.amount,
        regional_context: request.region,
        metadata: {
          comments: comments,
          processing_time_days: Math.floor((new Date() - new Date(request.created_at)) / (1000 * 60 * 60 * 24))
        }
      })

    // Add comment if provided
    if (comments) {
      await supabase
        .from('request_comments')
        .insert({
          request_id: request_id,
          user_id: user.id,
          comment: comments,
          comment_type: 'approval'
        })
    }

    return res.status(200).json({
      success: true,
      message: `Request successfully ${nextStage === 'disbursed' ? 'disbursed' : 'approved'}`,
      request: {
        id: updatedRequest.id,
        status: updatedRequest.status,
        current_stage: updatedRequest.current_stage,
        assigned_to: updatedRequest.assigned_to,
        updated_at: updatedRequest.updated_at,
        completed_at: updatedRequest.completed_at
      }
    })

  } catch (error) {
    console.error('Approve request error:', error)
    return res.status(500).json({ 
      error: 'Internal server error',
      details: error.message 
    })
  }
}
