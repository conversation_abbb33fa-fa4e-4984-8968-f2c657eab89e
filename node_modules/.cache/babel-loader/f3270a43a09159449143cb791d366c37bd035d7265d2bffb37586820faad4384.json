{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/ADFD_Tracking_System/src/App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport Navigation from './components/Navigation';\nimport HeroSection from './components/HeroSection';\nimport FeaturesSection from './components/FeaturesSection';\nimport Footer from './components/Footer';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  useEffect(() => {\n    // Smooth scrolling for anchor links\n    const handleSmoothScroll = e => {\n      const target = e.target;\n      if (target.hash) {\n        e.preventDefault();\n        const element = document.querySelector(target.hash);\n        if (element) {\n          element.scrollIntoView({\n            behavior: 'smooth',\n            block: 'start'\n          });\n        }\n      }\n    };\n\n    // Add event listeners for smooth scrolling\n    const links = document.querySelectorAll('a[href^=\"#\"]');\n    links.forEach(link => {\n      link.addEventListener('click', handleSmoothScroll);\n    });\n\n    // Scroll-based animations\n    const observerOptions = {\n      threshold: 0.1,\n      rootMargin: '0px 0px -100px 0px'\n    };\n    const observer = new IntersectionObserver(entries => {\n      entries.forEach(entry => {\n        if (entry.isIntersecting) {\n          entry.target.classList.add('visible');\n        }\n      });\n    }, observerOptions);\n    document.querySelectorAll('.animate-on-scroll').forEach(el => {\n      observer.observe(el);\n    });\n\n    // Cleanup\n    return () => {\n      links.forEach(link => {\n        link.removeEventListener('click', handleSmoothScroll);\n      });\n      observer.disconnect();\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"hero-background\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"floating-elements\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"floating-shape\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"floating-shape\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"floating-shape\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"floating-shape\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"floating-shape\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 66,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Navigation, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 69,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      children: [/*#__PURE__*/_jsxDEV(HeroSection, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(FeaturesSection, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 74,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useEffect", "Navigation", "HeroSection", "FeaturesSection", "Footer", "jsxDEV", "_jsxDEV", "App", "_s", "handleSmoothScroll", "e", "target", "hash", "preventDefault", "element", "document", "querySelector", "scrollIntoView", "behavior", "block", "links", "querySelectorAll", "for<PERSON>ach", "link", "addEventListener", "observerOptions", "threshold", "rootMargin", "observer", "IntersectionObserver", "entries", "entry", "isIntersecting", "classList", "add", "el", "observe", "removeEventListener", "disconnect", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/ADFD_Tracking_System/src/App.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport Navigation from './components/Navigation';\nimport HeroSection from './components/HeroSection';\nimport FeaturesSection from './components/FeaturesSection';\nimport Footer from './components/Footer';\n\nfunction App() {\n  useEffect(() => {\n    // Smooth scrolling for anchor links\n    const handleSmoothScroll = (e: Event) => {\n      const target = e.target as HTMLAnchorElement;\n      if (target.hash) {\n        e.preventDefault();\n        const element = document.querySelector(target.hash);\n        if (element) {\n          element.scrollIntoView({\n            behavior: 'smooth',\n            block: 'start',\n          });\n        }\n      }\n    };\n\n    // Add event listeners for smooth scrolling\n    const links = document.querySelectorAll('a[href^=\"#\"]');\n    links.forEach(link => {\n      link.addEventListener('click', handleSmoothScroll);\n    });\n\n    // Scroll-based animations\n    const observerOptions = {\n      threshold: 0.1,\n      rootMargin: '0px 0px -100px 0px'\n    };\n\n    const observer = new IntersectionObserver((entries) => {\n      entries.forEach(entry => {\n        if (entry.isIntersecting) {\n          entry.target.classList.add('visible');\n        }\n      });\n    }, observerOptions);\n\n    document.querySelectorAll('.animate-on-scroll').forEach(el => {\n      observer.observe(el);\n    });\n\n    // Cleanup\n    return () => {\n      links.forEach(link => {\n        link.removeEventListener('click', handleSmoothScroll);\n      });\n      observer.disconnect();\n    };\n  }, []);\n\n  return (\n    <div className=\"App\">\n      {/* Background Elements */}\n      <div className=\"hero-background\"></div>\n      <div className=\"floating-elements\">\n        <div className=\"floating-shape\"></div>\n        <div className=\"floating-shape\"></div>\n        <div className=\"floating-shape\"></div>\n        <div className=\"floating-shape\"></div>\n        <div className=\"floating-shape\"></div>\n      </div>\n\n      <Navigation />\n      <main>\n        <HeroSection />\n        <FeaturesSection />\n      </main>\n      <Footer />\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,WAAW,MAAM,0BAA0B;AAClD,OAAOC,eAAe,MAAM,8BAA8B;AAC1D,OAAOC,MAAM,MAAM,qBAAqB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzC,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACbR,SAAS,CAAC,MAAM;IACd;IACA,MAAMS,kBAAkB,GAAIC,CAAQ,IAAK;MACvC,MAAMC,MAAM,GAAGD,CAAC,CAACC,MAA2B;MAC5C,IAAIA,MAAM,CAACC,IAAI,EAAE;QACfF,CAAC,CAACG,cAAc,CAAC,CAAC;QAClB,MAAMC,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAACL,MAAM,CAACC,IAAI,CAAC;QACnD,IAAIE,OAAO,EAAE;UACXA,OAAO,CAACG,cAAc,CAAC;YACrBC,QAAQ,EAAE,QAAQ;YAClBC,KAAK,EAAE;UACT,CAAC,CAAC;QACJ;MACF;IACF,CAAC;;IAED;IACA,MAAMC,KAAK,GAAGL,QAAQ,CAACM,gBAAgB,CAAC,cAAc,CAAC;IACvDD,KAAK,CAACE,OAAO,CAACC,IAAI,IAAI;MACpBA,IAAI,CAACC,gBAAgB,CAAC,OAAO,EAAEf,kBAAkB,CAAC;IACpD,CAAC,CAAC;;IAEF;IACA,MAAMgB,eAAe,GAAG;MACtBC,SAAS,EAAE,GAAG;MACdC,UAAU,EAAE;IACd,CAAC;IAED,MAAMC,QAAQ,GAAG,IAAIC,oBAAoB,CAAEC,OAAO,IAAK;MACrDA,OAAO,CAACR,OAAO,CAACS,KAAK,IAAI;QACvB,IAAIA,KAAK,CAACC,cAAc,EAAE;UACxBD,KAAK,CAACpB,MAAM,CAACsB,SAAS,CAACC,GAAG,CAAC,SAAS,CAAC;QACvC;MACF,CAAC,CAAC;IACJ,CAAC,EAAET,eAAe,CAAC;IAEnBV,QAAQ,CAACM,gBAAgB,CAAC,oBAAoB,CAAC,CAACC,OAAO,CAACa,EAAE,IAAI;MAC5DP,QAAQ,CAACQ,OAAO,CAACD,EAAE,CAAC;IACtB,CAAC,CAAC;;IAEF;IACA,OAAO,MAAM;MACXf,KAAK,CAACE,OAAO,CAACC,IAAI,IAAI;QACpBA,IAAI,CAACc,mBAAmB,CAAC,OAAO,EAAE5B,kBAAkB,CAAC;MACvD,CAAC,CAAC;MACFmB,QAAQ,CAACU,UAAU,CAAC,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEhC,OAAA;IAAKiC,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAElBlC,OAAA;MAAKiC,SAAS,EAAC;IAAiB;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACvCtC,OAAA;MAAKiC,SAAS,EAAC,mBAAmB;MAAAC,QAAA,gBAChClC,OAAA;QAAKiC,SAAS,EAAC;MAAgB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACtCtC,OAAA;QAAKiC,SAAS,EAAC;MAAgB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACtCtC,OAAA;QAAKiC,SAAS,EAAC;MAAgB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACtCtC,OAAA;QAAKiC,SAAS,EAAC;MAAgB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACtCtC,OAAA;QAAKiC,SAAS,EAAC;MAAgB;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnC,CAAC,eAENtC,OAAA,CAACL,UAAU;MAAAwC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACdtC,OAAA;MAAAkC,QAAA,gBACElC,OAAA,CAACJ,WAAW;QAAAuC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACftC,OAAA,CAACH,eAAe;QAAAsC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACf,CAAC,eACPtC,OAAA,CAACF,MAAM;MAAAqC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACP,CAAC;AAEV;AAACpC,EAAA,CAtEQD,GAAG;AAAAsC,EAAA,GAAHtC,GAAG;AAwEZ,eAAeA,GAAG;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}