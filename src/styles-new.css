@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

:root {
    --primary-gradient: linear-gradient(135deg, #0f172a 0%, #1e293b 25%, #334155 50%, #475569 75%, #64748b 100%);
    --accent-gradient: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 25%, #1e40af 50%, #1e3a8a 75%, #172554 100%);
    --success-gradient: linear-gradient(135deg, #059669 0%, #047857 25%, #065f46 50%, #064e3b 75%, #022c22 100%);
    --warning-gradient: linear-gradient(135deg, #d97706 0%, #b45309 25%, #92400e 50%, #78350f 75%, #451a03 100%);
    --surface-glass: rgba(255, 255, 255, 0.08);
    --surface-glass-hover: rgba(255, 255, 255, 0.12);
    --text-primary: #0f172a;
    --text-secondary: #475569;
    --text-muted: #64748b;
    --border-subtle: rgba(148, 163, 184, 0.2);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    overflow-x: hidden;
    background: #fafafa;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Enhanced Background */
.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 120vh;
    background: var(--primary-gradient);
    z-index: -3;
}

.hero-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.3) 0%, transparent 50%),
        radial-gradient(circle at 40% 60%, rgba(16, 185, 129, 0.2) 0%, transparent 50%);
}

.floating-elements {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: -2;
}

.floating-shape {
    position: absolute;
    opacity: 0.1;
    border-radius: 50%;
    animation: float 20s infinite ease-in-out;
}

.floating-shape:nth-child(1) {
    width: 300px;
    height: 300px;
    background: var(--accent-gradient);
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.floating-shape:nth-child(2) {
    width: 200px;
    height: 200px;
    background: var(--success-gradient);
    top: 60%;
    right: 15%;
    animation-delay: -7s;
}

.floating-shape:nth-child(3) {
    width: 150px;
    height: 150px;
    background: var(--warning-gradient);
    bottom: 30%;
    left: 20%;
    animation-delay: -14s;
}

.floating-shape:nth-child(4) {
    width: 120px;
    height: 120px;
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
    top: 30%;
    right: 30%;
    animation-delay: -3s;
}

.floating-shape:nth-child(5) {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #ef4444, #dc2626);
    bottom: 60%;
    left: 60%;
    animation-delay: -10s;
}

@keyframes float {
    0%, 100% { 
        transform: translate(0, 0) rotate(0deg) scale(1); 
        opacity: 0.1;
    }
    25% { 
        transform: translate(30px, -30px) rotate(90deg) scale(1.1); 
        opacity: 0.15;
    }
    50% { 
        transform: translate(-20px, 20px) rotate(180deg) scale(0.9); 
        opacity: 0.08;
    }
    75% { 
        transform: translate(40px, 10px) rotate(270deg) scale(1.05); 
        opacity: 0.12;
    }
}

/* Enhanced Navigation */
.navigation {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(15, 23, 42, 0.8);
    backdrop-filter: blur(24px);
    -webkit-backdrop-filter: blur(24px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 1000;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.navigation.scrolled {
    background: rgba(15, 23, 42, 0.95);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.nav-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 0;
}

.brand {
    display: flex;
    align-items: center;
    gap: 1rem;
    text-decoration: none;
    color: white;
}

.brand-icon {
    width: 48px;
    height: 48px;
    background: var(--accent-gradient);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
}

.brand-text {
    display: flex;
    flex-direction: column;
}

.brand-name {
    font-size: 1.5rem;
    font-weight: 800;
    letter-spacing: -0.025em;
}

.brand-tagline {
    font-size: 0.75rem;
    opacity: 0.7;
    font-weight: 500;
    letter-spacing: 0.05em;
    text-transform: uppercase;
}

.nav-menu {
    display: flex;
    gap: 3rem;
    list-style: none;
}

.nav-menu a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    position: relative;
    padding: 0.5rem 0;
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--accent-gradient);
    transition: width 0.3s ease;
}

.nav-menu a:hover {
    color: white;
}

.nav-menu a:hover::after {
    width: 100%;
}

.nav-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.btn-nav {
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    cursor: pointer;
}

.btn-nav.secondary {
    color: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.3);
    background: rgba(255, 255, 255, 0.1);
}

.btn-nav.secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-1px);
}

.btn-nav.primary {
    background: var(--accent-gradient);
    color: white;
    box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
}

.btn-nav.primary:hover {
    box-shadow: 0 6px 24px rgba(59, 130, 246, 0.4);
    transform: translateY(-2px);
}

/* Hero Section */
.hero {
    position: relative;
    padding: 12rem 0 8rem;
    color: white;
    text-align: center;
}

.hero-content {
    max-width: 900px;
    margin: 0 auto;
}

.hero-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(12px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 0.75rem 1.5rem;
    border-radius: 50px;
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 3rem;
    animation: fadeInUp 0.8s ease 0.2s both;
}

.hero-badge::before {
    content: '';
    width: 8px;
    height: 8px;
    background: #10b981;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.hero-title {
    font-size: clamp(3rem, 6vw, 5.5rem);
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 2rem;
    background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 50%, #cbd5e1 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: fadeInUp 0.8s ease 0.4s both;
}

.hero-subtitle {
    font-size: 1.5rem;
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 3rem;
    font-weight: 400;
    animation: fadeInUp 0.8s ease 0.6s both;
}

.hero-actions {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 4rem;
    animation: fadeInUp 0.8s ease 0.8s both;
}

.btn-hero {
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1.25rem 2.5rem;
    border-radius: 16px;
    text-decoration: none;
    font-weight: 700;
    font-size: 1.1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.btn-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-hero:hover::before {
    left: 100%;
}

.btn-hero.primary {
    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
    color: #0f172a;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.btn-hero.primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}

.btn-hero.secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-color: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(12px);
}

.btn-hero.secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-3px);
}

.hero-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
    max-width: 900px;
    margin: 0 auto;
    animation: fadeInUp 0.8s ease 1s both;
    padding: 0 1rem;
}

.hero-stat {
    text-align: center;
    padding: 1.5rem 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(12px);
    transition: all 0.3s ease;
}

.hero-stat:hover {
    transform: translateY(-4px);
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.2);
}

.hero-stat-number {
    display: block;
    font-size: 3rem;
    font-weight: 900;
    background: linear-gradient(135deg, #60a5fa 0%, #3b82f6 50%, #2563eb 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 0.5rem;
}

.hero-stat-label {
    color: rgba(255, 255, 255, 0.7);
    font-weight: 600;
    font-size: 0.95rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* Trust Section */
.trust-section {
    background: white;
    padding: 6rem 0;
    border-top: 1px solid var(--border-subtle);
}

.trust-content {
    text-align: center;
    max-width: 800px;
    margin: 0 auto;
}

.trust-label {
    color: var(--text-muted);
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.1em;
    margin-bottom: 3rem;
}

.trust-logos {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 3rem;
    align-items: center;
}

.trust-logo {
    height: 60px;
    background: var(--surface-glass);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    color: var(--text-secondary);
    border: 1px solid var(--border-subtle);
    transition: all 0.3s ease;
}

.trust-logo:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.2);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Scroll Animations */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-on-scroll.visible {
    opacity: 1;
    transform: translateY(0);
}

.delay-1 { transition-delay: 0.1s; }
.delay-2 { transition-delay: 0.2s; }
.delay-3 { transition-delay: 0.3s; }
.delay-4 { transition-delay: 0.4s; }

/* Section Styles */
.section-header {
    text-align: center;
    margin-bottom: 5rem;
}

.section-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: var(--surface-glass);
    border: 1px solid var(--border-subtle);
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 0.85rem;
    font-weight: 600;
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
}

.section-title {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 900;
    line-height: 1.2;
    color: var(--text-primary);
    margin-bottom: 1.5rem;
}

.section-description {
    font-size: 1.25rem;
    line-height: 1.6;
    color: var(--text-secondary);
    max-width: 700px;
    margin: 0 auto;
}

/* Features Section */
.features {
    padding: 8rem 0;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

/* Security Section */
.security {
    padding: 8rem 0;
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    color: white;
    position: relative;
}

.security::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
    pointer-events: none;
}

.security-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 3rem;
    margin-bottom: 6rem;
}

.security-card {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 24px;
    padding: 3rem;
    text-align: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(12px);
    position: relative;
    z-index: 1;
}

.security-card:hover {
    transform: translateY(-8px);
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
}

.security-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin: 0 auto 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.security-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: white;
}

.security-description {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.6;
}



.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 3rem;
}

.feature-card {
    background: white;
    border-radius: 24px;
    padding: 3rem;
    border: 1px solid var(--border-subtle);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--accent-gradient);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.feature-card:hover::before {
    transform: scaleX(1);
}

.feature-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 24px 64px rgba(0, 0, 0, 0.15);
}

.feature-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.1);
}

.feature-title {
    font-size: 1.5rem;
    font-weight: 800;
    color: var(--text-primary);
    margin-bottom: 1rem;
    line-height: 1.3;
}

.feature-description {
    color: var(--text-secondary);
    line-height: 1.7;
    margin-bottom: 1.5rem;
}

.feature-link {
    color: #3b82f6;
    text-decoration: none;
    font-weight: 600;
    font-size: 0.95rem;
    transition: all 0.3s ease;
}

.feature-link:hover {
    color: #1d4ed8;
    transform: translateX(4px);
}

/* Footer */
.footer {
    background: #0f172a;
    color: rgba(255, 255, 255, 0.8);
    padding: 6rem 0 3rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 6rem;
    margin-bottom: 4rem;
}

.footer-brand {
    max-width: 400px;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.footer-logo-icon {
    width: 48px;
    height: 48px;
    background: var(--accent-gradient);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.footer-description {
    line-height: 1.7;
    margin-bottom: 2rem;
}

.footer-social {
    display: flex;
    gap: 1rem;
}

.social-link {
    width: 44px;
    height: 44px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    transform: translateY(-2px);
}

.footer-sections {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 3rem;
}

.footer-section h4 {
    color: white;
    font-weight: 700;
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section li {
    margin-bottom: 0.75rem;
}

.footer-section a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: white;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-copyright {
    color: rgba(255, 255, 255, 0.6);
}

.footer-links {
    display: flex;
    gap: 2rem;
    list-style: none;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 0.9rem;
}

.footer-links a:hover {
    color: white;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .footer-content {
        grid-template-columns: 1fr;
        gap: 4rem;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 0 1.5rem;
    }

    .nav-menu {
        display: none;
    }

    .hero {
        padding: 8rem 0 4rem;
    }

    .hero-title {
        font-size: clamp(2rem, 8vw, 3.5rem);
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .btn-hero {
        width: 100%;
        max-width: 300px;
        justify-content: center;
    }

    .hero-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
        padding: 0 0.5rem;
    }

    .hero-stat {
        padding: 1rem 0.5rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .security-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .security-card {
        padding: 2rem;
    }

    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }
}
