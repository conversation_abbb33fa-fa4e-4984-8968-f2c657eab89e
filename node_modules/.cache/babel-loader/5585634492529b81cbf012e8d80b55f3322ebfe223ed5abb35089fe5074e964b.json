{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/ADFD_Tracking_System/src/components/Sections/FeaturesSection.tsx\";\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport { Shield, Zap, Globe, Users, Eye, FileText, Clock, BarChart3 } from 'lucide-react';\nimport Card from '../UI/Card';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FeaturesSection = () => {\n  const features = [{\n    icon: Shield,\n    title: 'Banking-Grade Security',\n    description: 'Zero permission exceptions with strict role-based access controls and Row Level Security policies.',\n    color: 'from-red-500 to-red-600',\n    details: ['JWT Authentication', 'Role-Based Access', 'Audit Logging', 'Data Encryption']\n  }, {\n    icon: Zap,\n    title: 'AI-Powered OCR',\n    description: 'Intelligent document processing with automatic field extraction and regional assignment.',\n    color: 'from-yellow-500 to-yellow-600',\n    details: ['Document Recognition', 'Auto-Assignment', 'Field Extraction', 'Validation']\n  }, {\n    icon: Globe,\n    title: 'Regional Operations',\n    description: 'Multi-regional workflow management across Europe/Latin America, Africa, and Asia.',\n    color: 'from-blue-500 to-blue-600',\n    details: ['3 Global Regions', 'Local Teams', 'Country Mapping', 'Regional Leads']\n  }, {\n    icon: Users,\n    title: '4-Stage Workflow',\n    description: 'Streamlined process from Initial Review through Technical Review to Core Banking and Disbursement.',\n    color: 'from-green-500 to-green-600',\n    details: ['Initial Review', 'Technical Review', 'Core Banking', 'Disbursed']\n  }, {\n    icon: Eye,\n    title: 'Real-Time Tracking',\n    description: 'Live status updates, urgent action alerts, and comprehensive progress monitoring.',\n    color: 'from-purple-500 to-purple-600',\n    details: ['Live Updates', 'Status Tracking', 'Notifications', 'Progress Monitoring']\n  }, {\n    icon: FileText,\n    title: 'Document Management',\n    description: 'Secure file storage, versioning, and attachment system with banking-grade compliance.',\n    color: 'from-indigo-500 to-indigo-600',\n    details: ['Secure Storage', 'Version Control', 'File Attachments', 'Compliance']\n  }, {\n    icon: Clock,\n    title: 'SLA Management',\n    description: 'Priority-based deadline tracking with automated escalation and overdue alerts.',\n    color: 'from-orange-500 to-orange-600',\n    details: ['Deadline Tracking', 'Priority Levels', 'Auto Escalation', 'SLA Monitoring']\n  }, {\n    icon: BarChart3,\n    title: 'Analytics & Reporting',\n    description: 'Comprehensive reporting dashboard with performance metrics and audit trails.',\n    color: 'from-teal-500 to-teal-600',\n    details: ['Performance Metrics', 'Audit Reports', 'Analytics Dashboard', 'Export Options']\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"features\",\n    className: \"section-padding bg-white\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container-custom\",\n      children: [/*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 30\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8\n        },\n        className: \"text-center mb-16\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-4xl md:text-5xl font-bold text-gray-900 mb-6\",\n          children: [\"Powerful System\", /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"gradient-text block\",\n            children: \"Features\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 76,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed\",\n          children: \"Advanced financial request management system with enterprise-level security, intelligent automation, and comprehensive workflow tracking capabilities.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n        children: features.map((feature, index) => /*#__PURE__*/_jsxDEV(motion.div, {\n          initial: {\n            opacity: 0,\n            y: 30\n          },\n          whileInView: {\n            opacity: 1,\n            y: 0\n          },\n          transition: {\n            duration: 0.6,\n            delay: index * 0.1\n          },\n          children: /*#__PURE__*/_jsxDEV(Card, {\n            className: \"p-6 h-full group cursor-pointer\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-center\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: `w-16 h-16 mx-auto mb-4 bg-gradient-to-r ${feature.color} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`,\n                children: /*#__PURE__*/_jsxDEV(feature.icon, {\n                  className: \"w-8 h-8 text-white\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 99,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-xl font-bold text-navy-900 mb-3\",\n                children: feature.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-navy-600 mb-4 leading-relaxed\",\n                children: feature.description\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"space-y-2\",\n                children: feature.details.map((detail, detailIndex) => /*#__PURE__*/_jsxDEV(motion.div, {\n                  initial: {\n                    opacity: 0,\n                    x: -10\n                  },\n                  whileInView: {\n                    opacity: 1,\n                    x: 0\n                  },\n                  transition: {\n                    delay: index * 0.1 + detailIndex * 0.05\n                  },\n                  className: \"flex items-center justify-center text-sm text-navy-500\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-1.5 h-1.5 bg-primary-500 rounded-full mr-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 122,\n                    columnNumber: 25\n                  }, this), detail]\n                }, detail, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 115,\n                  columnNumber: 23\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 96,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 95,\n            columnNumber: 15\n          }, this)\n        }, feature.title, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0,\n          y: 30\n        },\n        whileInView: {\n          opacity: 1,\n          y: 0\n        },\n        transition: {\n          duration: 0.8,\n          delay: 0.4\n        },\n        className: \"text-center mt-16\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-gradient-to-r from-primary-50 to-gold-50 rounded-2xl p-8\",\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"text-2xl font-bold text-navy-900 mb-4\",\n            children: \"Ready to Experience Enterprise-Grade Efficiency?\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-navy-600 mb-6 max-w-2xl mx-auto\",\n            children: \"Join the Abu Dhabi Fund for Development team in revolutionizing withdrawal request management with cutting-edge technology and uncompromising security.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 144,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(motion.button, {\n            whileHover: {\n              scale: 1.05\n            },\n            whileTap: {\n              scale: 0.95\n            },\n            className: \"btn-primary text-lg\",\n            children: \"Request System Access\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 134,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 5\n  }, this);\n};\n_c = FeaturesSection;\nexport default FeaturesSection;\nvar _c;\n$RefreshReg$(_c, \"FeaturesSection\");", "map": {"version": 3, "names": ["React", "motion", "Shield", "Zap", "Globe", "Users", "Eye", "FileText", "Clock", "BarChart3", "Card", "jsxDEV", "_jsxDEV", "FeaturesSection", "features", "icon", "title", "description", "color", "details", "id", "className", "children", "div", "initial", "opacity", "y", "whileInView", "transition", "duration", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "feature", "index", "delay", "detail", "detailIndex", "x", "button", "whileHover", "scale", "whileTap", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/ADFD_Tracking_System/src/components/Sections/FeaturesSection.tsx"], "sourcesContent": ["import React from 'react';\nimport { motion } from 'framer-motion';\nimport { Shield, Zap, Globe, Users, Eye, FileText, Clock, BarChart3 } from 'lucide-react';\nimport Card from '../UI/Card';\n\nconst FeaturesSection: React.FC = () => {\n  const features = [\n    {\n      icon: Shield,\n      title: 'Banking-Grade Security',\n      description: 'Zero permission exceptions with strict role-based access controls and Row Level Security policies.',\n      color: 'from-red-500 to-red-600',\n      details: ['JWT Authentication', 'Role-Based Access', 'Audit Logging', 'Data Encryption']\n    },\n    {\n      icon: Zap,\n      title: 'AI-Powered OCR',\n      description: 'Intelligent document processing with automatic field extraction and regional assignment.',\n      color: 'from-yellow-500 to-yellow-600',\n      details: ['Document Recognition', 'Auto-Assignment', 'Field Extraction', 'Validation']\n    },\n    {\n      icon: Globe,\n      title: 'Regional Operations',\n      description: 'Multi-regional workflow management across Europe/Latin America, Africa, and Asia.',\n      color: 'from-blue-500 to-blue-600',\n      details: ['3 Global Regions', 'Local Teams', 'Country Mapping', 'Regional Leads']\n    },\n    {\n      icon: Users,\n      title: '4-Stage Workflow',\n      description: 'Streamlined process from Initial Review through Technical Review to Core Banking and Disbursement.',\n      color: 'from-green-500 to-green-600',\n      details: ['Initial Review', 'Technical Review', 'Core Banking', 'Disbursed']\n    },\n    {\n      icon: Eye,\n      title: 'Real-Time Tracking',\n      description: 'Live status updates, urgent action alerts, and comprehensive progress monitoring.',\n      color: 'from-purple-500 to-purple-600',\n      details: ['Live Updates', 'Status Tracking', 'Notifications', 'Progress Monitoring']\n    },\n    {\n      icon: FileText,\n      title: 'Document Management',\n      description: 'Secure file storage, versioning, and attachment system with banking-grade compliance.',\n      color: 'from-indigo-500 to-indigo-600',\n      details: ['Secure Storage', 'Version Control', 'File Attachments', 'Compliance']\n    },\n    {\n      icon: Clock,\n      title: 'SLA Management',\n      description: 'Priority-based deadline tracking with automated escalation and overdue alerts.',\n      color: 'from-orange-500 to-orange-600',\n      details: ['Deadline Tracking', 'Priority Levels', 'Auto Escalation', 'SLA Monitoring']\n    },\n    {\n      icon: BarChart3,\n      title: 'Analytics & Reporting',\n      description: 'Comprehensive reporting dashboard with performance metrics and audit trails.',\n      color: 'from-teal-500 to-teal-600',\n      details: ['Performance Metrics', 'Audit Reports', 'Analytics Dashboard', 'Export Options']\n    }\n  ];\n\n  return (\n    <section id=\"features\" className=\"section-padding bg-white\">\n      <div className=\"container-custom\">\n        {/* Section Header */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8 }}\n          className=\"text-center mb-16\"\n        >\n          <h2 className=\"text-4xl md:text-5xl font-bold text-gray-900 mb-6\">\n            Powerful System\n            <span className=\"gradient-text block\">Features</span>\n          </h2>\n          <p className=\"text-xl text-gray-700 max-w-3xl mx-auto leading-relaxed\">\n            Advanced financial request management system with enterprise-level security,\n            intelligent automation, and comprehensive workflow tracking capabilities.\n          </p>\n        </motion.div>\n\n        {/* Features Grid */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {features.map((feature, index) => (\n            <motion.div\n              key={feature.title}\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              transition={{ duration: 0.6, delay: index * 0.1 }}\n            >\n              <Card className=\"p-6 h-full group cursor-pointer\">\n                <div className=\"text-center\">\n                  {/* Icon */}\n                  <div className={`w-16 h-16 mx-auto mb-4 bg-gradient-to-r ${feature.color} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>\n                    <feature.icon className=\"w-8 h-8 text-white\" />\n                  </div>\n\n                  {/* Title */}\n                  <h3 className=\"text-xl font-bold text-navy-900 mb-3\">\n                    {feature.title}\n                  </h3>\n\n                  {/* Description */}\n                  <p className=\"text-navy-600 mb-4 leading-relaxed\">\n                    {feature.description}\n                  </p>\n\n                  {/* Details */}\n                  <div className=\"space-y-2\">\n                    {feature.details.map((detail, detailIndex) => (\n                      <motion.div\n                        key={detail}\n                        initial={{ opacity: 0, x: -10 }}\n                        whileInView={{ opacity: 1, x: 0 }}\n                        transition={{ delay: index * 0.1 + detailIndex * 0.05 }}\n                        className=\"flex items-center justify-center text-sm text-navy-500\"\n                      >\n                        <div className=\"w-1.5 h-1.5 bg-primary-500 rounded-full mr-2\"></div>\n                        {detail}\n                      </motion.div>\n                    ))}\n                  </div>\n                </div>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n\n        {/* Bottom CTA */}\n        <motion.div\n          initial={{ opacity: 0, y: 30 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.8, delay: 0.4 }}\n          className=\"text-center mt-16\"\n        >\n          <div className=\"bg-gradient-to-r from-primary-50 to-gold-50 rounded-2xl p-8\">\n            <h3 className=\"text-2xl font-bold text-navy-900 mb-4\">\n              Ready to Experience Enterprise-Grade Efficiency?\n            </h3>\n            <p className=\"text-navy-600 mb-6 max-w-2xl mx-auto\">\n              Join the Abu Dhabi Fund for Development team in revolutionizing withdrawal request management \n              with cutting-edge technology and uncompromising security.\n            </p>\n            <motion.button\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n              className=\"btn-primary text-lg\"\n            >\n              Request System Access\n            </motion.button>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport default FeaturesSection;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,eAAe;AACtC,SAASC,MAAM,EAAEC,GAAG,EAAEC,KAAK,EAAEC,KAAK,EAAEC,GAAG,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,SAAS,QAAQ,cAAc;AACzF,OAAOC,IAAI,MAAM,YAAY;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE9B,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EACtC,MAAMC,QAAQ,GAAG,CACf;IACEC,IAAI,EAAEb,MAAM;IACZc,KAAK,EAAE,wBAAwB;IAC/BC,WAAW,EAAE,oGAAoG;IACjHC,KAAK,EAAE,yBAAyB;IAChCC,OAAO,EAAE,CAAC,oBAAoB,EAAE,mBAAmB,EAAE,eAAe,EAAE,iBAAiB;EACzF,CAAC,EACD;IACEJ,IAAI,EAAEZ,GAAG;IACTa,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE,0FAA0F;IACvGC,KAAK,EAAE,+BAA+B;IACtCC,OAAO,EAAE,CAAC,sBAAsB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,YAAY;EACvF,CAAC,EACD;IACEJ,IAAI,EAAEX,KAAK;IACXY,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAE,mFAAmF;IAChGC,KAAK,EAAE,2BAA2B;IAClCC,OAAO,EAAE,CAAC,kBAAkB,EAAE,aAAa,EAAE,iBAAiB,EAAE,gBAAgB;EAClF,CAAC,EACD;IACEJ,IAAI,EAAEV,KAAK;IACXW,KAAK,EAAE,kBAAkB;IACzBC,WAAW,EAAE,oGAAoG;IACjHC,KAAK,EAAE,6BAA6B;IACpCC,OAAO,EAAE,CAAC,gBAAgB,EAAE,kBAAkB,EAAE,cAAc,EAAE,WAAW;EAC7E,CAAC,EACD;IACEJ,IAAI,EAAET,GAAG;IACTU,KAAK,EAAE,oBAAoB;IAC3BC,WAAW,EAAE,mFAAmF;IAChGC,KAAK,EAAE,+BAA+B;IACtCC,OAAO,EAAE,CAAC,cAAc,EAAE,iBAAiB,EAAE,eAAe,EAAE,qBAAqB;EACrF,CAAC,EACD;IACEJ,IAAI,EAAER,QAAQ;IACdS,KAAK,EAAE,qBAAqB;IAC5BC,WAAW,EAAE,uFAAuF;IACpGC,KAAK,EAAE,+BAA+B;IACtCC,OAAO,EAAE,CAAC,gBAAgB,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,YAAY;EACjF,CAAC,EACD;IACEJ,IAAI,EAAEP,KAAK;IACXQ,KAAK,EAAE,gBAAgB;IACvBC,WAAW,EAAE,gFAAgF;IAC7FC,KAAK,EAAE,+BAA+B;IACtCC,OAAO,EAAE,CAAC,mBAAmB,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,gBAAgB;EACvF,CAAC,EACD;IACEJ,IAAI,EAAEN,SAAS;IACfO,KAAK,EAAE,uBAAuB;IAC9BC,WAAW,EAAE,8EAA8E;IAC3FC,KAAK,EAAE,2BAA2B;IAClCC,OAAO,EAAE,CAAC,qBAAqB,EAAE,eAAe,EAAE,qBAAqB,EAAE,gBAAgB;EAC3F,CAAC,CACF;EAED,oBACEP,OAAA;IAASQ,EAAE,EAAC,UAAU;IAACC,SAAS,EAAC,0BAA0B;IAAAC,QAAA,eACzDV,OAAA;MAAKS,SAAS,EAAC,kBAAkB;MAAAC,QAAA,gBAE/BV,OAAA,CAACX,MAAM,CAACsB,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAC9BR,SAAS,EAAC,mBAAmB;QAAAC,QAAA,gBAE7BV,OAAA;UAAIS,SAAS,EAAC,mDAAmD;UAAAC,QAAA,GAAC,iBAEhE,eAAAV,OAAA;YAAMS,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAAQ;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnD,CAAC,eACLrB,OAAA;UAAGS,SAAS,EAAC,yDAAyD;UAAAC,QAAA,EAAC;QAGvE;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACM,CAAC,eAGbrB,OAAA;QAAKS,SAAS,EAAC,sDAAsD;QAAAC,QAAA,EAClER,QAAQ,CAACoB,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3BxB,OAAA,CAACX,MAAM,CAACsB,GAAG;UAETC,OAAO,EAAE;YAAEC,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAG,CAAE;UAC/BC,WAAW,EAAE;YAAEF,OAAO,EAAE,CAAC;YAAEC,CAAC,EAAE;UAAE,CAAE;UAClCE,UAAU,EAAE;YAAEC,QAAQ,EAAE,GAAG;YAAEQ,KAAK,EAAED,KAAK,GAAG;UAAI,CAAE;UAAAd,QAAA,eAElDV,OAAA,CAACF,IAAI;YAACW,SAAS,EAAC,iCAAiC;YAAAC,QAAA,eAC/CV,OAAA;cAAKS,SAAS,EAAC,aAAa;cAAAC,QAAA,gBAE1BV,OAAA;gBAAKS,SAAS,EAAE,2CAA2Cc,OAAO,CAACjB,KAAK,sGAAuG;gBAAAI,QAAA,eAC7KV,OAAA,CAACuB,OAAO,CAACpB,IAAI;kBAACM,SAAS,EAAC;gBAAoB;kBAAAS,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C,CAAC,eAGNrB,OAAA;gBAAIS,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EACjDa,OAAO,CAACnB;cAAK;gBAAAc,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eAGLrB,OAAA;gBAAGS,SAAS,EAAC,oCAAoC;gBAAAC,QAAA,EAC9Ca,OAAO,CAAClB;cAAW;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnB,CAAC,eAGJrB,OAAA;gBAAKS,SAAS,EAAC,WAAW;gBAAAC,QAAA,EACvBa,OAAO,CAAChB,OAAO,CAACe,GAAG,CAAC,CAACI,MAAM,EAAEC,WAAW,kBACvC3B,OAAA,CAACX,MAAM,CAACsB,GAAG;kBAETC,OAAO,EAAE;oBAAEC,OAAO,EAAE,CAAC;oBAAEe,CAAC,EAAE,CAAC;kBAAG,CAAE;kBAChCb,WAAW,EAAE;oBAAEF,OAAO,EAAE,CAAC;oBAAEe,CAAC,EAAE;kBAAE,CAAE;kBAClCZ,UAAU,EAAE;oBAAES,KAAK,EAAED,KAAK,GAAG,GAAG,GAAGG,WAAW,GAAG;kBAAK,CAAE;kBACxDlB,SAAS,EAAC,wDAAwD;kBAAAC,QAAA,gBAElEV,OAAA;oBAAKS,SAAS,EAAC;kBAA8C;oBAAAS,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,EACnEK,MAAM;gBAAA,GAPFA,MAAM;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAQD,CACb;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF;QAAC,GAtCFE,OAAO,CAACnB,KAAK;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAuCR,CACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNrB,OAAA,CAACX,MAAM,CAACsB,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAG,CAAE;QAC/BC,WAAW,EAAE;UAAEF,OAAO,EAAE,CAAC;UAAEC,CAAC,EAAE;QAAE,CAAE;QAClCE,UAAU,EAAE;UAAEC,QAAQ,EAAE,GAAG;UAAEQ,KAAK,EAAE;QAAI,CAAE;QAC1ChB,SAAS,EAAC,mBAAmB;QAAAC,QAAA,eAE7BV,OAAA;UAAKS,SAAS,EAAC,6DAA6D;UAAAC,QAAA,gBAC1EV,OAAA;YAAIS,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAEtD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACLrB,OAAA;YAAGS,SAAS,EAAC,sCAAsC;YAAAC,QAAA,EAAC;UAGpD;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACJrB,OAAA,CAACX,MAAM,CAACwC,MAAM;YACZC,UAAU,EAAE;cAAEC,KAAK,EAAE;YAAK,CAAE;YAC5BC,QAAQ,EAAE;cAAED,KAAK,EAAE;YAAK,CAAE;YAC1BtB,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAChC;UAED;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAe,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACY,EAAA,GA1JIhC,eAAyB;AA4J/B,eAAeA,eAAe;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}