{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/ADFD_Tracking_System/src/components/FeaturesSection.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FeaturesSection = () => {\n  const features = [{\n    icon: '📄',\n    title: 'AI-Powered OCR Intelligence',\n    description: 'Advanced neural networks achieve 99.9% accuracy in Arabic and English document processing.',\n    gradient: 'var(--accent-gradient)'\n  }, {\n    icon: '📍',\n    title: 'Intelligent Regional Routing',\n    description: 'Smart geographic assignment routes requests to specialized regional teams automatically.',\n    gradient: 'var(--success-gradient)'\n  }, {\n    icon: '🛡️',\n    title: 'Zero-Exception Security Model',\n    description: 'Military-grade role-based access controls with immutable permissions and zero privilege escalation.',\n    gradient: 'var(--warning-gradient)'\n  }, {\n    icon: '📊',\n    title: 'Real-Time Process Intelligence',\n    description: 'Live tracking with predictive analytics and automated bottleneck detection.',\n    gradient: 'linear-gradient(135deg, #8b5cf6, #7c3aed)'\n  }, {\n    icon: '⚡',\n    title: 'Intelligent Alert System',\n    description: 'ML-powered priority detection with automated escalation and smart notifications.',\n    gradient: 'linear-gradient(135deg, #ef4444, #dc2626)'\n  }, {\n    icon: '🔍',\n    title: 'Advanced Analytics Engine',\n    description: 'Multi-dimensional search, predictive insights, and custom dashboards for data-driven decisions.',\n    gradient: 'linear-gradient(135deg, #06b6d4, #0891b2)'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"section\", {\n    id: \"features\",\n    className: \"features\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"section-header animate-on-scroll\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"section-badge\",\n          children: \"\\u26A1 Powerful Features\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 47,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"section-title\",\n          children: \"Built for Enterprise Financial Operations\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"section-description\",\n          children: \"Advanced AI capabilities, intelligent automation, and enterprise-grade security designed for the most demanding financial institutions.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"features-grid\",\n        children: features.map((feature, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: `feature-card animate-on-scroll delay-${index % 4 + 1}`,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-icon\",\n            style: {\n              background: feature.gradient\n            },\n            children: feature.icon\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n            className: \"feature-title\",\n            children: feature.title\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 65,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"feature-description\",\n            children: feature.description\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 66,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this);\n};\n_c = FeaturesSection;\nexport default FeaturesSection;\nvar _c;\n$RefreshReg$(_c, \"FeaturesSection\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "FeaturesSection", "features", "icon", "title", "description", "gradient", "id", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "feature", "index", "style", "background", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/ADFD_Tracking_System/src/components/FeaturesSection.tsx"], "sourcesContent": ["import React from 'react';\n\nconst FeaturesSection: React.FC = () => {\n  const features = [\n    {\n      icon: '📄',\n      title: 'AI-Powered OCR Intelligence',\n      description: 'Advanced neural networks achieve 99.9% accuracy in Arabic and English document processing.',\n      gradient: 'var(--accent-gradient)'\n    },\n    {\n      icon: '📍',\n      title: 'Intelligent Regional Routing',\n      description: 'Smart geographic assignment routes requests to specialized regional teams automatically.',\n      gradient: 'var(--success-gradient)'\n    },\n    {\n      icon: '🛡️',\n      title: 'Zero-Exception Security Model',\n      description: 'Military-grade role-based access controls with immutable permissions and zero privilege escalation.',\n      gradient: 'var(--warning-gradient)'\n    },\n    {\n      icon: '📊',\n      title: 'Real-Time Process Intelligence',\n      description: 'Live tracking with predictive analytics and automated bottleneck detection.',\n      gradient: 'linear-gradient(135deg, #8b5cf6, #7c3aed)'\n    },\n    {\n      icon: '⚡',\n      title: 'Intelligent Alert System',\n      description: 'ML-powered priority detection with automated escalation and smart notifications.',\n      gradient: 'linear-gradient(135deg, #ef4444, #dc2626)'\n    },\n    {\n      icon: '🔍',\n      title: 'Advanced Analytics Engine',\n      description: 'Multi-dimensional search, predictive insights, and custom dashboards for data-driven decisions.',\n      gradient: 'linear-gradient(135deg, #06b6d4, #0891b2)'\n    }\n  ];\n\n  return (\n    <section id=\"features\" className=\"features\">\n      <div className=\"container\">\n        <div className=\"section-header animate-on-scroll\">\n          <div className=\"section-badge\">\n            ⚡ Powerful Features\n          </div>\n          <h2 className=\"section-title\">Built for Enterprise Financial Operations</h2>\n          <p className=\"section-description\">\n            Advanced AI capabilities, intelligent automation, and enterprise-grade security designed for the most demanding financial institutions.\n          </p>\n        </div>\n\n        <div className=\"features-grid\">\n          {features.map((feature, index) => (\n            <div key={index} className={`feature-card animate-on-scroll delay-${(index % 4) + 1}`}>\n              <div\n                className=\"feature-icon\"\n                style={{ background: feature.gradient }}\n              >\n                {feature.icon}\n              </div>\n              <h3 className=\"feature-title\">{feature.title}</h3>\n              <p className=\"feature-description\">{feature.description}</p>\n            </div>\n          ))}\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport default FeaturesSection;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EACtC,MAAMC,QAAQ,GAAG,CACf;IACEC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,6BAA6B;IACpCC,WAAW,EAAE,4FAA4F;IACzGC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,8BAA8B;IACrCC,WAAW,EAAE,0FAA0F;IACvGC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,KAAK;IACXC,KAAK,EAAE,+BAA+B;IACtCC,WAAW,EAAE,qGAAqG;IAClHC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,gCAAgC;IACvCC,WAAW,EAAE,6EAA6E;IAC1FC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,GAAG;IACTC,KAAK,EAAE,0BAA0B;IACjCC,WAAW,EAAE,kFAAkF;IAC/FC,QAAQ,EAAE;EACZ,CAAC,EACD;IACEH,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,2BAA2B;IAClCC,WAAW,EAAE,iGAAiG;IAC9GC,QAAQ,EAAE;EACZ,CAAC,CACF;EAED,oBACEN,OAAA;IAASO,EAAE,EAAC,UAAU;IAACC,SAAS,EAAC,UAAU;IAAAC,QAAA,eACzCT,OAAA;MAAKQ,SAAS,EAAC,WAAW;MAAAC,QAAA,gBACxBT,OAAA;QAAKQ,SAAS,EAAC,kCAAkC;QAAAC,QAAA,gBAC/CT,OAAA;UAAKQ,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAE/B;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACNb,OAAA;UAAIQ,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAyC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC5Eb,OAAA;UAAGQ,SAAS,EAAC,qBAAqB;UAAAC,QAAA,EAAC;QAEnC;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENb,OAAA;QAAKQ,SAAS,EAAC,eAAe;QAAAC,QAAA,EAC3BP,QAAQ,CAACY,GAAG,CAAC,CAACC,OAAO,EAAEC,KAAK,kBAC3BhB,OAAA;UAAiBQ,SAAS,EAAE,wCAAyCQ,KAAK,GAAG,CAAC,GAAI,CAAC,EAAG;UAAAP,QAAA,gBACpFT,OAAA;YACEQ,SAAS,EAAC,cAAc;YACxBS,KAAK,EAAE;cAAEC,UAAU,EAAEH,OAAO,CAACT;YAAS,CAAE;YAAAG,QAAA,EAEvCM,OAAO,CAACZ;UAAI;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eACNb,OAAA;YAAIQ,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAEM,OAAO,CAACX;UAAK;YAAAM,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAClDb,OAAA;YAAGQ,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAEM,OAAO,CAACV;UAAW;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA,GARpDG,KAAK;UAAAN,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OASV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEd,CAAC;AAACM,EAAA,GAtEIlB,eAAyB;AAwE/B,eAAeA,eAAe;AAAC,IAAAkB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}