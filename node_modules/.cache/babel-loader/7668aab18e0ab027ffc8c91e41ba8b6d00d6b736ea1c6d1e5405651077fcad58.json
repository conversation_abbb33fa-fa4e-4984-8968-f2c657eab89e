{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/ADFD_Tracking_System/src/components/3D/WorkflowVisualization.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\nimport React, { useRef, useMemo } from 'react';\nimport { useFrame } from '@react-three/fiber';\nimport { Text, Sphere, Line, OrbitControls } from '@react-three/drei';\nimport * as THREE from 'three';\n\n// Workflow Stage Component\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst WorkflowStage = ({\n  position,\n  color,\n  label,\n  active,\n  index\n}) => {\n  _s();\n  const meshRef = useRef(null);\n  const textRef = useRef(null);\n  useFrame(state => {\n    if (meshRef.current) {\n      meshRef.current.rotation.y = state.clock.elapsedTime * 0.5;\n      meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime + index) * 0.1;\n    }\n  });\n  return /*#__PURE__*/_jsxDEV(\"group\", {\n    position: position,\n    children: [/*#__PURE__*/_jsxDEV(Sphere, {\n      ref: meshRef,\n      args: [0.8, 32, 32],\n      children: /*#__PURE__*/_jsxDEV(\"meshStandardMaterial\", {\n        color: color,\n        emissive: active ? color : '#000000',\n        emissiveIntensity: active ? 0.3 : 0,\n        metalness: 0.8,\n        roughness: 0.2\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this), active && /*#__PURE__*/_jsxDEV(Sphere, {\n      args: [1.2, 32, 32],\n      children: /*#__PURE__*/_jsxDEV(\"meshBasicMaterial\", {\n        color: color,\n        transparent: true,\n        opacity: 0.2\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(Text, {\n      ref: textRef,\n      position: [0, -1.5, 0],\n      fontSize: 0.3,\n      color: \"#1e293b\",\n      anchorX: \"center\",\n      anchorY: \"middle\",\n      font: \"/fonts/inter-bold.woff\",\n      children: label\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this);\n};\n\n// Connection Line Component\n_s(WorkflowStage, \"VWmtHyqzMccLFoZW2FWfqf2z0G8=\", false, function () {\n  return [useFrame];\n});\n_c = WorkflowStage;\nconst ConnectionLine = ({\n  start,\n  end,\n  active\n}) => {\n  _s2();\n  const points = useMemo(() => [new THREE.Vector3(...start), new THREE.Vector3(...end)], [start, end]);\n  return /*#__PURE__*/_jsxDEV(Line, {\n    points: points,\n    color: active ? \"#0ea5e9\" : \"#94a3b8\",\n    lineWidth: active ? 4 : 2,\n    transparent: true,\n    opacity: active ? 0.8 : 0.4\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 76,\n    columnNumber: 5\n  }, this);\n};\n\n// Floating Particles Component\n_s2(ConnectionLine, \"G95GpPLhY4BpTJPrFAeCtZA4AeI=\");\n_c2 = ConnectionLine;\nconst FloatingParticles = () => {\n  _s3();\n  const particlesRef = useRef(null);\n  const particles = useMemo(() => {\n    const positions = new Float32Array(100 * 3);\n    for (let i = 0; i < 100; i++) {\n      positions[i * 3] = (Math.random() - 0.5) * 20;\n      positions[i * 3 + 1] = (Math.random() - 0.5) * 20;\n      positions[i * 3 + 2] = (Math.random() - 0.5) * 20;\n    }\n    return positions;\n  }, []);\n  useFrame(state => {\n    if (particlesRef.current) {\n      particlesRef.current.rotation.y = state.clock.elapsedTime * 0.1;\n    }\n  });\n  return /*#__PURE__*/_jsxDEV(\"points\", {\n    ref: particlesRef,\n    children: [/*#__PURE__*/_jsxDEV(\"bufferGeometry\", {\n      children: /*#__PURE__*/_jsxDEV(\"bufferAttribute\", {\n        attach: \"attributes-position\",\n        args: [particles, 3]\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 108,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"pointsMaterial\", {\n      size: 0.05,\n      color: \"#f59e0b\",\n      transparent: true,\n      opacity: 0.6\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 114,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 107,\n    columnNumber: 5\n  }, this);\n};\n\n// Main 3D Scene Component\n_s3(FloatingParticles, \"6U3X4nuEaYm1tAaVFlZXvVGwQm4=\", false, function () {\n  return [useFrame];\n});\n_c3 = FloatingParticles;\nconst WorkflowScene = ({\n  activeStage\n}) => {\n  const stages = [{\n    label: \"Initial\\nReview\",\n    color: \"#f97316\",\n    position: [-6, 0, 0]\n  }, {\n    label: \"Technical\\nReview\",\n    color: \"#eab308\",\n    position: [-2, 0, 0]\n  }, {\n    label: \"Core\\nBanking\",\n    color: \"#22c55e\",\n    position: [2, 0, 0]\n  }, {\n    label: \"Disbursed\",\n    color: \"#06b6d4\",\n    position: [6, 0, 0]\n  }];\n  const connections = [{\n    start: [-6, 0, 0],\n    end: [-2, 0, 0]\n  }, {\n    start: [-2, 0, 0],\n    end: [2, 0, 0]\n  }, {\n    start: [2, 0, 0],\n    end: [6, 0, 0]\n  }];\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"ambientLight\", {\n      intensity: 0.6\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 141,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"pointLight\", {\n      position: [10, 10, 10],\n      intensity: 1\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 142,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"pointLight\", {\n      position: [-10, -10, -10],\n      intensity: 0.5\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 143,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"directionalLight\", {\n      position: [0, 10, 5],\n      intensity: 0.5\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 144,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FloatingParticles, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 146,\n      columnNumber: 7\n    }, this), stages.map((stage, index) => /*#__PURE__*/_jsxDEV(WorkflowStage, {\n      position: stage.position,\n      color: stage.color,\n      label: stage.label,\n      active: index <= activeStage,\n      index: index\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 150,\n      columnNumber: 9\n    }, this)), connections.map((connection, index) => /*#__PURE__*/_jsxDEV(ConnectionLine, {\n      start: connection.start,\n      end: connection.end,\n      active: index < activeStage\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 9\n    }, this)), /*#__PURE__*/_jsxDEV(OrbitControls, {\n      enableZoom: false,\n      enablePan: false,\n      maxPolarAngle: Math.PI / 2,\n      minPolarAngle: Math.PI / 2,\n      autoRotate: true,\n      autoRotateSpeed: 0.5\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 170,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n\n// Animated Fallback Component\n_c4 = WorkflowScene;\nconst WorkflowFallback = () => {\n  _s4();\n  const [activeStage, setActiveStage] = React.useState(0);\n  React.useEffect(() => {\n    const interval = setInterval(() => {\n      setActiveStage(prev => (prev + 1) % 4);\n    }, 2000);\n    return () => clearInterval(interval);\n  }, []);\n  const stages = [{\n    name: 'Request\\nSubmission',\n    color: 'from-blue-500 to-blue-600',\n    icon: '📝'\n  }, {\n    name: 'Technical\\nReview',\n    color: 'from-yellow-500 to-yellow-600',\n    icon: '🔍'\n  }, {\n    name: 'Approval\\nProcess',\n    color: 'from-green-500 to-green-600',\n    icon: '✅'\n  }, {\n    name: 'Fund\\nDisbursement',\n    color: 'from-purple-500 to-purple-600',\n    icon: '💰'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-full h-96 lg:h-[500px] bg-gradient-to-br from-gray-50 to-blue-50 rounded-2xl flex items-center justify-center p-8\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full max-w-4xl\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-8\",\n        children: stages.map((stage, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex flex-col items-center relative\",\n          children: [index < stages.length - 1 && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute top-8 left-full w-full h-1 bg-gray-200 z-0\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `h-full bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-1000 ${activeStage > index ? 'w-full' : 'w-0'}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 210,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 209,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `relative z-10 w-16 h-16 rounded-full bg-gradient-to-r ${stage.color}\n                flex items-center justify-center text-white text-2xl font-bold shadow-lg\n                transform transition-all duration-500 ${activeStage >= index ? 'scale-110 shadow-xl' : 'scale-100'}`,\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-2xl\",\n              children: stage.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 224,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 219,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-3 text-center\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: `text-sm font-semibold whitespace-pre-line transition-colors duration-300 ${activeStage >= index ? 'text-gray-900' : 'text-gray-500'}`,\n              children: stage.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 229,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 228,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 204,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-center space-x-2 mb-4\",\n          children: stages.map((_, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `w-2 h-2 rounded-full transition-all duration-300 ${activeStage >= index ? 'bg-blue-500' : 'bg-gray-300'}`\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 241,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-gray-600 font-medium\",\n          children: \"Real-time Workflow Tracking\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 251,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 240,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 201,\n    columnNumber: 5\n  }, this);\n};\n\n// Main Component - Using animated fallback for reliability\n_s4(WorkflowFallback, \"QvhuEkVAg+XDAXXTDx9NAA7JQIw=\");\n_c5 = WorkflowFallback;\nconst WorkflowVisualization = () => {\n  return /*#__PURE__*/_jsxDEV(WorkflowFallback, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 260,\n    columnNumber: 10\n  }, this);\n};\n_c6 = WorkflowVisualization;\nexport default WorkflowVisualization;\nvar _c, _c2, _c3, _c4, _c5, _c6;\n$RefreshReg$(_c, \"WorkflowStage\");\n$RefreshReg$(_c2, \"ConnectionLine\");\n$RefreshReg$(_c3, \"FloatingParticles\");\n$RefreshReg$(_c4, \"WorkflowScene\");\n$RefreshReg$(_c5, \"WorkflowFallback\");\n$RefreshReg$(_c6, \"WorkflowVisualization\");", "map": {"version": 3, "names": ["React", "useRef", "useMemo", "useFrame", "Text", "Sphere", "Line", "OrbitControls", "THREE", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "WorkflowStage", "position", "color", "label", "active", "index", "_s", "meshRef", "textRef", "state", "current", "rotation", "y", "clock", "elapsedTime", "Math", "sin", "children", "ref", "args", "emissive", "emissiveIntensity", "metalness", "roughness", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "transparent", "opacity", "fontSize", "anchorX", "anchorY", "font", "_c", "ConnectionLine", "start", "end", "_s2", "points", "Vector3", "lineWidth", "_c2", "FloatingParticles", "_s3", "particlesRef", "particles", "positions", "Float32Array", "i", "random", "attach", "size", "_c3", "WorkflowScene", "activeStage", "stages", "connections", "intensity", "map", "stage", "connection", "enableZoom", "enablePan", "maxPolarAngle", "PI", "minPolarAngle", "autoRotate", "autoRotateSpeed", "_c4", "WorkflowFallback", "_s4", "setActiveStage", "useState", "useEffect", "interval", "setInterval", "prev", "clearInterval", "name", "icon", "className", "length", "_", "_c5", "WorkflowVisualization", "_c6", "$RefreshReg$"], "sources": ["/Users/<USER>/ADFD_Tracking_System/src/components/3D/WorkflowVisualization.tsx"], "sourcesContent": ["import React, { useRef, useMemo, Suspense } from 'react';\nimport { Canvas, useFrame } from '@react-three/fiber';\nimport { Text, Sphere, Line, OrbitControls } from '@react-three/drei';\nimport * as THREE from 'three';\n\n// Workflow Stage Component\nconst WorkflowStage: React.FC<{\n  position: [number, number, number];\n  color: string;\n  label: string;\n  active: boolean;\n  index: number;\n}> = ({ position, color, label, active, index }) => {\n  const meshRef = useRef<THREE.Mesh>(null);\n  const textRef = useRef<THREE.Mesh>(null);\n\n  useFrame((state) => {\n    if (meshRef.current) {\n      meshRef.current.rotation.y = state.clock.elapsedTime * 0.5;\n      meshRef.current.position.y = position[1] + Math.sin(state.clock.elapsedTime + index) * 0.1;\n    }\n  });\n\n  return (\n    <group position={position}>\n      {/* Stage Sphere */}\n      <Sphere ref={meshRef} args={[0.8, 32, 32]}>\n        <meshStandardMaterial\n          color={color}\n          emissive={active ? color : '#000000'}\n          emissiveIntensity={active ? 0.3 : 0}\n          metalness={0.8}\n          roughness={0.2}\n        />\n      </Sphere>\n      \n      {/* Glow Effect */}\n      {active && (\n        <Sphere args={[1.2, 32, 32]}>\n          <meshBasicMaterial\n            color={color}\n            transparent\n            opacity={0.2}\n          />\n        </Sphere>\n      )}\n      \n      {/* Stage Label */}\n      <Text\n        ref={textRef}\n        position={[0, -1.5, 0]}\n        fontSize={0.3}\n        color=\"#1e293b\"\n        anchorX=\"center\"\n        anchorY=\"middle\"\n        font=\"/fonts/inter-bold.woff\"\n      >\n        {label}\n      </Text>\n    </group>\n  );\n};\n\n// Connection Line Component\nconst ConnectionLine: React.FC<{\n  start: [number, number, number];\n  end: [number, number, number];\n  active: boolean;\n}> = ({ start, end, active }) => {\n  const points = useMemo(() => [\n    new THREE.Vector3(...start),\n    new THREE.Vector3(...end),\n  ], [start, end]);\n\n  return (\n    <Line\n      points={points}\n      color={active ? \"#0ea5e9\" : \"#94a3b8\"}\n      lineWidth={active ? 4 : 2}\n      transparent\n      opacity={active ? 0.8 : 0.4}\n    />\n  );\n};\n\n// Floating Particles Component\nconst FloatingParticles: React.FC = () => {\n  const particlesRef = useRef<THREE.Points>(null);\n  \n  const particles = useMemo(() => {\n    const positions = new Float32Array(100 * 3);\n    for (let i = 0; i < 100; i++) {\n      positions[i * 3] = (Math.random() - 0.5) * 20;\n      positions[i * 3 + 1] = (Math.random() - 0.5) * 20;\n      positions[i * 3 + 2] = (Math.random() - 0.5) * 20;\n    }\n    return positions;\n  }, []);\n\n  useFrame((state) => {\n    if (particlesRef.current) {\n      particlesRef.current.rotation.y = state.clock.elapsedTime * 0.1;\n    }\n  });\n\n  return (\n    <points ref={particlesRef}>\n      <bufferGeometry>\n        <bufferAttribute\n          attach=\"attributes-position\"\n          args={[particles, 3]}\n        />\n      </bufferGeometry>\n      <pointsMaterial\n        size={0.05}\n        color=\"#f59e0b\"\n        transparent\n        opacity={0.6}\n      />\n    </points>\n  );\n};\n\n// Main 3D Scene Component\nconst WorkflowScene: React.FC<{ activeStage: number }> = ({ activeStage }) => {\n  const stages = [\n    { label: \"Initial\\nReview\", color: \"#f97316\", position: [-6, 0, 0] as [number, number, number] },\n    { label: \"Technical\\nReview\", color: \"#eab308\", position: [-2, 0, 0] as [number, number, number] },\n    { label: \"Core\\nBanking\", color: \"#22c55e\", position: [2, 0, 0] as [number, number, number] },\n    { label: \"Disbursed\", color: \"#06b6d4\", position: [6, 0, 0] as [number, number, number] },\n  ];\n\n  const connections = [\n    { start: [-6, 0, 0] as [number, number, number], end: [-2, 0, 0] as [number, number, number] },\n    { start: [-2, 0, 0] as [number, number, number], end: [2, 0, 0] as [number, number, number] },\n    { start: [2, 0, 0] as [number, number, number], end: [6, 0, 0] as [number, number, number] },\n  ];\n\n  return (\n    <>\n      <ambientLight intensity={0.6} />\n      <pointLight position={[10, 10, 10]} intensity={1} />\n      <pointLight position={[-10, -10, -10]} intensity={0.5} />\n      <directionalLight position={[0, 10, 5]} intensity={0.5} />\n      \n      <FloatingParticles />\n      \n      {/* Workflow Stages */}\n      {stages.map((stage, index) => (\n        <WorkflowStage\n          key={index}\n          position={stage.position}\n          color={stage.color}\n          label={stage.label}\n          active={index <= activeStage}\n          index={index}\n        />\n      ))}\n      \n      {/* Connection Lines */}\n      {connections.map((connection, index) => (\n        <ConnectionLine\n          key={index}\n          start={connection.start}\n          end={connection.end}\n          active={index < activeStage}\n        />\n      ))}\n      \n      <OrbitControls\n        enableZoom={false}\n        enablePan={false}\n        maxPolarAngle={Math.PI / 2}\n        minPolarAngle={Math.PI / 2}\n        autoRotate\n        autoRotateSpeed={0.5}\n      />\n    </>\n  );\n};\n\n// Animated Fallback Component\nconst WorkflowFallback: React.FC = () => {\n  const [activeStage, setActiveStage] = React.useState(0);\n\n  React.useEffect(() => {\n    const interval = setInterval(() => {\n      setActiveStage((prev) => (prev + 1) % 4);\n    }, 2000);\n    return () => clearInterval(interval);\n  }, []);\n\n  const stages = [\n    { name: 'Request\\nSubmission', color: 'from-blue-500 to-blue-600', icon: '📝' },\n    { name: 'Technical\\nReview', color: 'from-yellow-500 to-yellow-600', icon: '🔍' },\n    { name: 'Approval\\nProcess', color: 'from-green-500 to-green-600', icon: '✅' },\n    { name: 'Fund\\nDisbursement', color: 'from-purple-500 to-purple-600', icon: '💰' },\n  ];\n\n  return (\n    <div className=\"w-full h-96 lg:h-[500px] bg-gradient-to-br from-gray-50 to-blue-50 rounded-2xl flex items-center justify-center p-8\">\n      <div className=\"w-full max-w-4xl\">\n        {/* Workflow Steps */}\n        <div className=\"flex items-center justify-between mb-8\">\n          {stages.map((stage, index) => (\n            <div key={index} className=\"flex flex-col items-center relative\">\n              {/* Connection Line */}\n              {index < stages.length - 1 && (\n                <div className=\"absolute top-8 left-full w-full h-1 bg-gray-200 z-0\">\n                  <div\n                    className={`h-full bg-gradient-to-r from-blue-500 to-purple-500 transition-all duration-1000 ${\n                      activeStage > index ? 'w-full' : 'w-0'\n                    }`}\n                  />\n                </div>\n              )}\n\n              {/* Stage Circle */}\n              <div className={`relative z-10 w-16 h-16 rounded-full bg-gradient-to-r ${stage.color}\n                flex items-center justify-center text-white text-2xl font-bold shadow-lg\n                transform transition-all duration-500 ${\n                  activeStage >= index ? 'scale-110 shadow-xl' : 'scale-100'\n                }`}>\n                <span className=\"text-2xl\">{stage.icon}</span>\n              </div>\n\n              {/* Stage Label */}\n              <div className=\"mt-3 text-center\">\n                <p className={`text-sm font-semibold whitespace-pre-line transition-colors duration-300 ${\n                  activeStage >= index ? 'text-gray-900' : 'text-gray-500'\n                }`}>\n                  {stage.name}\n                </p>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Progress Indicator */}\n        <div className=\"text-center\">\n          <div className=\"flex justify-center space-x-2 mb-4\">\n            {stages.map((_, index) => (\n              <div\n                key={index}\n                className={`w-2 h-2 rounded-full transition-all duration-300 ${\n                  activeStage >= index ? 'bg-blue-500' : 'bg-gray-300'\n                }`}\n              />\n            ))}\n          </div>\n          <p className=\"text-gray-600 font-medium\">Real-time Workflow Tracking</p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Main Component - Using animated fallback for reliability\nconst WorkflowVisualization: React.FC = () => {\n  return <WorkflowFallback />;\n};\n\nexport default WorkflowVisualization;\n"], "mappings": ";;;;;AAAA,OAAOA,KAAK,IAAIC,MAAM,EAAEC,OAAO,QAAkB,OAAO;AACxD,SAAiBC,QAAQ,QAAQ,oBAAoB;AACrD,SAASC,IAAI,EAAEC,MAAM,EAAEC,IAAI,EAAEC,aAAa,QAAQ,mBAAmB;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;;AAE9B;AAAA,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AACA,MAAMC,aAMJ,GAAGA,CAAC;EAAEC,QAAQ;EAAEC,KAAK;EAAEC,KAAK;EAAEC,MAAM;EAAEC;AAAM,CAAC,KAAK;EAAAC,EAAA;EAClD,MAAMC,OAAO,GAAGnB,MAAM,CAAa,IAAI,CAAC;EACxC,MAAMoB,OAAO,GAAGpB,MAAM,CAAa,IAAI,CAAC;EAExCE,QAAQ,CAAEmB,KAAK,IAAK;IAClB,IAAIF,OAAO,CAACG,OAAO,EAAE;MACnBH,OAAO,CAACG,OAAO,CAACC,QAAQ,CAACC,CAAC,GAAGH,KAAK,CAACI,KAAK,CAACC,WAAW,GAAG,GAAG;MAC1DP,OAAO,CAACG,OAAO,CAACT,QAAQ,CAACW,CAAC,GAAGX,QAAQ,CAAC,CAAC,CAAC,GAAGc,IAAI,CAACC,GAAG,CAACP,KAAK,CAACI,KAAK,CAACC,WAAW,GAAGT,KAAK,CAAC,GAAG,GAAG;IAC5F;EACF,CAAC,CAAC;EAEF,oBACER,OAAA;IAAOI,QAAQ,EAAEA,QAAS;IAAAgB,QAAA,gBAExBpB,OAAA,CAACL,MAAM;MAAC0B,GAAG,EAAEX,OAAQ;MAACY,IAAI,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAE;MAAAF,QAAA,eACxCpB,OAAA;QACEK,KAAK,EAAEA,KAAM;QACbkB,QAAQ,EAAEhB,MAAM,GAAGF,KAAK,GAAG,SAAU;QACrCmB,iBAAiB,EAAEjB,MAAM,GAAG,GAAG,GAAG,CAAE;QACpCkB,SAAS,EAAE,GAAI;QACfC,SAAS,EAAE;MAAI;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC,EAGRvB,MAAM,iBACLP,OAAA,CAACL,MAAM;MAAC2B,IAAI,EAAE,CAAC,GAAG,EAAE,EAAE,EAAE,EAAE,CAAE;MAAAF,QAAA,eAC1BpB,OAAA;QACEK,KAAK,EAAEA,KAAM;QACb0B,WAAW;QACXC,OAAO,EAAE;MAAI;QAAAL,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACd;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CACT,eAGD9B,OAAA,CAACN,IAAI;MACH2B,GAAG,EAAEV,OAAQ;MACbP,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAE;MACvB6B,QAAQ,EAAE,GAAI;MACd5B,KAAK,EAAC,SAAS;MACf6B,OAAO,EAAC,QAAQ;MAChBC,OAAO,EAAC,QAAQ;MAChBC,IAAI,EAAC,wBAAwB;MAAAhB,QAAA,EAE5Bd;IAAK;MAAAqB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEZ,CAAC;;AAED;AAAArB,EAAA,CAzDMN,aAMJ;EAAA,QAIAV,QAAQ;AAAA;AAAA4C,EAAA,GAVJlC,aAMJ;AAoDF,MAAMmC,cAIJ,GAAGA,CAAC;EAAEC,KAAK;EAAEC,GAAG;EAAEjC;AAAO,CAAC,KAAK;EAAAkC,GAAA;EAC/B,MAAMC,MAAM,GAAGlD,OAAO,CAAC,MAAM,CAC3B,IAAIM,KAAK,CAAC6C,OAAO,CAAC,GAAGJ,KAAK,CAAC,EAC3B,IAAIzC,KAAK,CAAC6C,OAAO,CAAC,GAAGH,GAAG,CAAC,CAC1B,EAAE,CAACD,KAAK,EAAEC,GAAG,CAAC,CAAC;EAEhB,oBACExC,OAAA,CAACJ,IAAI;IACH8C,MAAM,EAAEA,MAAO;IACfrC,KAAK,EAAEE,MAAM,GAAG,SAAS,GAAG,SAAU;IACtCqC,SAAS,EAAErC,MAAM,GAAG,CAAC,GAAG,CAAE;IAC1BwB,WAAW;IACXC,OAAO,EAAEzB,MAAM,GAAG,GAAG,GAAG;EAAI;IAAAoB,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC7B,CAAC;AAEN,CAAC;;AAED;AAAAW,GAAA,CArBMH,cAIJ;AAAAO,GAAA,GAJIP,cAIJ;AAkBF,MAAMQ,iBAA2B,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACxC,MAAMC,YAAY,GAAGzD,MAAM,CAAe,IAAI,CAAC;EAE/C,MAAM0D,SAAS,GAAGzD,OAAO,CAAC,MAAM;IAC9B,MAAM0D,SAAS,GAAG,IAAIC,YAAY,CAAC,GAAG,GAAG,CAAC,CAAC;IAC3C,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,GAAG,EAAEA,CAAC,EAAE,EAAE;MAC5BF,SAAS,CAACE,CAAC,GAAG,CAAC,CAAC,GAAG,CAAClC,IAAI,CAACmC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,EAAE;MAC7CH,SAAS,CAACE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAClC,IAAI,CAACmC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,EAAE;MACjDH,SAAS,CAACE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAClC,IAAI,CAACmC,MAAM,CAAC,CAAC,GAAG,GAAG,IAAI,EAAE;IACnD;IACA,OAAOH,SAAS;EAClB,CAAC,EAAE,EAAE,CAAC;EAENzD,QAAQ,CAAEmB,KAAK,IAAK;IAClB,IAAIoC,YAAY,CAACnC,OAAO,EAAE;MACxBmC,YAAY,CAACnC,OAAO,CAACC,QAAQ,CAACC,CAAC,GAAGH,KAAK,CAACI,KAAK,CAACC,WAAW,GAAG,GAAG;IACjE;EACF,CAAC,CAAC;EAEF,oBACEjB,OAAA;IAAQqB,GAAG,EAAE2B,YAAa;IAAA5B,QAAA,gBACxBpB,OAAA;MAAAoB,QAAA,eACEpB,OAAA;QACEsD,MAAM,EAAC,qBAAqB;QAC5BhC,IAAI,EAAE,CAAC2B,SAAS,EAAE,CAAC;MAAE;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACY,CAAC,eACjB9B,OAAA;MACEuD,IAAI,EAAE,IAAK;MACXlD,KAAK,EAAC,SAAS;MACf0B,WAAW;MACXC,OAAO,EAAE;IAAI;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACI,CAAC;AAEb,CAAC;;AAED;AAAAiB,GAAA,CArCMD,iBAA2B;EAAA,QAa/BrD,QAAQ;AAAA;AAAA+D,GAAA,GAbJV,iBAA2B;AAsCjC,MAAMW,aAAgD,GAAGA,CAAC;EAAEC;AAAY,CAAC,KAAK;EAC5E,MAAMC,MAAM,GAAG,CACb;IAAErD,KAAK,EAAE,iBAAiB;IAAED,KAAK,EAAE,SAAS;IAAED,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;EAA8B,CAAC,EAChG;IAAEE,KAAK,EAAE,mBAAmB;IAAED,KAAK,EAAE,SAAS;IAAED,QAAQ,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;EAA8B,CAAC,EAClG;IAAEE,KAAK,EAAE,eAAe;IAAED,KAAK,EAAE,SAAS;IAAED,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;EAA8B,CAAC,EAC7F;IAAEE,KAAK,EAAE,WAAW;IAAED,KAAK,EAAE,SAAS;IAAED,QAAQ,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;EAA8B,CAAC,CAC1F;EAED,MAAMwD,WAAW,GAAG,CAClB;IAAErB,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAA6B;IAAEC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;EAA8B,CAAC,EAC9F;IAAED,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAA6B;IAAEC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;EAA8B,CAAC,EAC7F;IAAED,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAA6B;IAAEC,GAAG,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;EAA8B,CAAC,CAC7F;EAED,oBACExC,OAAA,CAAAE,SAAA;IAAAkB,QAAA,gBACEpB,OAAA;MAAc6D,SAAS,EAAE;IAAI;MAAAlC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAChC9B,OAAA;MAAYI,QAAQ,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,CAAE;MAACyD,SAAS,EAAE;IAAE;MAAAlC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACpD9B,OAAA;MAAYI,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAE;MAACyD,SAAS,EAAE;IAAI;MAAAlC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACzD9B,OAAA;MAAkBI,QAAQ,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,CAAE;MAACyD,SAAS,EAAE;IAAI;MAAAlC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAE1D9B,OAAA,CAAC8C,iBAAiB;MAAAnB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EAGpB6B,MAAM,CAACG,GAAG,CAAC,CAACC,KAAK,EAAEvD,KAAK,kBACvBR,OAAA,CAACG,aAAa;MAEZC,QAAQ,EAAE2D,KAAK,CAAC3D,QAAS;MACzBC,KAAK,EAAE0D,KAAK,CAAC1D,KAAM;MACnBC,KAAK,EAAEyD,KAAK,CAACzD,KAAM;MACnBC,MAAM,EAAEC,KAAK,IAAIkD,WAAY;MAC7BlD,KAAK,EAAEA;IAAM,GALRA,KAAK;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAMX,CACF,CAAC,EAGD8B,WAAW,CAACE,GAAG,CAAC,CAACE,UAAU,EAAExD,KAAK,kBACjCR,OAAA,CAACsC,cAAc;MAEbC,KAAK,EAAEyB,UAAU,CAACzB,KAAM;MACxBC,GAAG,EAAEwB,UAAU,CAACxB,GAAI;MACpBjC,MAAM,EAAEC,KAAK,GAAGkD;IAAY,GAHvBlD,KAAK;MAAAmB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAIX,CACF,CAAC,eAEF9B,OAAA,CAACH,aAAa;MACZoE,UAAU,EAAE,KAAM;MAClBC,SAAS,EAAE,KAAM;MACjBC,aAAa,EAAEjD,IAAI,CAACkD,EAAE,GAAG,CAAE;MAC3BC,aAAa,EAAEnD,IAAI,CAACkD,EAAE,GAAG,CAAE;MAC3BE,UAAU;MACVC,eAAe,EAAE;IAAI;MAAA5C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtB,CAAC;EAAA,eACF,CAAC;AAEP,CAAC;;AAED;AAAA0C,GAAA,GAzDMf,aAAgD;AA0DtD,MAAMgB,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,GAAA;EACvC,MAAM,CAAChB,WAAW,EAAEiB,cAAc,CAAC,GAAGrF,KAAK,CAACsF,QAAQ,CAAC,CAAC,CAAC;EAEvDtF,KAAK,CAACuF,SAAS,CAAC,MAAM;IACpB,MAAMC,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCJ,cAAc,CAAEK,IAAI,IAAK,CAACA,IAAI,GAAG,CAAC,IAAI,CAAC,CAAC;IAC1C,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMC,aAAa,CAACH,QAAQ,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMnB,MAAM,GAAG,CACb;IAAEuB,IAAI,EAAE,qBAAqB;IAAE7E,KAAK,EAAE,2BAA2B;IAAE8E,IAAI,EAAE;EAAK,CAAC,EAC/E;IAAED,IAAI,EAAE,mBAAmB;IAAE7E,KAAK,EAAE,+BAA+B;IAAE8E,IAAI,EAAE;EAAK,CAAC,EACjF;IAAED,IAAI,EAAE,mBAAmB;IAAE7E,KAAK,EAAE,6BAA6B;IAAE8E,IAAI,EAAE;EAAI,CAAC,EAC9E;IAAED,IAAI,EAAE,oBAAoB;IAAE7E,KAAK,EAAE,+BAA+B;IAAE8E,IAAI,EAAE;EAAK,CAAC,CACnF;EAED,oBACEnF,OAAA;IAAKoF,SAAS,EAAC,qHAAqH;IAAAhE,QAAA,eAClIpB,OAAA;MAAKoF,SAAS,EAAC,kBAAkB;MAAAhE,QAAA,gBAE/BpB,OAAA;QAAKoF,SAAS,EAAC,wCAAwC;QAAAhE,QAAA,EACpDuC,MAAM,CAACG,GAAG,CAAC,CAACC,KAAK,EAAEvD,KAAK,kBACvBR,OAAA;UAAiBoF,SAAS,EAAC,qCAAqC;UAAAhE,QAAA,GAE7DZ,KAAK,GAAGmD,MAAM,CAAC0B,MAAM,GAAG,CAAC,iBACxBrF,OAAA;YAAKoF,SAAS,EAAC,qDAAqD;YAAAhE,QAAA,eAClEpB,OAAA;cACEoF,SAAS,EAAE,oFACT1B,WAAW,GAAGlD,KAAK,GAAG,QAAQ,GAAG,KAAK;YACrC;cAAAmB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CACN,eAGD9B,OAAA;YAAKoF,SAAS,EAAE,yDAAyDrB,KAAK,CAAC1D,KAAK;AAClG;AACA,wDACkBqD,WAAW,IAAIlD,KAAK,GAAG,qBAAqB,GAAG,WAAW,EACzD;YAAAY,QAAA,eACHpB,OAAA;cAAMoF,SAAS,EAAC,UAAU;cAAAhE,QAAA,EAAE2C,KAAK,CAACoB;YAAI;cAAAxD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,eAGN9B,OAAA;YAAKoF,SAAS,EAAC,kBAAkB;YAAAhE,QAAA,eAC/BpB,OAAA;cAAGoF,SAAS,EAAE,4EACZ1B,WAAW,IAAIlD,KAAK,GAAG,eAAe,GAAG,eAAe,EACvD;cAAAY,QAAA,EACA2C,KAAK,CAACmB;YAAI;cAAAvD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC;QAAA,GA5BEtB,KAAK;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA6BV,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGN9B,OAAA;QAAKoF,SAAS,EAAC,aAAa;QAAAhE,QAAA,gBAC1BpB,OAAA;UAAKoF,SAAS,EAAC,oCAAoC;UAAAhE,QAAA,EAChDuC,MAAM,CAACG,GAAG,CAAC,CAACwB,CAAC,EAAE9E,KAAK,kBACnBR,OAAA;YAEEoF,SAAS,EAAE,oDACT1B,WAAW,IAAIlD,KAAK,GAAG,aAAa,GAAG,aAAa;UACnD,GAHEA,KAAK;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAIX,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC,eACN9B,OAAA;UAAGoF,SAAS,EAAC,2BAA2B;UAAAhE,QAAA,EAAC;QAA2B;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;;AAED;AAAA4C,GAAA,CA3EMD,gBAA0B;AAAAc,GAAA,GAA1Bd,gBAA0B;AA4EhC,MAAMe,qBAA+B,GAAGA,CAAA,KAAM;EAC5C,oBAAOxF,OAAA,CAACyE,gBAAgB;IAAA9C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAC7B,CAAC;AAAC2D,GAAA,GAFID,qBAA+B;AAIrC,eAAeA,qBAAqB;AAAC,IAAAnD,EAAA,EAAAQ,GAAA,EAAAW,GAAA,EAAAgB,GAAA,EAAAe,GAAA,EAAAE,GAAA;AAAAC,YAAA,CAAArD,EAAA;AAAAqD,YAAA,CAAA7C,GAAA;AAAA6C,YAAA,CAAAlC,GAAA;AAAAkC,YAAA,CAAAlB,GAAA;AAAAkB,YAAA,CAAAH,GAAA;AAAAG,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}