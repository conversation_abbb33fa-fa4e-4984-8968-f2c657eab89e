{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/ADFD_Tracking_System/src/components/Navigation.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Navigation = () => {\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    className: \"navigation\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"#\",\n          className: \"brand\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"brand-icon\",\n            children: \"\\uD83C\\uDFE2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 9,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"brand-text\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"brand-name\",\n              children: \"Abu Dhabi Fund\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 13,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"brand-tagline\",\n              children: \"Enterprise Platform\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 14,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 12,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 8,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"nav-menu\",\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#platform\",\n              children: \"Platform\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 19,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#features\",\n              children: \"Features\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 20,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#security\",\n              children: \"Security\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 21,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#enterprise\",\n              children: \"Enterprise\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 22,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"nav-actions\",\n          children: [/*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#contact\",\n            className: \"btn-nav secondary\",\n            children: \"Contact Sales\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 26,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n            href: \"#start\",\n            className: \"btn-nav primary\",\n            children: \"Get Started\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 27,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = Navigation;\nexport default Navigation;\nvar _c;\n$RefreshReg$(_c, \"Navigation\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Navigation", "className", "children", "href", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/ADFD_Tracking_System/src/components/Navigation.tsx"], "sourcesContent": ["import React from 'react';\n\nconst Navigation: React.FC = () => {\n  return (\n    <nav className=\"navigation\">\n      <div className=\"container\">\n        <div className=\"nav-container\">\n          <a href=\"#\" className=\"brand\">\n            <div className=\"brand-icon\">\n              🏢\n            </div>\n            <div className=\"brand-text\">\n              <div className=\"brand-name\">Abu Dhabi Fund</div>\n              <div className=\"brand-tagline\">Enterprise Platform</div>\n            </div>\n          </a>\n\n          <ul className=\"nav-menu\">\n            <li><a href=\"#platform\">Platform</a></li>\n            <li><a href=\"#features\">Features</a></li>\n            <li><a href=\"#security\">Security</a></li>\n            <li><a href=\"#enterprise\">Enterprise</a></li>\n          </ul>\n\n          <div className=\"nav-actions\">\n            <a href=\"#contact\" className=\"btn-nav secondary\">Contact Sales</a>\n            <a href=\"#start\" className=\"btn-nav primary\">Get Started</a>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EACjC,oBACED,OAAA;IAAKE,SAAS,EAAC,YAAY;IAAAC,QAAA,eACzBH,OAAA;MAAKE,SAAS,EAAC,WAAW;MAAAC,QAAA,eACxBH,OAAA;QAAKE,SAAS,EAAC,eAAe;QAAAC,QAAA,gBAC5BH,OAAA;UAAGI,IAAI,EAAC,GAAG;UAACF,SAAS,EAAC,OAAO;UAAAC,QAAA,gBAC3BH,OAAA;YAAKE,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAE5B;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACNR,OAAA;YAAKE,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBH,OAAA;cAAKE,SAAS,EAAC,YAAY;cAAAC,QAAA,EAAC;YAAc;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eAChDR,OAAA;cAAKE,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAmB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eAEJR,OAAA;UAAIE,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACtBH,OAAA;YAAAG,QAAA,eAAIH,OAAA;cAAGI,IAAI,EAAC,WAAW;cAAAD,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzCR,OAAA;YAAAG,QAAA,eAAIH,OAAA;cAAGI,IAAI,EAAC,WAAW;cAAAD,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzCR,OAAA;YAAAG,QAAA,eAAIH,OAAA;cAAGI,IAAI,EAAC,WAAW;cAAAD,QAAA,EAAC;YAAQ;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzCR,OAAA;YAAAG,QAAA,eAAIH,OAAA;cAAGI,IAAI,EAAC,aAAa;cAAAD,QAAA,EAAC;YAAU;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3C,CAAC,eAELR,OAAA;UAAKE,SAAS,EAAC,aAAa;UAAAC,QAAA,gBAC1BH,OAAA;YAAGI,IAAI,EAAC,UAAU;YAACF,SAAS,EAAC,mBAAmB;YAAAC,QAAA,EAAC;UAAa;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAClER,OAAA;YAAGI,IAAI,EAAC,QAAQ;YAACF,SAAS,EAAC,iBAAiB;YAAAC,QAAA,EAAC;UAAW;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACC,EAAA,GA9BIR,UAAoB;AAgC1B,eAAeA,UAAU;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}