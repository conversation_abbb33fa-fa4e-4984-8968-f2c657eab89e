{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/ADFD_Tracking_System/src/App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Header from './components/Layout/Header';\nimport Footer from './components/Layout/Footer';\nimport HeroSection from './components/Sections/HeroSection';\nimport FeaturesSection from './components/Sections/FeaturesSection';\nimport SecuritySection from './components/Sections/SecuritySection';\nimport { ScrollProgress, ScrollToTop, FloatingElements } from './components/Effects/ScrollEffects';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  useEffect(() => {\n    // Smooth scrolling for anchor links\n    const handleSmoothScroll = e => {\n      const target = e.target;\n      if (target.hash) {\n        e.preventDefault();\n        const element = document.querySelector(target.hash);\n        if (element) {\n          element.scrollIntoView({\n            behavior: 'smooth',\n            block: 'start'\n          });\n        }\n      }\n    };\n\n    // Add event listeners for smooth scrolling\n    const links = document.querySelectorAll('a[href^=\"#\"]');\n    links.forEach(link => {\n      link.addEventListener('click', handleSmoothScroll);\n    });\n\n    // Cleanup\n    return () => {\n      links.forEach(link => {\n        link.removeEventListener('click', handleSmoothScroll);\n      });\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App relative\",\n    children: [/*#__PURE__*/_jsxDEV(ScrollProgress, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(FloatingElements, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(AnimatePresence, {\n      children: /*#__PURE__*/_jsxDEV(motion.div, {\n        initial: {\n          opacity: 0\n        },\n        animate: {\n          opacity: 1\n        },\n        transition: {\n          duration: 0.5\n        },\n        children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          children: [/*#__PURE__*/_jsxDEV(HeroSection, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FeaturesSection, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 57,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(SecuritySection, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 59,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Footer, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(ScrollToTop, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 48,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 44,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useEffect", "motion", "AnimatePresence", "Header", "Footer", "HeroSection", "FeaturesSection", "SecuritySection", "ScrollProgress", "ScrollToTop", "FloatingElements", "jsxDEV", "_jsxDEV", "App", "_s", "handleSmoothScroll", "e", "target", "hash", "preventDefault", "element", "document", "querySelector", "scrollIntoView", "behavior", "block", "links", "querySelectorAll", "for<PERSON>ach", "link", "addEventListener", "removeEventListener", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "div", "initial", "opacity", "animate", "transition", "duration", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/ADFD_Tracking_System/src/App.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Header from './components/Layout/Header';\nimport Footer from './components/Layout/Footer';\nimport HeroSection from './components/Sections/HeroSection';\nimport FeaturesSection from './components/Sections/FeaturesSection';\n\nimport SecuritySection from './components/Sections/SecuritySection';\n\nimport { ScrollProgress, ScrollToTop, FloatingElements } from './components/Effects/ScrollEffects';\n\nfunction App() {\n  useEffect(() => {\n    // Smooth scrolling for anchor links\n    const handleSmoothScroll = (e: Event) => {\n      const target = e.target as HTMLAnchorElement;\n      if (target.hash) {\n        e.preventDefault();\n        const element = document.querySelector(target.hash);\n        if (element) {\n          element.scrollIntoView({\n            behavior: 'smooth',\n            block: 'start',\n          });\n        }\n      }\n    };\n\n    // Add event listeners for smooth scrolling\n    const links = document.querySelectorAll('a[href^=\"#\"]');\n    links.forEach(link => {\n      link.addEventListener('click', handleSmoothScroll);\n    });\n\n    // Cleanup\n    return () => {\n      links.forEach(link => {\n        link.removeEventListener('click', handleSmoothScroll);\n      });\n    };\n  }, []);\n\n  return (\n    <div className=\"App relative\">\n      <ScrollProgress />\n      <FloatingElements />\n\n      <AnimatePresence>\n        <motion.div\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ duration: 0.5 }}\n        >\n          <Header />\n          <main>\n            <HeroSection />\n            <FeaturesSection />\n\n            <SecuritySection />\n\n          </main>\n          <Footer />\n          <ScrollToTop />\n        </motion.div>\n      </AnimatePresence>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,MAAM,EAAEC,eAAe,QAAQ,eAAe;AACvD,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,MAAM,MAAM,4BAA4B;AAC/C,OAAOC,WAAW,MAAM,mCAAmC;AAC3D,OAAOC,eAAe,MAAM,uCAAuC;AAEnE,OAAOC,eAAe,MAAM,uCAAuC;AAEnE,SAASC,cAAc,EAAEC,WAAW,EAAEC,gBAAgB,QAAQ,oCAAoC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnG,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACbd,SAAS,CAAC,MAAM;IACd;IACA,MAAMe,kBAAkB,GAAIC,CAAQ,IAAK;MACvC,MAAMC,MAAM,GAAGD,CAAC,CAACC,MAA2B;MAC5C,IAAIA,MAAM,CAACC,IAAI,EAAE;QACfF,CAAC,CAACG,cAAc,CAAC,CAAC;QAClB,MAAMC,OAAO,GAAGC,QAAQ,CAACC,aAAa,CAACL,MAAM,CAACC,IAAI,CAAC;QACnD,IAAIE,OAAO,EAAE;UACXA,OAAO,CAACG,cAAc,CAAC;YACrBC,QAAQ,EAAE,QAAQ;YAClBC,KAAK,EAAE;UACT,CAAC,CAAC;QACJ;MACF;IACF,CAAC;;IAED;IACA,MAAMC,KAAK,GAAGL,QAAQ,CAACM,gBAAgB,CAAC,cAAc,CAAC;IACvDD,KAAK,CAACE,OAAO,CAACC,IAAI,IAAI;MACpBA,IAAI,CAACC,gBAAgB,CAAC,OAAO,EAAEf,kBAAkB,CAAC;IACpD,CAAC,CAAC;;IAEF;IACA,OAAO,MAAM;MACXW,KAAK,CAACE,OAAO,CAACC,IAAI,IAAI;QACpBA,IAAI,CAACE,mBAAmB,CAAC,OAAO,EAAEhB,kBAAkB,CAAC;MACvD,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEH,OAAA;IAAKoB,SAAS,EAAC,cAAc;IAAAC,QAAA,gBAC3BrB,OAAA,CAACJ,cAAc;MAAA0B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAClBzB,OAAA,CAACF,gBAAgB;MAAAwB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAEpBzB,OAAA,CAACV,eAAe;MAAA+B,QAAA,eACdrB,OAAA,CAACX,MAAM,CAACqC,GAAG;QACTC,OAAO,EAAE;UAAEC,OAAO,EAAE;QAAE,CAAE;QACxBC,OAAO,EAAE;UAAED,OAAO,EAAE;QAAE,CAAE;QACxBE,UAAU,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE;QAAAV,QAAA,gBAE9BrB,OAAA,CAACT,MAAM;UAAA+B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACVzB,OAAA;UAAAqB,QAAA,gBACErB,OAAA,CAACP,WAAW;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACfzB,OAAA,CAACN,eAAe;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAEnBzB,OAAA,CAACL,eAAe;YAAA2B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAEf,CAAC,eACPzB,OAAA,CAACR,MAAM;UAAA8B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACVzB,OAAA,CAACH,WAAW;UAAAyB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACf,CAAC;AAEV;AAACvB,EAAA,CAxDQD,GAAG;AAAA+B,EAAA,GAAH/B,GAAG;AA0DZ,eAAeA,GAAG;AAAC,IAAA+B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}