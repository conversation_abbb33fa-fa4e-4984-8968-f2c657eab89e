{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/ADFD_Tracking_System/src/components/Navigation.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Navigation = () => {\n  return /*#__PURE__*/_jsxDEV(\"nav\", {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"container\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"nav-container\",\n        children: [/*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"#\",\n          className: \"logo\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"logo-icon\",\n            children: \"\\uD83C\\uDFE2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 9,\n            columnNumber: 13\n          }, this), \"FinTrack System\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 8,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          className: \"nav-links\",\n          children: [/*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#features\",\n              children: \"Features\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 15,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#workflow\",\n              children: \"Workflow\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 16,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#roles\",\n              children: \"User Roles\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 17,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 17,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#technology\",\n              children: \"Technology\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 18,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 18,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(\"a\", {\n              href: \"#demo\",\n              children: \"Demo\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 19,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"a\", {\n          href: \"#demo\",\n          className: \"cta-button\",\n          children: \"Try Demo\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 6,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = Navigation;\nexport default Navigation;\nvar _c;\n$RefreshReg$(_c, \"Navigation\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Navigation", "children", "className", "href", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/ADFD_Tracking_System/src/components/Navigation.tsx"], "sourcesContent": ["import React from 'react';\n\nconst Navigation: React.FC = () => {\n  return (\n    <nav>\n      <div className=\"container\">\n        <div className=\"nav-container\">\n          <a href=\"#\" className=\"logo\">\n            <div className=\"logo-icon\">\n              🏢\n            </div>\n            FinTrack System\n          </a>\n          <ul className=\"nav-links\">\n            <li><a href=\"#features\">Features</a></li>\n            <li><a href=\"#workflow\">Workflow</a></li>\n            <li><a href=\"#roles\">User Roles</a></li>\n            <li><a href=\"#technology\">Technology</a></li>\n            <li><a href=\"#demo\">Demo</a></li>\n          </ul>\n          <a href=\"#demo\" className=\"cta-button\">Try Demo</a>\n        </div>\n      </div>\n    </nav>\n  );\n};\n\nexport default Navigation;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,UAAoB,GAAGA,CAAA,KAAM;EACjC,oBACED,OAAA;IAAAE,QAAA,eACEF,OAAA;MAAKG,SAAS,EAAC,WAAW;MAAAD,QAAA,eACxBF,OAAA;QAAKG,SAAS,EAAC,eAAe;QAAAD,QAAA,gBAC5BF,OAAA;UAAGI,IAAI,EAAC,GAAG;UAACD,SAAS,EAAC,MAAM;UAAAD,QAAA,gBAC1BF,OAAA;YAAKG,SAAS,EAAC,WAAW;YAAAD,QAAA,EAAC;UAE3B;YAAAG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,mBAER;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eACJR,OAAA;UAAIG,SAAS,EAAC,WAAW;UAAAD,QAAA,gBACvBF,OAAA;YAAAE,QAAA,eAAIF,OAAA;cAAGI,IAAI,EAAC,WAAW;cAAAF,QAAA,EAAC;YAAQ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzCR,OAAA;YAAAE,QAAA,eAAIF,OAAA;cAAGI,IAAI,EAAC,WAAW;cAAAF,QAAA,EAAC;YAAQ;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzCR,OAAA;YAAAE,QAAA,eAAIF,OAAA;cAAGI,IAAI,EAAC,QAAQ;cAAAF,QAAA,EAAC;YAAU;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACxCR,OAAA;YAAAE,QAAA,eAAIF,OAAA;cAAGI,IAAI,EAAC,aAAa;cAAAF,QAAA,EAAC;YAAU;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC7CR,OAAA;YAAAE,QAAA,eAAIF,OAAA;cAAGI,IAAI,EAAC,OAAO;cAAAF,QAAA,EAAC;YAAI;cAAAG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/B,CAAC,eACLR,OAAA;UAAGI,IAAI,EAAC,OAAO;UAACD,SAAS,EAAC,YAAY;UAAAD,QAAA,EAAC;QAAQ;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChD;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACC,EAAA,GAvBIR,UAAoB;AAyB1B,eAAeA,UAAU;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}